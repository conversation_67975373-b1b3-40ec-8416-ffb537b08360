# QuantTradeX API配置指南

## 📋 概述

QuantTradeX平台支持多个数据提供商的API集成，为用户提供实时的金融市场数据。本指南将帮助您配置和使用这些API。

## 🔧 支持的数据提供商

### 1. Alpha Vantage (股票和外汇数据)
- **用途**: 股票价格、技术指标、外汇汇率
- **免费额度**: 每分钟5次请求，每天500次请求
- **申请地址**: https://www.alphavantage.co/support/#api-key

#### 配置步骤：
1. 访问 Alpha Vantage 官网
2. 注册账户并获取免费API密钥
3. 在环境变量中设置：
   ```bash
   export ALPHA_VANTAGE_API_KEY="your_api_key_here"
   ```

### 2. CoinGecko (加密货币数据)
- **用途**: 加密货币价格、市值、交易量
- **免费额度**: 每分钟10-50次请求
- **申请地址**: https://www.coingecko.com/en/api

#### 配置步骤：
1. 访问 CoinGecko API 页面
2. 注册Pro账户获取API密钥（可选，免费版本无需密钥）
3. 在环境变量中设置：
   ```bash
   export COINGECKO_API_KEY="your_api_key_here"
   ```

### 3. Yahoo Finance (免费股票数据)
- **用途**: 股票价格、指数数据
- **免费额度**: 每小时2000次请求
- **申请地址**: 无需申请，直接使用

#### 配置步骤：
无需配置，系统默认启用。

### 4. Twelve Data (综合金融数据)
- **用途**: 股票、外汇、加密货币、期货
- **免费额度**: 每分钟8次请求，每天800次请求
- **申请地址**: https://twelvedata.com/

#### 配置步骤：
1. 访问 Twelve Data 官网
2. 注册账户并获取免费API密钥
3. 在环境变量中设置：
   ```bash
   export TWELVE_DATA_API_KEY="your_api_key_here"
   ```

## 💳 支付系统配置

### 1. 支付宝 (Alipay)
- **用途**: 国内用户VIP升级支付
- **申请地址**: https://open.alipay.com/

#### 配置步骤：
1. 注册支付宝开放平台账户
2. 创建应用并获取配置信息
3. 在环境变量中设置：
   ```bash
   export ALIPAY_APP_ID="your_app_id"
   export ALIPAY_PRIVATE_KEY="your_private_key"
   export ALIPAY_PUBLIC_KEY="alipay_public_key"
   ```

### 2. 微信支付 (WeChat Pay)
- **用途**: 国内用户VIP升级支付
- **申请地址**: https://pay.weixin.qq.com/

#### 配置步骤：
1. 注册微信商户平台账户
2. 获取商户号和API密钥
3. 在环境变量中设置：
   ```bash
   export WECHAT_APP_ID="your_app_id"
   export WECHAT_MCH_ID="your_mch_id"
   export WECHAT_API_KEY="your_api_key"
   ```

### 3. PayPal
- **用途**: 国际用户VIP升级支付
- **申请地址**: https://developer.paypal.com/

#### 配置步骤：
1. 注册PayPal开发者账户
2. 创建应用并获取Client ID和Secret
3. 在环境变量中设置：
   ```bash
   export PAYPAL_CLIENT_ID="your_client_id"
   export PAYPAL_CLIENT_SECRET="your_client_secret"
   export PAYPAL_SANDBOX="true"  # 测试环境，生产环境设为false
   ```

### 4. Stripe
- **用途**: 国际信用卡支付
- **申请地址**: https://stripe.com/

#### 配置步骤：
1. 注册Stripe账户
2. 获取API密钥
3. 在环境变量中设置：
   ```bash
   export STRIPE_PUBLIC_KEY="your_public_key"
   export STRIPE_SECRET_KEY="your_secret_key"
   ```

## 🔧 环境变量配置

### 方法1: 使用 .env 文件
在项目根目录创建 `.env` 文件：

```bash
# 数据API配置
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
COINGECKO_API_KEY=your_coingecko_key
TWELVE_DATA_API_KEY=your_twelve_data_key

# 支付API配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
ALIPAY_PUBLIC_KEY=your_alipay_public_key

WECHAT_APP_ID=your_wechat_app_id
WECHAT_MCH_ID=your_wechat_mch_id
WECHAT_API_KEY=your_wechat_api_key

PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_SANDBOX=true

STRIPE_PUBLIC_KEY=your_stripe_public_key
STRIPE_SECRET_KEY=your_stripe_secret_key
```

### 方法2: 系统环境变量
```bash
# 在 ~/.bashrc 或 ~/.zshrc 中添加
export ALPHA_VANTAGE_API_KEY="your_api_key"
export PAYPAL_CLIENT_ID="your_client_id"
# ... 其他配置
```

## 📊 API状态检查

### 查看提供商状态
访问 `/api/data-providers` 端点查看所有数据提供商的状态：

```bash
curl http://localhost:5000/api/data-providers
```

### 测试提供商连接
访问 `/api/data-providers/test/<provider_id>` 测试特定提供商：

```bash
curl http://localhost:5000/api/data-providers/test/alpha_vantage
```

## 🚀 快速开始

### 1. 最小配置（仅使用免费服务）
```bash
# 只需要这一个配置即可开始使用
export TWELVE_DATA_API_KEY="your_twelve_data_free_key"
```

### 2. 推荐配置（数据 + 支付）
```bash
# 数据服务
export ALPHA_VANTAGE_API_KEY="your_alpha_vantage_key"
export TWELVE_DATA_API_KEY="your_twelve_data_key"

# 支付服务（选择一个）
export PAYPAL_CLIENT_ID="your_paypal_client_id"
export PAYPAL_CLIENT_SECRET="your_paypal_client_secret"
```

## ⚠️ 注意事项

### 1. API限制
- 注意各个提供商的请求频率限制
- 免费账户通常有较低的请求限制
- 建议升级到付费计划以获得更高的限制

### 2. 安全性
- 不要在代码中硬编码API密钥
- 使用环境变量或安全的配置管理系统
- 定期轮换API密钥

### 3. 错误处理
- 系统会自动处理API错误和限制
- 当某个提供商不可用时，会自动切换到备用提供商
- 查看日志了解详细的错误信息

## 🔍 故障排除

### 常见问题

1. **API密钥无效**
   - 检查环境变量是否正确设置
   - 确认API密钥没有过期
   - 验证API密钥的权限设置

2. **请求频率超限**
   - 检查API使用情况
   - 考虑升级到付费计划
   - 实施请求缓存机制

3. **支付功能不工作**
   - 确认支付提供商配置正确
   - 检查沙盒/生产环境设置
   - 验证回调URL配置

### 获取帮助
- 查看系统日志：`tail -f logs/app.log`
- 访问API状态页面：`/api/data-providers`
- 联系技术支持：<EMAIL>

## 📈 性能优化

### 1. 缓存策略
- 系统默认缓存数据5分钟
- 可以通过配置调整缓存时间
- 使用Redis提高缓存性能

### 2. 负载均衡
- 配置多个API提供商作为备份
- 系统会自动选择最佳提供商
- 实现故障转移机制

### 3. 监控和告警
- 监控API使用情况
- 设置请求限制告警
- 跟踪API响应时间

---

**更新时间**: 2025年1月27日  
**版本**: v1.0  
**维护者**: QuantTradeX开发团队
