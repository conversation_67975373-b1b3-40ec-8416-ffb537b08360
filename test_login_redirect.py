#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试登录跳转功能的脚本
"""

import requests
import time
import random

def test_login_redirect():
    """测试登录跳转功能"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔄 测试登录跳转功能")
    print("=" * 50)
    
    # 创建会话
    session = requests.Session()
    
    # 1. 测试使用默认管理员账户登录
    print("1. 测试管理员登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        print(f"   登录结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        if result.get('success'):
            user = result.get('user', {})
            print(f"   用户信息: {user.get('username')} ({user.get('full_name')})")
            print(f"   用户角色: {user.get('role')}")
            print(f"   VIP状态: {'✅ VIP' if user.get('is_premium') else '❌ 普通用户'}")
            print(f"   重定向URL: {result.get('redirect_url')}")
        else:
            print(f"   错误: {result.get('error')}")
            return
    except Exception as e:
        print(f"   登录异常: {e}")
        return
    
    # 2. 验证登录状态
    print("\n2. 验证登录状态...")
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        authenticated = result.get('authenticated', False)
        print(f"   认证状态: {'✅ 已认证' if authenticated else '❌ 未认证'}")
        if authenticated:
            user = result.get('user', {})
            print(f"   当前用户: {user.get('username')}")
            print(f"   用户ID: {user.get('id')}")
        else:
            print("   ❌ 登录后认证状态异常！")
            return
    except Exception as e:
        print(f"   验证认证状态异常: {e}")
        return
    
    # 3. 测试访问仪表板页面
    print("\n3. 测试访问仪表板页面...")
    try:
        response = session.get(f"{base_url}/dashboard", timeout=10)
        if response.status_code == 200:
            print("   ✅ 仪表板页面访问成功")
            # 检查页面内容是否包含用户信息
            content = response.text
            if 'QuantTradeX' in content:
                print("   ✅ 页面内容正常")
            else:
                print("   ⚠️ 页面内容可能有问题")
        else:
            print(f"   ❌ 仪表板页面访问失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"   访问仪表板异常: {e}")
    
    # 4. 测试访问用户资料
    print("\n4. 测试访问用户资料...")
    try:
        response = session.get(f"{base_url}/auth/profile", timeout=5)
        result = response.json()
        success = result.get('success', False)
        print(f"   访问结果: {'✅ 成功' if success else '❌ 失败'}")
        if success:
            user = result.get('user', {})
            print(f"   用户资料: {user.get('username')} ({user.get('email')})")
            print(f"   注册时间: {user.get('created_at')}")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   访问用户资料异常: {e}")
    
    # 5. 测试创建新用户并登录
    print("\n5. 测试新用户注册和登录...")
    random_suffix = random.randint(1000, 9999)
    new_username = f"testuser{random_suffix}"
    
    register_data = {
        "username": new_username,
        "email": f"{new_username}@example.com",
        "password": "test123456",
        "full_name": f"测试用户{random_suffix}"
    }
    
    try:
        # 先退出当前用户
        session.post(f"{base_url}/auth/logout", json={}, timeout=5)
        
        # 注册新用户
        response = session.post(f"{base_url}/auth/register", json=register_data, timeout=10)
        result = response.json()
        print(f"   注册结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        if result.get('success'):
            user = result.get('user', {})
            print(f"   新用户: {user.get('username')}")
            print(f"   重定向URL: {result.get('redirect_url')}")
            
            # 验证自动登录
            check_response = session.get(f"{base_url}/auth/check", timeout=5)
            check_result = check_response.json()
            if check_result.get('authenticated'):
                print("   ✅ 注册后自动登录成功")
            else:
                print("   ❌ 注册后自动登录失败")
        else:
            print(f"   注册错误: {result.get('error')}")
    except Exception as e:
        print(f"   注册测试异常: {e}")
    
    # 6. 测试退出登录
    print("\n6. 测试退出登录...")
    try:
        response = session.post(f"{base_url}/auth/logout", json={}, timeout=5)
        result = response.json()
        success = result.get('success', False)
        print(f"   退出结果: {'✅ 成功' if success else '❌ 失败'}")
        if success:
            print(f"   消息: {result.get('message')}")
            
            # 验证退出状态
            check_response = session.get(f"{base_url}/auth/check", timeout=5)
            check_result = check_response.json()
            if not check_result.get('authenticated'):
                print("   ✅ 退出登录验证成功")
            else:
                print("   ❌ 退出登录验证失败")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   退出登录异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 登录跳转测试完成")

if __name__ == "__main__":
    test_login_redirect()
