#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantTradeX 智能数据生成器
使用大模型API生成真实的模拟内容
包括：论坛帖子、策略代码、用户评论等
"""

import json
import random
import time
from datetime import datetime, timedelta
import requests
import os
from typing import List, Dict, Any

class QuantTradeXDataGenerator:
    def __init__(self):
        """初始化数据生成器"""
        self.api_configs = {
            # 可以配置多个API提供商
            'openai': {
                'url': 'https://api.openai.com/v1/chat/completions',
                'headers': {
                    'Authorization': f'Bearer {os.getenv("OPENAI_API_KEY", "")}',
                    'Content-Type': 'application/json'
                },
                'model': 'gpt-3.5-turbo'
            },
            'claude': {
                'url': 'https://api.anthropic.com/v1/messages',
                'headers': {
                    'x-api-key': os.getenv("CLAUDE_API_KEY", ""),
                    'Content-Type': 'application/json',
                    'anthropic-version': '2023-06-01'
                },
                'model': 'claude-3-haiku-20240307'
            },
            'deepseek': {
                'url': 'https://api.deepseek.com/v1/chat/completions',
                'headers': {
                    'Authorization': f'Bearer {os.getenv("DEEPSEEK_API_KEY", "")}',
                    'Content-Type': 'application/json'
                },
                'model': 'deepseek-chat'
            }
        }
        
        # 当前使用的API
        self.current_api = 'deepseek'  # 默认使用DeepSeek
        
        # 数据存储路径
        self.data_dir = 'generated_data'
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 策略类型映射
        self.strategy_types = {
            'moving_average_crossover': '移动平均交叉策略',
            'rsi_mean_reversion': 'RSI均值回归策略',
            'bollinger_bands': '布林带策略',
            'momentum': '动量策略',
            'mean_reversion': '均值回归策略',
            'trend_following': '趋势跟踪策略',
            'arbitrage': '套利策略',
            'volatility': '波动率策略'
        }

    def call_llm_api(self, prompt: str, system_prompt: str = "") -> str:
        """调用大模型API"""
        try:
            config = self.api_configs[self.current_api]
            
            if self.current_api == 'openai' or self.current_api == 'deepseek':
                messages = []
                if system_prompt:
                    messages.append({"role": "system", "content": system_prompt})
                messages.append({"role": "user", "content": prompt})
                
                payload = {
                    "model": config['model'],
                    "messages": messages,
                    "max_tokens": 2000,
                    "temperature": 0.7
                }
                
                response = requests.post(config['url'], headers=config['headers'], json=payload)
                
                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content']
                else:
                    print(f"API调用失败: {response.status_code} - {response.text}")
                    return self._get_fallback_content(prompt)
                    
            elif self.current_api == 'claude':
                payload = {
                    "model": config['model'],
                    "max_tokens": 2000,
                    "messages": [{"role": "user", "content": prompt}]
                }
                if system_prompt:
                    payload["system"] = system_prompt
                
                response = requests.post(config['url'], headers=config['headers'], json=payload)
                
                if response.status_code == 200:
                    result = response.json()
                    return result['content'][0]['text']
                else:
                    print(f"Claude API调用失败: {response.status_code} - {response.text}")
                    return self._get_fallback_content(prompt)
                    
        except Exception as e:
            print(f"API调用异常: {e}")
            return self._get_fallback_content(prompt)

    def _get_fallback_content(self, prompt: str) -> str:
        """API失败时的备用内容"""
        if "论坛帖子" in prompt:
            return "这是一个关于量化交易的讨论帖子，内容正在生成中..."
        elif "策略代码" in prompt:
            return "# 策略代码正在生成中...\npass"
        else:
            return "内容正在生成中..."

    def generate_forum_posts(self, count: int = 20) -> List[Dict]:
        """生成论坛帖子"""
        print(f"开始生成 {count} 个论坛帖子...")
        
        posts = []
        topics = [
            "移动平均策略的优化技巧",
            "如何在震荡市场中使用RSI指标",
            "布林带策略的实战经验分享",
            "量化交易中的风险管理",
            "Python在量化交易中的应用",
            "机器学习在股票预测中的效果",
            "加密货币量化交易策略",
            "期货套利的实操心得",
            "高频交易的技术要求",
            "回测过拟合的避免方法"
        ]
        
        for i in range(count):
            topic = random.choice(topics)
            
            system_prompt = """你是一个专业的量化交易专家，请生成一个真实的论坛帖子。
要求：
1. 内容专业且实用
2. 包含具体的技术细节
3. 语言自然，像真实用户发帖
4. 长度适中（200-500字）
5. 可以包含一些个人经验和观点"""

            prompt = f"""请生成一个关于"{topic}"的量化交易论坛帖子。
帖子应该包含：
- 引人入胜的标题
- 详细的内容描述
- 一些技术要点
- 个人经验分享
- 适当的专业术语

请以JSON格式返回，包含title和content字段。"""

            try:
                response = self.call_llm_api(prompt, system_prompt)
                
                # 尝试解析JSON，如果失败则手动构造
                try:
                    post_data = json.loads(response)
                except:
                    post_data = {
                        "title": topic,
                        "content": response
                    }
                
                post = {
                    "id": i + 1,
                    "title": post_data.get("title", topic),
                    "content": post_data.get("content", response),
                    "author": f"trader_{random.randint(1000, 9999)}",
                    "created_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                    "views": random.randint(50, 2000),
                    "replies": random.randint(0, 50),
                    "likes": random.randint(0, 100),
                    "category": random.choice(["策略讨论", "技术分析", "市场观点", "经验分享", "工具推荐"])
                }
                
                posts.append(post)
                print(f"已生成帖子 {i+1}/{count}: {post['title']}")
                
                # 避免API限流
                time.sleep(1)
                
            except Exception as e:
                print(f"生成帖子 {i+1} 失败: {e}")
                continue
        
        # 保存到文件
        with open(f"{self.data_dir}/forum_posts.json", "w", encoding="utf-8") as f:
            json.dump(posts, f, ensure_ascii=False, indent=2)
        
        print(f"论坛帖子生成完成，共 {len(posts)} 个")
        return posts

    def generate_strategy_codes(self) -> Dict[str, str]:
        """生成策略代码"""
        print("开始生成策略代码...")
        
        strategy_codes = {}
        
        for strategy_type, strategy_name in self.strategy_types.items():
            system_prompt = f"""你是一个专业的量化交易策略开发者，请生成一个完整的{strategy_name}的Python代码。
要求：
1. 代码结构清晰，注释详细
2. 包含策略逻辑、信号生成、风险管理
3. 使用pandas、numpy等常用库
4. 代码可以实际运行
5. 包含参数配置和回测框架"""

            prompt = f"""请生成一个完整的{strategy_name}Python代码实现。
代码应该包含：
- 导入必要的库
- 策略类定义
- 信号生成逻辑
- 风险管理
- 回测框架
- 详细注释

请生成专业、可运行的代码。"""

            try:
                code = self.call_llm_api(prompt, system_prompt)
                strategy_codes[strategy_type] = code
                print(f"已生成策略代码: {strategy_name}")
                time.sleep(1)
                
            except Exception as e:
                print(f"生成策略代码失败 {strategy_name}: {e}")
                strategy_codes[strategy_type] = f"# {strategy_name}\n# 代码生成中...\npass"
        
        # 保存到文件
        with open(f"{self.data_dir}/strategy_codes.json", "w", encoding="utf-8") as f:
            json.dump(strategy_codes, f, ensure_ascii=False, indent=2)
        
        print("策略代码生成完成")
        return strategy_codes

    def generate_user_comments(self, count: int = 50) -> List[Dict]:
        """生成用户评论"""
        print(f"开始生成 {count} 个用户评论...")
        
        comments = []
        comment_types = [
            "策略评价",
            "使用心得",
            "改进建议",
            "问题咨询",
            "经验分享"
        ]
        
        for i in range(count):
            comment_type = random.choice(comment_types)
            
            system_prompt = """你是一个量化交易用户，请生成一个真实的评论。
要求：
1. 语言自然，像真实用户评论
2. 内容有价值，不是水贴
3. 长度适中（50-200字）
4. 可以包含一些专业术语"""

            prompt = f"""请生成一个关于量化交易的{comment_type}评论。
评论应该：
- 语言自然
- 内容有价值
- 表达个人观点
- 适当使用专业术语"""

            try:
                content = self.call_llm_api(prompt, system_prompt)
                
                comment = {
                    "id": i + 1,
                    "content": content,
                    "author": f"user_{random.randint(1000, 9999)}",
                    "created_at": (datetime.now() - timedelta(hours=random.randint(1, 720))).isoformat(),
                    "likes": random.randint(0, 20),
                    "type": comment_type
                }
                
                comments.append(comment)
                print(f"已生成评论 {i+1}/{count}")
                time.sleep(0.5)
                
            except Exception as e:
                print(f"生成评论 {i+1} 失败: {e}")
                continue
        
        # 保存到文件
        with open(f"{self.data_dir}/user_comments.json", "w", encoding="utf-8") as f:
            json.dump(comments, f, ensure_ascii=False, indent=2)
        
        print(f"用户评论生成完成，共 {len(comments)} 个")
        return comments

    def run_full_generation(self):
        """运行完整的数据生成流程"""
        print("=" * 50)
        print("QuantTradeX 智能数据生成器启动")
        print("=" * 50)
        
        # 检查API配置
        if not self._check_api_config():
            print("警告：未配置API密钥，将使用备用内容")
        
        # 生成论坛帖子
        self.generate_forum_posts(20)
        
        # 生成策略代码
        self.generate_strategy_codes()
        
        # 生成用户评论
        self.generate_user_comments(50)
        
        print("=" * 50)
        print("数据生成完成！")
        print(f"生成的文件保存在: {self.data_dir}/")
        print("=" * 50)

    def _check_api_config(self) -> bool:
        """检查API配置"""
        config = self.api_configs[self.current_api]
        if self.current_api == 'openai':
            return bool(os.getenv("OPENAI_API_KEY"))
        elif self.current_api == 'claude':
            return bool(os.getenv("CLAUDE_API_KEY"))
        elif self.current_api == 'deepseek':
            return bool(os.getenv("DEEPSEEK_API_KEY"))
        return False

if __name__ == "__main__":
    # 创建生成器实例
    generator = QuantTradeXDataGenerator()
    
    # 运行完整生成流程
    generator.run_full_generation()
