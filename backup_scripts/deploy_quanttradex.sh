#!/bin/bash
# QuantTradeX 一键部署脚本
# 作者: QuantTradeX Team
# 版本: v1.0
# 创建时间: 2025-01-27

# ===========================================
# 配置区域
# ===========================================

# 基础配置
PROJECT_NAME="QuantTradeX"
DOMAIN_NAME="qclb.com"
PROJECT_DIR="/www/wwwroot/qclb.com"
BACKUP_DIR="/backup"
LOG_DIR="/var/log/quanttradex"

# 服务配置
FLASK_PORT=5000
NGINX_PORT=80
SSL_PORT=443

# 用户配置
WEB_USER="www-data"
WEB_GROUP="www-data"

# Python配置
PYTHON_VERSION="3.8"
VENV_NAME="venv"

# ===========================================
# 颜色定义
# ===========================================

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ===========================================
# 函数定义
# ===========================================

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%H:%M:%S')] ${message}${NC}"
}

# 成功消息
success() {
    print_message $GREEN "✅ $1"
}

# 信息消息
info() {
    print_message $BLUE "ℹ️  $1"
}

# 警告消息
warning() {
    print_message $YELLOW "⚠️  $1"
}

# 错误消息
error() {
    print_message $RED "❌ $1"
}

# 步骤消息
step() {
    print_message $PURPLE "🔄 $1"
}

# 错误退出
error_exit() {
    error "$1"
    exit 1
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" >/dev/null 2>&1; then
        error_exit "命令 '$1' 未找到，请先安装"
    fi
}

# 检查系统要求
check_system_requirements() {
    step "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        error_exit "不支持的操作系统"
    fi
    
    local os_name=$(grep ^NAME /etc/os-release | cut -d'"' -f2)
    info "操作系统: $os_name"
    
    # 检查内存
    local memory_gb=$(free -g | awk 'NR==2{print $2}')
    if [[ $memory_gb -lt 2 ]]; then
        warning "内存不足2GB，可能影响性能"
    else
        success "内存检查通过: ${memory_gb}GB"
    fi
    
    # 检查磁盘空间
    local disk_available=$(df / | awk 'NR==2{print $4}')
    local disk_available_gb=$((disk_available / 1024 / 1024))
    if [[ $disk_available_gb -lt 10 ]]; then
        error_exit "磁盘空间不足10GB"
    else
        success "磁盘空间检查通过: ${disk_available_gb}GB可用"
    fi
    
    success "系统要求检查完成"
}

# 检测包管理器
detect_package_manager() {
    if command -v apt-get >/dev/null 2>&1; then
        echo "apt"
    elif command -v yum >/dev/null 2>&1; then
        echo "yum"
    elif command -v dnf >/dev/null 2>&1; then
        echo "dnf"
    else
        error_exit "不支持的包管理器"
    fi
}

# 安装基础软件包
install_base_packages() {
    step "安装基础软件包..."
    
    local pkg_manager=$(detect_package_manager)
    
    case $pkg_manager in
        "apt")
            apt-get update
            apt-get install -y python3 python3-pip python3-venv nginx git curl wget unzip bc
            ;;
        "yum"|"dnf")
            $pkg_manager update -y
            $pkg_manager install -y python3 python3-pip nginx git curl wget unzip bc
            ;;
    esac
    
    success "基础软件包安装完成"
}

# 创建目录结构
create_directories() {
    step "创建目录结构..."
    
    # 创建主要目录
    mkdir -p "$PROJECT_DIR"
    mkdir -p "$BACKUP_DIR"/{scripts,quanttradex}
    mkdir -p "$LOG_DIR"
    mkdir -p /etc/nginx/sites-available
    mkdir -p /etc/nginx/sites-enabled
    
    # 设置权限
    chown -R $WEB_USER:$WEB_GROUP "$PROJECT_DIR"
    chown -R $WEB_USER:$WEB_GROUP "$LOG_DIR"
    
    success "目录结构创建完成"
}

# 从备份恢复项目文件
restore_from_backup() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        warning "未指定备份文件，将创建示例项目"
        create_sample_project
        return
    fi
    
    if [[ ! -f "$backup_file" ]]; then
        error_exit "备份文件不存在: $backup_file"
    fi
    
    step "从备份恢复项目文件..."
    
    # 解压备份文件
    local temp_dir="/tmp/quanttradex_restore_$$"
    mkdir -p "$temp_dir"
    
    if [[ "$backup_file" == *.tar.gz ]]; then
        tar -xzf "$backup_file" -C "$temp_dir"
    elif [[ "$backup_file" == *.zip ]]; then
        unzip -q "$backup_file" -d "$temp_dir"
    else
        error_exit "不支持的备份文件格式"
    fi
    
    # 查找项目目录
    local project_backup_dir=$(find "$temp_dir" -name "project" -type d | head -1)
    if [[ -n "$project_backup_dir" ]]; then
        cp -r "$project_backup_dir"/* "$PROJECT_DIR/"
    else
        # 直接复制所有内容
        cp -r "$temp_dir"/* "$PROJECT_DIR/"
    fi
    
    # 清理临时目录
    rm -rf "$temp_dir"
    
    # 设置权限
    chown -R $WEB_USER:$WEB_GROUP "$PROJECT_DIR"
    chmod -R 755 "$PROJECT_DIR"
    
    success "项目文件恢复完成"
}

# 创建示例项目
create_sample_project() {
    step "创建示例项目..."
    
    # 创建基本的Flask应用
    cat > "$PROJECT_DIR/app.py" << 'EOF'
from flask import Flask, render_template, jsonify

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/system/status')
def system_status():
    return jsonify({
        'status': 'ok',
        'message': 'QuantTradeX is running',
        'version': '1.0.0'
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
EOF

    # 创建模板目录和基本HTML
    mkdir -p "$PROJECT_DIR/templates"
    cat > "$PROJECT_DIR/templates/index.html" << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantTradeX - 量化交易平台</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .status { text-align: center; margin: 20px 0; }
        .success { color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 QuantTradeX 部署成功！</h1>
        <div class="status">
            <p class="success">✅ 系统正在运行</p>
            <p>欢迎使用 QuantTradeX 量化交易平台</p>
        </div>
    </div>
</body>
</html>
EOF

    success "示例项目创建完成"
}

# 配置Python环境
setup_python_environment() {
    step "配置Python环境..."
    
    cd "$PROJECT_DIR"
    
    # 创建虚拟环境
    python3 -m venv "$VENV_NAME"
    
    # 激活虚拟环境并安装依赖
    source "$VENV_NAME/bin/activate"
    pip install --upgrade pip
    pip install flask gunicorn
    
    # 如果存在requirements.txt，安装依赖
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
    fi
    
    deactivate
    
    success "Python环境配置完成"
}

# 配置Nginx
configure_nginx() {
    step "配置Nginx..."
    
    # 创建Nginx配置文件
    cat > "/etc/nginx/sites-available/$DOMAIN_NAME" << EOF
server {
    listen $NGINX_PORT;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    
    root $PROJECT_DIR;
    index index.html;
    
    # 静态文件
    location /static/ {
        alias $PROJECT_DIR/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # API请求转发
    location /api/ {
        proxy_pass http://127.0.0.1:$FLASK_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 认证请求转发
    location /auth/ {
        proxy_pass http://127.0.0.1:$FLASK_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Flask路由转发
    location ~ ^/(dashboard|strategies|backtest|forum|strategy-editor)$ {
        proxy_pass http://127.0.0.1:$FLASK_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 默认处理
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # 日志
    access_log /var/log/nginx/$DOMAIN_NAME.access.log;
    error_log /var/log/nginx/$DOMAIN_NAME.error.log;
}
EOF

    # 启用站点
    ln -sf "/etc/nginx/sites-available/$DOMAIN_NAME" "/etc/nginx/sites-enabled/"
    
    # 删除默认站点
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    if nginx -t; then
        success "Nginx配置验证通过"
    else
        error_exit "Nginx配置验证失败"
    fi
    
    success "Nginx配置完成"
}

# 创建系统服务
create_systemd_service() {
    step "创建系统服务..."
    
    cat > "/etc/systemd/system/quanttradex.service" << EOF
[Unit]
Description=QuantTradeX Flask Application
After=network.target

[Service]
Type=simple
User=$WEB_USER
Group=$WEB_GROUP
WorkingDirectory=$PROJECT_DIR
Environment=PATH=$PROJECT_DIR/$VENV_NAME/bin
ExecStart=$PROJECT_DIR/$VENV_NAME/bin/gunicorn --bind 127.0.0.1:$FLASK_PORT --workers 4 app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

    # 重载systemd配置
    systemctl daemon-reload
    
    success "系统服务创建完成"
}

# 安装监控脚本
install_monitoring() {
    step "安装监控脚本..."
    
    # 复制监控脚本
    if [[ -f "backup_scripts/monitor_quanttradex.sh" ]]; then
        cp backup_scripts/monitor_quanttradex.sh "$BACKUP_DIR/scripts/"
        chmod +x "$BACKUP_DIR/scripts/monitor_quanttradex.sh"
    fi
    
    # 复制备份脚本
    if [[ -f "backup_scripts/backup_quanttradex.sh" ]]; then
        cp backup_scripts/backup_quanttradex.sh "$BACKUP_DIR/scripts/"
        chmod +x "$BACKUP_DIR/scripts/backup_quanttradex.sh"
    fi
    
    success "监控脚本安装完成"
}

# 配置防火墙
configure_firewall() {
    step "配置防火墙..."
    
    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu UFW
        ufw --force enable
        ufw allow ssh
        ufw allow http
        ufw allow https
        success "UFW防火墙配置完成"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        # CentOS/RHEL firewalld
        systemctl enable firewalld
        systemctl start firewalld
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
        success "firewalld防火墙配置完成"
    else
        warning "未找到防火墙管理工具，请手动配置"
    fi
}

# 启动服务
start_services() {
    step "启动服务..."
    
    # 启动并启用QuantTradeX服务
    systemctl enable quanttradex
    systemctl start quanttradex
    
    # 启动并启用Nginx
    systemctl enable nginx
    systemctl restart nginx
    
    # 检查服务状态
    if systemctl is-active --quiet quanttradex; then
        success "QuantTradeX服务启动成功"
    else
        error "QuantTradeX服务启动失败"
        systemctl status quanttradex
    fi
    
    if systemctl is-active --quiet nginx; then
        success "Nginx服务启动成功"
    else
        error "Nginx服务启动失败"
        systemctl status nginx
    fi
}

# 验证部署
verify_deployment() {
    step "验证部署..."
    
    # 等待服务启动
    sleep 5
    
    # 检查端口监听
    if netstat -tuln | grep -q ":$NGINX_PORT "; then
        success "Nginx端口监听正常"
    else
        error "Nginx端口监听异常"
    fi
    
    if netstat -tuln | grep -q ":$FLASK_PORT "; then
        success "Flask端口监听正常"
    else
        error "Flask端口监听异常"
    fi
    
    # 检查HTTP响应
    local http_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/ || echo "000")
    if [[ "$http_status" == "200" ]]; then
        success "HTTP响应正常"
    else
        warning "HTTP响应异常，状态码: $http_status"
    fi
    
    # 检查API响应
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/api/system/status || echo "000")
    if [[ "$api_status" == "200" ]]; then
        success "API响应正常"
    else
        warning "API响应异常，状态码: $api_status"
    fi
}

# 显示部署结果
show_deployment_result() {
    echo
    print_message $GREEN "🎉 QuantTradeX 部署完成！"
    echo
    info "访问信息:"
    info "  网站地址: http://$DOMAIN_NAME"
    info "  本地访问: http://localhost"
    info "  API状态: http://localhost/api/system/status"
    echo
    info "服务管理:"
    info "  启动服务: systemctl start quanttradex"
    info "  停止服务: systemctl stop quanttradex"
    info "  重启服务: systemctl restart quanttradex"
    info "  查看状态: systemctl status quanttradex"
    echo
    info "日志查看:"
    info "  应用日志: journalctl -u quanttradex -f"
    info "  Nginx日志: tail -f /var/log/nginx/$DOMAIN_NAME.access.log"
    info "  错误日志: tail -f /var/log/nginx/$DOMAIN_NAME.error.log"
    echo
    info "文件位置:"
    info "  项目目录: $PROJECT_DIR"
    info "  备份目录: $BACKUP_DIR"
    info "  日志目录: $LOG_DIR"
    echo
    warning "下一步:"
    warning "  1. 配置域名DNS解析"
    warning "  2. 申请SSL证书（可选）"
    warning "  3. 配置定时备份"
    warning "  4. 设置监控告警"
    echo
}

# ===========================================
# 主函数
# ===========================================

main() {
    local backup_file="$1"
    
    echo
    print_message $CYAN "🚀 开始部署 $PROJECT_NAME"
    echo
    
    # 检查是否以root权限运行
    if [[ $EUID -ne 0 ]]; then
        error_exit "请以root权限运行此脚本"
    fi
    
    # 执行部署步骤
    check_system_requirements
    install_base_packages
    create_directories
    restore_from_backup "$backup_file"
    setup_python_environment
    configure_nginx
    create_systemd_service
    install_monitoring
    configure_firewall
    start_services
    verify_deployment
    show_deployment_result
    
    success "部署脚本执行完成！"
}

# ===========================================
# 脚本入口
# ===========================================

# 显示帮助信息
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    echo "QuantTradeX 一键部署脚本"
    echo
    echo "用法: $0 [备份文件]"
    echo
    echo "参数:"
    echo "  备份文件    可选，指定要恢复的备份文件路径"
    echo
    echo "示例:"
    echo "  $0                                    # 创建新的示例项目"
    echo "  $0 /path/to/backup.tar.gz            # 从备份文件恢复"
    echo
    exit 0
fi

# 设置错误处理
set -e
trap 'error "部署过程中发生错误，请检查日志"' ERR

# 执行主函数
main "$@"
