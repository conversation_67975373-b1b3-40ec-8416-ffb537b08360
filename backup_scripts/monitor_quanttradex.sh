#!/bin/bash
# QuantTradeX 系统监控脚本
# 作者: QuantTradeX Team
# 版本: v1.0
# 创建时间: 2025-01-27

# ===========================================
# 配置区域
# ===========================================

# 基础配置
LOG_DIR="/var/log/quanttradex"
MONITOR_LOG="${LOG_DIR}/monitor.log"
ALERT_LOG="${LOG_DIR}/alerts.log"
PERFORMANCE_LOG="${LOG_DIR}/performance.log"

# 阈值配置
CPU_THRESHOLD=80          # CPU使用率阈值(%)
MEMORY_THRESHOLD=85       # 内存使用率阈值(%)
DISK_THRESHOLD=80         # 磁盘使用率阈值(%)
LOAD_THRESHOLD=4.0        # 系统负载阈值
CONNECTION_THRESHOLD=1000 # 连接数阈值

# 服务配置
SERVICES=("quanttradex" "nginx")
PORTS=("5000" "80" "443")

# 通知配置
EMAIL_ENABLED=false
EMAIL_TO="<EMAIL>"
WEBHOOK_ENABLED=false
WEBHOOK_URL=""

# ===========================================
# 函数定义
# ===========================================

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$MONITOR_LOG"
}

# 警报函数
alert() {
    local level="$1"
    local message="$2"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $message" | tee -a "$ALERT_LOG"
    
    # 发送通知
    send_notification "QuantTradeX Alert: $level" "$message"
}

# 性能日志函数
perf_log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$PERFORMANCE_LOG"
}

# 发送通知函数
send_notification() {
    local title="$1"
    local message="$2"
    
    # 邮件通知
    if [ "$EMAIL_ENABLED" = true ] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "$title" "$EMAIL_TO"
    fi
    
    # Webhook通知
    if [ "$WEBHOOK_ENABLED" = true ] && [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
             -H "Content-Type: application/json" \
             -d "{\"title\":\"$title\",\"message\":\"$message\",\"timestamp\":\"$(date -Iseconds)\"}" \
             >/dev/null 2>&1
    fi
}

# 检查服务状态
check_services() {
    log "🔍 检查服务状态..."
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log "✅ $service 服务运行正常"
        else
            alert "CRITICAL" "$service 服务已停止，正在尝试重启..."
            
            # 尝试重启服务
            if systemctl restart "$service"; then
                alert "INFO" "$service 服务重启成功"
            else
                alert "CRITICAL" "$service 服务重启失败，需要人工干预"
            fi
        fi
    done
}

# 检查端口状态
check_ports() {
    log "🔍 检查端口状态..."
    
    for port in "${PORTS[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            log "✅ 端口 $port 正常监听"
        else
            alert "WARNING" "端口 $port 未在监听状态"
        fi
    done
}

# 检查CPU使用率
check_cpu() {
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | cut -d',' -f1)
    cpu_usage=${cpu_usage%.*}  # 去除小数部分
    
    perf_log "CPU_USAGE:$cpu_usage%"
    
    if [ "$cpu_usage" -gt "$CPU_THRESHOLD" ]; then
        alert "WARNING" "CPU使用率过高: ${cpu_usage}% (阈值: ${CPU_THRESHOLD}%)"
        
        # 记录CPU占用最高的进程
        log "CPU占用最高的进程:"
        ps aux --sort=-%cpu | head -6 | tee -a "$MONITOR_LOG"
    else
        log "✅ CPU使用率正常: ${cpu_usage}%"
    fi
}

# 检查内存使用率
check_memory() {
    local memory_info=$(free | awk 'NR==2{printf "%.0f %.0f %.0f", $3*100/$2, $3/1024/1024, $2/1024/1024}')
    local memory_usage=$(echo $memory_info | cut -d' ' -f1)
    local used_gb=$(echo $memory_info | cut -d' ' -f2)
    local total_gb=$(echo $memory_info | cut -d' ' -f3)
    
    perf_log "MEMORY_USAGE:${memory_usage}% (${used_gb}GB/${total_gb}GB)"
    
    if [ "$memory_usage" -gt "$MEMORY_THRESHOLD" ]; then
        alert "WARNING" "内存使用率过高: ${memory_usage}% (${used_gb}GB/${total_gb}GB, 阈值: ${MEMORY_THRESHOLD}%)"
        
        # 记录内存占用最高的进程
        log "内存占用最高的进程:"
        ps aux --sort=-%mem | head -6 | tee -a "$MONITOR_LOG"
    else
        log "✅ 内存使用率正常: ${memory_usage}% (${used_gb}GB/${total_gb}GB)"
    fi
}

# 检查磁盘使用率
check_disk() {
    log "🔍 检查磁盘使用率..."
    
    # 检查根分区
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    perf_log "DISK_USAGE_ROOT:${disk_usage}%"
    
    if [ "$disk_usage" -gt "$DISK_THRESHOLD" ]; then
        alert "WARNING" "根分区磁盘使用率过高: ${disk_usage}% (阈值: ${DISK_THRESHOLD}%)"
        
        # 显示磁盘使用详情
        log "磁盘使用详情:"
        df -h | tee -a "$MONITOR_LOG"
        
        # 查找大文件
        log "查找大于100MB的文件:"
        find /var/log -type f -size +100M -exec ls -lh {} \; 2>/dev/null | head -5 | tee -a "$MONITOR_LOG"
        
        # 自动清理
        cleanup_disk
    else
        log "✅ 磁盘使用率正常: ${disk_usage}%"
    fi
    
    # 检查其他挂载点
    while read -r line; do
        local mount_point=$(echo "$line" | awk '{print $6}')
        local usage=$(echo "$line" | awk '{print $5}' | sed 's/%//')
        
        if [ "$usage" -gt "$DISK_THRESHOLD" ] && [ "$mount_point" != "/" ]; then
            alert "WARNING" "挂载点 $mount_point 磁盘使用率过高: ${usage}%"
        fi
    done < <(df | grep -E '^/dev/')
}

# 检查系统负载
check_load() {
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local load_1min=$(echo "$load_avg" | cut -d'.' -f1)
    
    perf_log "LOAD_AVERAGE:$load_avg"
    
    if (( $(echo "$load_avg > $LOAD_THRESHOLD" | bc -l) )); then
        alert "WARNING" "系统负载过高: $load_avg (阈值: $LOAD_THRESHOLD)"
        
        # 显示当前运行的进程
        log "当前运行进程数: $(ps aux | wc -l)"
        log "负载最高的进程:"
        ps aux --sort=-%cpu | head -6 | tee -a "$MONITOR_LOG"
    else
        log "✅ 系统负载正常: $load_avg"
    fi
}

# 检查网络连接
check_connections() {
    local total_connections=$(netstat -an | grep -E ":(80|443|5000)" | wc -l)
    local established_connections=$(netstat -an | grep ESTABLISHED | wc -l)
    
    perf_log "CONNECTIONS:total=$total_connections,established=$established_connections"
    
    if [ "$total_connections" -gt "$CONNECTION_THRESHOLD" ]; then
        alert "WARNING" "网络连接数过高: $total_connections (阈值: $CONNECTION_THRESHOLD)"
        
        # 显示连接统计
        log "连接状态统计:"
        netstat -an | awk '/^tcp/ {print $6}' | sort | uniq -c | sort -nr | tee -a "$MONITOR_LOG"
    else
        log "✅ 网络连接正常: 总连接数=$total_connections, 已建立=$established_connections"
    fi
}

# 检查应用健康状态
check_app_health() {
    log "🔍 检查应用健康状态..."
    
    # 检查主页是否可访问
    if curl -s -o /dev/null -w "%{http_code}" http://localhost/ | grep -q "200"; then
        log "✅ 主页访问正常"
    else
        alert "CRITICAL" "主页无法访问，HTTP状态码异常"
    fi
    
    # 检查API是否响应
    if curl -s -o /dev/null -w "%{http_code}" http://localhost/api/system/status | grep -q "200"; then
        log "✅ API接口响应正常"
    else
        alert "WARNING" "API接口响应异常"
    fi
    
    # 检查Flask应用日志
    if [ -f "/var/log/quanttradex/app.log" ]; then
        local error_count=$(tail -100 /var/log/quanttradex/app.log | grep -i error | wc -l)
        if [ "$error_count" -gt 5 ]; then
            alert "WARNING" "应用日志中发现 $error_count 个错误"
        fi
    fi
}

# 检查安全状态
check_security() {
    log "🔍 检查安全状态..."
    
    # 检查失败的登录尝试
    local failed_logins=$(grep "Failed password" /var/log/auth.log 2>/dev/null | grep "$(date '+%b %d')" | wc -l)
    if [ "$failed_logins" -gt 10 ]; then
        alert "WARNING" "今日失败登录尝试过多: $failed_logins 次"
    fi
    
    # 检查异常网络连接
    local suspicious_connections=$(netstat -tuln | grep -E ":(22|3306|5432)" | grep -v "127.0.0.1\|::1" | wc -l)
    if [ "$suspicious_connections" -gt 0 ]; then
        alert "INFO" "发现 $suspicious_connections 个外部数据库连接"
    fi
    
    # 检查文件权限
    local world_writable=$(find /www/wwwroot/gdpp.com -type f -perm 777 2>/dev/null | wc -l)
    if [ "$world_writable" -gt 0 ]; then
        alert "WARNING" "发现 $world_writable 个全局可写文件"
    fi
}

# 磁盘清理函数
cleanup_disk() {
    log "🧹 开始自动磁盘清理..."
    
    # 清理临时文件
    find /tmp -type f -mtime +7 -delete 2>/dev/null || true
    find /var/tmp -type f -mtime +7 -delete 2>/dev/null || true
    
    # 清理旧日志文件
    find /var/log -name "*.log.*" -mtime +30 -delete 2>/dev/null || true
    
    # 清理APT缓存
    if command -v apt-get >/dev/null 2>&1; then
        apt-get clean 2>/dev/null || true
    fi
    
    # 清理journal日志
    if command -v journalctl >/dev/null 2>&1; then
        journalctl --vacuum-time=30d 2>/dev/null || true
    fi
    
    log "✅ 磁盘清理完成"
}

# 生成性能报告
generate_performance_report() {
    local report_file="${LOG_DIR}/performance_report_$(date +%Y%m%d).txt"
    
    cat > "$report_file" << EOF
QuantTradeX 性能报告
==================
生成时间: $(date '+%Y-%m-%d %H:%M:%S')

系统信息:
--------
主机名: $(hostname)
系统版本: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
内核版本: $(uname -r)
运行时间: $(uptime -p)

资源使用:
--------
CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')
内存使用: $(free -h | awk 'NR==2{printf "%s/%s (%.1f%%)", $3, $2, $3*100/$2}')
磁盘使用: $(df -h / | awk 'NR==2{printf "%s/%s (%s)", $3, $2, $5}')
系统负载: $(uptime | awk -F'load average:' '{print $2}')

网络状态:
--------
总连接数: $(netstat -an | wc -l)
已建立连接: $(netstat -an | grep ESTABLISHED | wc -l)
监听端口: $(netstat -tuln | grep LISTEN | wc -l)

服务状态:
--------
EOF

    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "$service"; then
            echo "$service: 运行中" >> "$report_file"
        else
            echo "$service: 已停止" >> "$report_file"
        fi
    done

    cat >> "$report_file" << EOF

进程信息:
--------
总进程数: $(ps aux | wc -l)
CPU占用前5:
$(ps aux --sort=-%cpu | head -6 | awk 'NR>1{printf "  %s: %.1f%%\n", $11, $3}')

内存占用前5:
$(ps aux --sort=-%mem | head -6 | awk 'NR>1{printf "  %s: %.1f%%\n", $11, $4}')

磁盘IO:
------
$(iostat -x 1 1 2>/dev/null | tail -n +4 || echo "iostat 不可用")

最近警报:
--------
$(tail -10 "$ALERT_LOG" 2>/dev/null || echo "无警报记录")
EOF

    log "📊 性能报告已生成: $report_file"
}

# ===========================================
# 主函数
# ===========================================

main() {
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    log "🚀 开始 QuantTradeX 系统监控检查..."
    
    # 执行各项检查
    check_services
    check_ports
    check_cpu
    check_memory
    check_disk
    check_load
    check_connections
    check_app_health
    check_security
    
    # 每小时生成一次性能报告
    local current_minute=$(date +%M)
    if [ "$current_minute" = "00" ]; then
        generate_performance_report
    fi
    
    log "✅ 监控检查完成"
}

# ===========================================
# 脚本入口
# ===========================================

# 检查bc命令是否可用（用于浮点数比较）
if ! command -v bc >/dev/null 2>&1; then
    echo "警告: bc 命令不可用，某些检查可能无法正常工作"
fi

# 执行主函数
main "$@"
