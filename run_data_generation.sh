#!/bin/bash

# QuantTradeX 数据生成和集成工作流程
# 使用大模型API生成真实的模拟内容

echo "=================================================="
echo "QuantTradeX 智能数据生成工作流程"
echo "=================================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python3，请先安装Python"
    exit 1
fi

# 检查必要的包
echo "检查Python依赖包..."
python3 -c "import requests" 2>/dev/null || {
    echo "安装requests包..."
    pip3 install requests
}

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo "警告：未找到.env文件"
    echo "请复制.env.example为.env并配置API密钥"
    echo "cp .env.example .env"
    echo ""
    echo "支持的API提供商："
    echo "1. DeepSeek API (推荐，性价比高)"
    echo "2. OpenAI API (质量稳定)"
    echo "3. Claude API (逻辑性强)"
    echo ""
    read -p "是否继续使用备用内容？(y/N): " continue_without_api
    if [[ ! $continue_without_api =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 创建数据目录
mkdir -p generated_data

echo ""
echo "第1步：生成智能模拟数据"
echo "================================"
echo "正在使用大模型API生成真实的模拟内容..."
echo "包括：论坛帖子、策略代码、用户评论"
echo ""

# 运行数据生成器
python3 data_generator.py

echo ""
echo "第2步：集成生成的数据"
echo "================================"
echo "正在将生成的数据集成到QuantTradeX平台..."
echo ""

# 运行数据集成器
python3 integrate_generated_data.py

echo ""
echo "第3步：验证修复效果"
echo "================================"

# 检查生成的文件
if [ -f "generated_data/forum_posts.json" ]; then
    post_count=$(python3 -c "import json; data=json.load(open('generated_data/forum_posts.json')); print(len(data))")
    echo "✅ 论坛帖子: $post_count 个"
else
    echo "❌ 论坛帖子生成失败"
fi

if [ -f "generated_data/strategy_codes.json" ]; then
    strategy_count=$(python3 -c "import json; data=json.load(open('generated_data/strategy_codes.json')); print(len(data))")
    echo "✅ 策略代码: $strategy_count 个"
else
    echo "❌ 策略代码生成失败"
fi

if [ -f "generated_data/user_comments.json" ]; then
    comment_count=$(python3 -c "import json; data=json.load(open('generated_data/user_comments.json')); print(len(data))")
    echo "✅ 用户评论: $comment_count 个"
else
    echo "❌ 用户评论生成失败"
fi

if [ -f "strategy_codes_mapping.json" ]; then
    echo "✅ 策略代码映射文件已创建"
else
    echo "❌ 策略代码映射文件创建失败"
fi

echo ""
echo "第4步：测试页面"
echo "================================"
echo "可以访问以下页面验证修复效果："
echo ""
echo "🏠 主页: http://gdpp.com/"
echo "📊 仪表板: http://gdpp.com/dashboard"
echo "🛒 策略市场: http://gdpp.com/strategies"
echo "💬 论坛: http://gdpp.com/forum"
echo "🚀 高级回测: http://gdpp.com/advanced_backtest"
echo "📡 实时数据: http://gdpp.com/realtime"
echo "👤 个人资料: http://gdpp.com/profile"
echo ""
echo "🧪 测试页面:"
echo "📋 下拉框测试: http://gdpp.com/dropdown_fix_test.html"
echo ""

echo "=================================================="
echo "数据生成和集成完成！"
echo "=================================================="
echo ""
echo "📝 使用说明："
echo "1. 所有下拉框颜色问题已修复"
echo "2. 论坛帖子内容已更新为真实模拟内容"
echo "3. 策略代码已生成对应的真实代码"
echo "4. 个人资料页面已创建"
echo ""
echo "🔄 如需重新生成数据："
echo "bash run_data_generation.sh"
echo ""
echo "⚙️ 如需配置API密钥："
echo "编辑 .env 文件，添加您的API密钥"
echo ""
echo "📞 如有问题，请检查："
echo "- API密钥配置是否正确"
echo "- 网络连接是否正常"
echo "- 生成的数据文件是否完整"
