#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地内容生成器 - 不依赖API，直接生成完整的样本内容
"""

import json
import random
from datetime import datetime, timedelta

def generate_strategy_codes():
    """生成完整的策略代码"""
    strategies = {
        "1": {
            "name": "双均线策略",
            "code": """def strategy(data):
    # 计算移动平均线
    sma_20 = data['close'].rolling(20).mean()
    sma_50 = data['close'].rolling(50).mean()

    # 生成交易信号
    signals = []
    for i in range(len(data)):
        if sma_20.iloc[i] > sma_50.iloc[i]:
            signals.append('buy')
        else:
            signals.append('sell')

    return signals"""
        },
        "2": {
            "name": "RSI均值回归策略",
            "code": """def strategy(data):
    # 计算RSI指标
    def calculate_rsi(prices, period=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    rsi = calculate_rsi(data['close'])
    signals = []

    for i in range(len(data)):
        if rsi.iloc[i] < 30:  # 超卖
            signals.append('buy')
        elif rsi.iloc[i] > 70:  # 超买
            signals.append('sell')
        else:
            signals.append('hold')

    return signals"""
        },
        "3": {
            "name": "布林带策略",
            "code": """def strategy(data):
    # 计算布林带
    period = 20
    std_dev = 2

    sma = data['close'].rolling(period).mean()
    std = data['close'].rolling(period).std()

    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)

    signals = []
    for i in range(len(data)):
        if data['close'].iloc[i] < lower_band.iloc[i]:
            signals.append('buy')  # 价格触及下轨，买入
        elif data['close'].iloc[i] > upper_band.iloc[i]:
            signals.append('sell')  # 价格触及上轨，卖出
        else:
            signals.append('hold')

    return signals"""
        },
        "4": {
            "name": "MACD动量策略",
            "code": """def strategy(data):
    # 计算MACD指标
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram

    macd, signal, histogram = calculate_macd(data['close'])
    signals = []

    for i in range(1, len(data)):
        # MACD金叉买入，死叉卖出
        if macd.iloc[i] > signal.iloc[i] and macd.iloc[i-1] <= signal.iloc[i-1]:
            signals.append('buy')
        elif macd.iloc[i] < signal.iloc[i] and macd.iloc[i-1] >= signal.iloc[i-1]:
            signals.append('sell')
        else:
            signals.append('hold')

    return ['hold'] + signals"""
        },
        "5": {
            "name": "均值回归策略",
            "code": """def strategy(data):
    # 计算价格偏离度
    period = 20
    mean_price = data['close'].rolling(period).mean()
    std_price = data['close'].rolling(period).std()

    # 计算Z-Score
    z_score = (data['close'] - mean_price) / std_price

    signals = []
    for i in range(len(data)):
        if z_score.iloc[i] < -2:  # 价格严重低于均值
            signals.append('buy')
        elif z_score.iloc[i] > 2:  # 价格严重高于均值
            signals.append('sell')
        elif abs(z_score.iloc[i]) < 0.5:  # 价格接近均值，平仓
            signals.append('close')
        else:
            signals.append('hold')

    return signals"""
        },
        "6": {
            "name": "趋势跟踪策略",
            "code": """def strategy(data):
    # 使用ATR和移动平均确定趋势
    def calculate_atr(data, period=14):
        high_low = data['high'] - data['low']
        high_close = abs(data['high'] - data['close'].shift())
        low_close = abs(data['low'] - data['close'].shift())
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return true_range.rolling(period).mean()

    atr = calculate_atr(data)
    sma_50 = data['close'].rolling(50).mean()

    signals = []
    for i in range(len(data)):
        # 趋势确认：价格在均线上方且ATR增加
        if (data['close'].iloc[i] > sma_50.iloc[i] and
            i > 0 and atr.iloc[i] > atr.iloc[i-1]):
            signals.append('buy')
        elif (data['close'].iloc[i] < sma_50.iloc[i] and
              i > 0 and atr.iloc[i] > atr.iloc[i-1]):
            signals.append('sell')
        else:
            signals.append('hold')

    return signals"""
        },
        "7": {
            "name": "配对交易策略",
            "code": """def strategy(data_a, data_b):
    # 配对交易：寻找两个相关资产的价差机会
    import numpy as np

    # 计算价格比率
    ratio = data_a['close'] / data_b['close']
    ratio_mean = ratio.rolling(30).mean()
    ratio_std = ratio.rolling(30).std()

    # 计算Z-Score
    z_score = (ratio - ratio_mean) / ratio_std

    signals = []
    for i in range(len(data_a)):
        if z_score.iloc[i] > 2:  # 比率过高，做空A做多B
            signals.append('short_a_long_b')
        elif z_score.iloc[i] < -2:  # 比率过低，做多A做空B
            signals.append('long_a_short_b')
        elif abs(z_score.iloc[i]) < 0.5:  # 比率回归，平仓
            signals.append('close_all')
        else:
            signals.append('hold')

    return signals"""
        },
        "8": {
            "name": "波动率突破策略",
            "code": """def strategy(data):
    # 基于波动率的突破策略
    period = 20

    # 计算历史波动率
    returns = data['close'].pct_change()
    volatility = returns.rolling(period).std() * (252 ** 0.5)  # 年化波动率

    # 计算价格通道
    high_channel = data['high'].rolling(period).max()
    low_channel = data['low'].rolling(period).min()

    signals = []
    for i in range(len(data)):
        # 高波动率环境下的突破信号
        if (volatility.iloc[i] > volatility.rolling(50).mean().iloc[i] and
            data['close'].iloc[i] > high_channel.iloc[i-1] if i > 0 else False):
            signals.append('buy')
        elif (volatility.iloc[i] > volatility.rolling(50).mean().iloc[i] and
              data['close'].iloc[i] < low_channel.iloc[i-1] if i > 0 else False):
            signals.append('sell')
        else:
            signals.append('hold')

    return signals"""
        }
    }

    return strategies

def generate_forum_posts():
    """生成完整的论坛帖子内容"""
    posts = []

    post_templates = [
        {
            "title": "量化交易中的风险管理：我的实战经验分享",
            "content": """经过两年的量化交易实践，我想分享一些关于风险管理的心得体会。

**1. 仓位管理是关键**
我采用的是固定比例仓位管理，每笔交易不超过总资金的2%。这样即使连续亏损10次，也只会损失20%的资金，给策略足够的试错空间。

**2. 止损设置**
- 技术止损：基于ATR的动态止损，通常设置为2-3倍ATR
- 时间止损：如果持仓超过预期时间没有盈利，主动平仓
- 资金止损：单日亏损超过总资金1%时停止交易

**3. 分散化投资**
不要把所有资金投入到一个策略或一个市场，我通常会：
- 运行3-5个不同类型的策略
- 投资不同的资产类别（股票、期货、外汇）
- 在不同时间框架上操作

**4. 回撤控制**
当策略回撤超过历史最大回撤的1.5倍时，我会暂停策略运行，重新评估市场环境。

大家有什么好的风险管理方法吗？欢迎交流讨论！""",
            "author": "RiskMaster",
            "category": "risk_management"
        },
        {
            "title": "机器学习在股票预测中的效果如何？",
            "content": """最近在研究用机器学习做股票预测，想和大家分享一些初步结果。

**使用的模型：**
1. **随机森林**：效果还不错，特征重要性分析很有用
2. **LSTM**：对时序数据处理能力强，但容易过拟合
3. **XGBoost**：综合表现最好，速度快且准确率高

**特征工程：**
- 技术指标：RSI、MACD、布林带等
- 价格特征：收益率、波动率、成交量
- 市场情绪：VIX指数、新闻情感分析
- 宏观数据：利率、汇率、商品价格

**实测结果：**
- 预测准确率：约55-60%（比随机好，但不算很高）
- 夏普比率：1.2-1.8（还算可以接受）
- 最大回撤：15-25%（需要进一步优化）

**遇到的问题：**
1. 数据质量影响很大，需要仔细清洗
2. 特征选择很关键，太多特征容易过拟合
3. 模型需要定期重训练，市场环境在变化

有做ML量化的朋友吗？想交流一下经验！""",
            "author": "MLTrader",
            "category": "machine_learning"
        },
        {
            "title": "移动平均策略的优化技巧",
            "content": """移动平均策略虽然简单，但通过一些优化技巧可以显著提升效果。

**1. 参数优化**
不要只用经典的20/50组合，可以尝试：
- 短期：5、8、13、21天
- 长期：34、55、89、144天
- 使用斐波那契数列作为参数

**2. 多重确认**
单纯的金叉死叉信号噪音太多，建议加入：
- 成交量确认：金叉时成交量放大
- 趋势确认：价格在长期均线上方
- 动量确认：RSI或MACD同步发出信号

**3. 自适应均线**
使用自适应移动平均（AMA）替代简单移动平均：
```python
def adaptive_ma(prices, period=14):
    direction = abs(prices - prices.shift(period))
    volatility = abs(prices - prices.shift(1)).rolling(period).sum()
    efficiency = direction / volatility
    # 根据效率比调整平滑常数
    alpha = (efficiency * 0.6667 + 0.0645) ** 2
    return prices.ewm(alpha=alpha).mean()
```

**4. 过滤器应用**
- 波动率过滤：低波动率时暂停交易
- 趋势过滤：只在明确趋势中交易
- 时间过滤：避开重要数据发布时间

**回测结果对比：**
- 原始策略：年化收益8%，最大回撤18%
- 优化后：年化收益15%，最大回撤12%

大家还有什么优化思路吗？""",
            "author": "TrendFollower",
            "category": "strategy_optimization"
        }
    ]

    # 生成更多帖子
    for i, template in enumerate(post_templates):
        post = {
            "id": i + 1,
            "title": template["title"],
            "content": template["content"],
            "author": template["author"],
            "category": template["category"],
            "created_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
            "views": random.randint(100, 2000),
            "likes": random.randint(10, 200),
            "reply_count": random.randint(3, 50),
            "is_pinned": i == 0,  # 第一个帖子置顶
            "is_locked": False,
            "tags": ["量化交易", "策略优化", "风险管理"]
        }
        posts.append(post)

    return posts

def main():
    """主函数"""
    print("🚀 开始生成完整的本地内容...")

    # 生成策略代码
    print("📝 生成策略代码...")
    strategies = generate_strategy_codes()
    with open('strategy_codes_mapping.json', 'w', encoding='utf-8') as f:
        json.dump(strategies, f, ensure_ascii=False, indent=2)
    print(f"✅ 生成了 {len(strategies)} 个完整策略代码")

    # 生成论坛帖子
    print("📰 生成论坛帖子...")
    posts = generate_forum_posts()
    with open('generated_data/forum_posts_complete.json', 'w', encoding='utf-8') as f:
        json.dump(posts, f, ensure_ascii=False, indent=2)
    print(f"✅ 生成了 {len(posts)} 个完整论坛帖子")

    print("\n🎉 内容生成完成！")
    print("📁 生成的文件：")
    print("  - strategy_codes_mapping.json (策略代码)")
    print("  - generated_data/forum_posts_complete.json (论坛帖子)")
    print("\n💡 请重启Flask应用以加载新内容")

if __name__ == "__main__":
    main()
