#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速功能测试脚本
"""

import requests
import json

def test_apis():
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 快速API测试")
    print("=" * 40)
    
    # 测试股票数据API
    print("1. 测试股票数据API...")
    try:
        response = requests.get(f"{base_url}/api/stock/AAPL", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stock_data = data['data']
                print(f"   ✅ 成功 - {stock_data['symbol']}, {len(stock_data['dates'])}个数据点")
                print(f"   数据源: {stock_data.get('source', 'Yahoo Finance')}")
            else:
                print(f"   ❌ 失败: {data.get('error')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试论坛API
    print("2. 测试论坛API...")
    try:
        response = requests.get(f"{base_url}/api/forum/posts", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ 成功 - {len(data['posts'])}个帖子")
            else:
                print(f"   ❌ 失败: {data.get('error')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试加密货币API
    print("3. 测试加密货币API...")
    try:
        response = requests.get(f"{base_url}/api/realtime/crypto/BTC", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ 成功 - BTC价格: ${data['data']['price']}")
            else:
                print(f"   ❌ 失败: {data.get('error')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试关注列表API
    print("4. 测试关注列表API...")
    try:
        response = requests.get(f"{base_url}/api/watchlist", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ 成功 - {len(data['watchlist'])}个股票")
            else:
                print(f"   ❌ 失败: {data.get('error')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print("=" * 40)
    print("🎉 测试完成")

if __name__ == "__main__":
    test_apis()
