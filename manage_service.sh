#!/bin/bash

# QuantTradeX 服务管理脚本
# 用于管理 QuantTradeX 应用的 systemd 服务

SERVICE_NAME="quanttradex"
PROJECT_DIR="/www/wwwroot/www.gdpp.com"
VENV_PATH="$PROJECT_DIR/venv"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查服务状态
check_status() {
    print_message $BLUE "检查 $SERVICE_NAME 服务状态..."
    sudo systemctl status $SERVICE_NAME.service --no-pager
}

# 启动服务
start_service() {
    print_message $BLUE "启动 $SERVICE_NAME 服务..."
    sudo systemctl start $SERVICE_NAME.service
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 服务启动成功"
        sleep 2
        check_status
    else
        print_message $RED "❌ 服务启动失败"
    fi
}

# 停止服务
stop_service() {
    print_message $BLUE "停止 $SERVICE_NAME 服务..."
    sudo systemctl stop $SERVICE_NAME.service
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 服务停止成功"
    else
        print_message $RED "❌ 服务停止失败"
    fi
}

# 重启服务
restart_service() {
    print_message $BLUE "重启 $SERVICE_NAME 服务..."
    sudo systemctl restart $SERVICE_NAME.service
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 服务重启成功"
        sleep 2
        check_status
    else
        print_message $RED "❌ 服务重启失败"
    fi
}

# 启用自动启动
enable_service() {
    print_message $BLUE "启用 $SERVICE_NAME 服务自动启动..."
    sudo systemctl enable $SERVICE_NAME.service
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 自动启动已启用"
    else
        print_message $RED "❌ 启用自动启动失败"
    fi
}

# 禁用自动启动
disable_service() {
    print_message $BLUE "禁用 $SERVICE_NAME 服务自动启动..."
    sudo systemctl disable $SERVICE_NAME.service
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 自动启动已禁用"
    else
        print_message $RED "❌ 禁用自动启动失败"
    fi
}

# 查看日志
view_logs() {
    print_message $BLUE "查看 $SERVICE_NAME 服务日志..."
    sudo journalctl -u $SERVICE_NAME.service -f --no-pager
}

# 查看最近日志
view_recent_logs() {
    print_message $BLUE "查看 $SERVICE_NAME 服务最近日志..."
    sudo journalctl -u $SERVICE_NAME.service -n 50 --no-pager
}

# 测试应用
test_app() {
    print_message $BLUE "测试应用连接..."
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000)
    if [ "$response" = "200" ]; then
        print_message $GREEN "✅ 应用运行正常 (HTTP 200)"
        print_message $GREEN "🌐 访问地址: http://localhost:5000"
    else
        print_message $RED "❌ 应用连接失败 (HTTP $response)"
    fi
}

# 安装依赖
install_deps() {
    print_message $BLUE "安装 Python 依赖..."
    cd $PROJECT_DIR
    if [ -f "requirements.txt" ]; then
        $VENV_PATH/bin/pip install -r requirements.txt
        print_message $GREEN "✅ 依赖安装完成"
    else
        print_message $RED "❌ 未找到 requirements.txt 文件"
    fi
}

# 显示帮助
show_help() {
    echo "QuantTradeX 服务管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看服务状态"
    echo "  enable      启用自动启动"
    echo "  disable     禁用自动启动"
    echo "  logs        查看实时日志"
    echo "  recent      查看最近日志"
    echo "  test        测试应用连接"
    echo "  install     安装依赖"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start     # 启动服务"
    echo "  $0 status    # 查看状态"
    echo "  $0 logs      # 查看日志"
}

# 主逻辑
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        check_status
        ;;
    enable)
        enable_service
        ;;
    disable)
        disable_service
        ;;
    logs)
        view_logs
        ;;
    recent)
        view_recent_logs
        ;;
    test)
        test_app
        ;;
    install)
        install_deps
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $YELLOW "未知选项: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
