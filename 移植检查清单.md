# QuantTradeX 移植检查清单

## 📋 移植前准备

### 源服务器准备
- [ ] 创建完整项目备份
  ```bash
  cd /www/wwwroot/gdpp.com
  tar -czf quanttradex_backup_$(date +%Y%m%d).tar.gz \
      --exclude='venv' \
      --exclude='__pycache__' \
      --exclude='*.pyc' \
      --exclude='.git' \
      .
  ```

- [ ] 导出数据库数据
  ```bash
  pg_dump -h localhost -U quanttradex_user -d quanttradex > quanttradex_db_backup.sql
  ```

- [ ] 备份用户数据
  ```bash
  python data_migration.py --action export
  ```

- [ ] 记录重要配置信息
  - 数据库密码
  - Redis配置
  - 第三方API密钥
  - SSL证书信息

### 目标服务器准备
- [ ] Ubuntu 22.04系统安装完成
- [ ] 宝塔面板安装完成
- [ ] 域名DNS解析配置 (www.gdpp.com)
- [ ] 服务器基础安全配置

## 🚀 移植执行步骤

### 1. 环境安装
- [ ] 运行快速移植脚本
  ```bash
  chmod +x quick_migration.sh
  ./quick_migration.sh
  ```

- [ ] 或手动安装环境
  - [ ] Python 3.8+
  - [ ] PostgreSQL 13+
  - [ ] Redis 6+
  - [ ] Nginx 1.18+
  - [ ] PM2

### 2. 项目部署
- [ ] 上传项目文件到 `/www/wwwroot/www.gdpp.com`
- [ ] 解压项目备份
- [ ] 创建Python虚拟环境
- [ ] 安装项目依赖
  ```bash
  pip install -r requirements.txt
  ```

### 3. 数据库配置
- [ ] 创建PostgreSQL数据库和用户
  ```sql
  CREATE DATABASE quanttradex;
  CREATE USER quanttradex_user WITH PASSWORD 'your_password';
  GRANT ALL PRIVILEGES ON DATABASE quanttradex TO quanttradex_user;
  ```

- [ ] 导入数据库数据
  ```bash
  psql -h localhost -U quanttradex_user -d quanttradex < quanttradex_db_backup.sql
  ```

- [ ] 配置Redis服务

### 4. 配置更新
- [ ] 更新数据库连接配置
- [ ] 更新域名引用 (gdpp.com → gdpp.com)
- [ ] 更新API密钥和配置
- [ ] 设置正确的文件权限

### 5. Web服务器配置
- [ ] 配置Nginx反向代理
- [ ] 申请和配置SSL证书
- [ ] 配置防火墙规则
- [ ] 设置进程管理 (PM2)

## ✅ 功能测试验证

### 基础功能测试
- [ ] 网站首页正常访问 (https://www.gdpp.com)
- [ ] 静态文件加载正常
- [ ] API接口响应正常
- [ ] 数据库连接正常
- [ ] Redis缓存正常

### 用户功能测试
- [ ] 用户注册功能
- [ ] 用户登录功能
- [ ] 用户资料管理
- [ ] 密码重置功能
- [ ] 登录状态持久化

### 核心功能测试
- [ ] 策略市场页面
- [ ] 我的策略管理
- [ ] 策略开发工具
- [ ] 策略详情查看
- [ ] 策略编辑功能

### 高级功能测试
- [ ] 基础回测系统
- [ ] 高级回测功能
- [ ] 实时数据中心
- [ ] 社区论坛
- [ ] WebSocket连接

### 性能测试
- [ ] 页面加载速度
- [ ] API响应时间
- [ ] 数据库查询性能
- [ ] 并发用户测试
- [ ] 内存使用情况

## 🔧 故障排除

### 常见问题检查
- [ ] 检查服务状态
  ```bash
  systemctl status nginx postgresql redis pm2
  ```

- [ ] 检查端口监听
  ```bash
  netstat -tlnp | grep -E ':(80|443|5000|5432|6379)'
  ```

- [ ] 检查日志文件
  ```bash
  tail -f /www/wwwroot/www.gdpp.com/logs/quanttradex.log
  tail -f /var/log/nginx/error.log
  ```

- [ ] 检查进程状态
  ```bash
  pm2 status
  pm2 logs quanttradex
  ```

### 权限问题
- [ ] 检查文件权限
  ```bash
  ls -la /www/wwwroot/www.gdpp.com
  ```

- [ ] 修复权限问题
  ```bash
  chown -R www:www /www/wwwroot/www.gdpp.com
  chmod -R 755 /www/wwwroot/www.gdpp.com
  ```

### 数据库问题
- [ ] 检查数据库连接
  ```bash
  psql -h localhost -U quanttradex_user -d quanttradex -c "SELECT version();"
  ```

- [ ] 检查数据库权限
  ```sql
  \du quanttradex_user
  \l quanttradex
  ```

## 🛡️ 安全配置

### 基础安全
- [ ] 防火墙配置
  ```bash
  ufw enable
  ufw allow 22,80,443,8888/tcp
  ```

- [ ] SSL证书配置
- [ ] 安全头配置
- [ ] 敏感文件权限设置

### 高级安全
- [ ] 数据库安全配置
- [ ] Redis安全配置
- [ ] 应用安全配置
- [ ] 日志监控配置

## 📊 性能优化

### 系统优化
- [ ] 内核参数优化
- [ ] 文件描述符限制
- [ ] 内存配置优化

### 应用优化
- [ ] Nginx配置优化
- [ ] PostgreSQL性能调优
- [ ] Redis缓存优化
- [ ] Python应用优化

### 监控配置
- [ ] 系统监控脚本
- [ ] 应用性能监控
- [ ] 日志轮转配置
- [ ] 告警机制设置

## 📈 移植后验证

### 自动验证
- [ ] 运行验证脚本
  ```bash
  ./verify_migration.sh
  ```

### 手动验证
- [ ] 完整功能测试
- [ ] 性能基准测试
- [ ] 安全扫描测试
- [ ] 用户体验测试

### 数据验证
- [ ] 用户数据完整性
- [ ] 策略数据完整性
- [ ] 配置数据正确性
- [ ] 文件完整性检查

## 🎯 移植完成

### 最终检查
- [ ] 所有功能正常运行
- [ ] 性能指标达标
- [ ] 安全配置完成
- [ ] 监控系统运行
- [ ] 备份策略制定

### 文档更新
- [ ] 更新部署文档
- [ ] 更新运维手册
- [ ] 记录配置变更
- [ ] 创建移植报告

### 上线准备
- [ ] DNS切换准备
- [ ] 用户通知准备
- [ ] 回滚方案准备
- [ ] 技术支持准备

---

## 🎉 移植成功！

**恭喜！QuantTradeX项目已成功移植到www.gdpp.com！**

### 访问地址
- **主站**: https://www.gdpp.com
- **管理面板**: https://www.gdpp.com:8888

### 管理命令
```bash
# 应用管理
pm2 status                    # 查看应用状态
pm2 restart quanttradex       # 重启应用
pm2 logs quanttradex          # 查看应用日志

# 服务管理
systemctl status nginx        # 检查Nginx状态
systemctl status postgresql   # 检查数据库状态
systemctl status redis        # 检查Redis状态

# 监控命令
./verify_migration.sh         # 运行验证脚本
python monitor.py             # 系统健康检查
```

### 下一步
1. 配置域名DNS解析
2. 申请SSL证书
3. 设置定期备份
4. 配置监控告警
5. 进行压力测试

**技术支持**: 如有问题请查看日志文件或联系技术支持团队
