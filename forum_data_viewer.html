<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论坛数据查看器 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            color: white;
        }

        .card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            color: white;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #06b6d4;
        }

        .post-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .post-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .badge {
            font-size: 0.75rem;
        }

        .category-badge {
            background: linear-gradient(45deg, #6366f1, #8b5cf6);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
        }

        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 5px 12px;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .tag-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #6366f1, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .filter-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 8px;
            padding: 8px 16px;
            margin: 4px;
            transition: all 0.3s ease;
        }

        .filter-btn:hover, .filter-btn.active {
            background: rgba(99, 102, 241, 0.3);
            border-color: #6366f1;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-comments me-2"></i>
                    QuantTradeX 论坛数据查看器
                </h1>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4" id="statsContainer">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalPosts">-</div>
                    <div class="stats-label">总帖子数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalUsers">-</div>
                    <div class="stats-label">活跃用户</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalViews">-</div>
                    <div class="stats-label">总浏览量</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalLikes">-</div>
                    <div class="stats-label">总点赞数</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：筛选和分类 -->
            <div class="col-md-3">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-filter me-2"></i>筛选选项
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">搜索</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索帖子..." 
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">分类筛选</label>
                            <div id="categoryFilters">
                                <button class="filter-btn active" onclick="filterByCategory('')">全部</button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">排序方式</label>
                            <select class="form-select" id="sortBy" onchange="sortPosts()" 
                                    style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                                <option value="created_at">发布时间</option>
                                <option value="views">浏览量</option>
                                <option value="likes">点赞数</option>
                                <option value="reply_count">回复数</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 热门标签 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tags me-2"></i>热门标签
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="tag-cloud" id="tagCloud">
                            <!-- 标签将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：帖子列表 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-list me-2"></i>帖子列表
                        </h6>
                        <div>
                            <span class="badge bg-primary" id="postCount">0</span> 条帖子
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="postsContainer">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let allPosts = [];
        let filteredPosts = [];
        let currentCategory = '';

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadForumData();
            
            // 搜索框事件
            document.getElementById('searchInput').addEventListener('input', function() {
                filterPosts();
            });
        });

        // 加载论坛数据
        async function loadForumData() {
            try {
                const response = await fetch('forum_api_data.json');
                const data = await response.json();
                
                allPosts = data.posts || [];
                filteredPosts = [...allPosts];
                
                // 显示统计信息
                displayStats(data.stats);
                
                // 生成分类筛选按钮
                generateCategoryFilters();
                
                // 生成标签云
                generateTagCloud();
                
                // 显示帖子
                displayPosts();
                
            } catch (error) {
                console.error('加载数据失败:', error);
                document.getElementById('postsContainer').innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>加载数据失败，请检查 forum_api_data.json 文件是否存在</p>
                    </div>
                `;
            }
        }

        // 显示统计信息
        function displayStats(stats) {
            if (stats) {
                document.getElementById('totalPosts').textContent = stats.total_posts?.toLocaleString() || '0';
                document.getElementById('totalUsers').textContent = stats.total_users?.toLocaleString() || '0';
                document.getElementById('totalViews').textContent = stats.total_views?.toLocaleString() || '0';
                document.getElementById('totalLikes').textContent = stats.total_likes?.toLocaleString() || '0';
            }
        }

        // 生成分类筛选按钮
        function generateCategoryFilters() {
            const categories = [...new Set(allPosts.map(post => post.category))];
            const container = document.getElementById('categoryFilters');
            
            categories.forEach(category => {
                const button = document.createElement('button');
                button.className = 'filter-btn';
                button.textContent = getCategoryName(category);
                button.onclick = () => filterByCategory(category);
                container.appendChild(button);
            });
        }

        // 生成标签云
        function generateTagCloud() {
            const tagCount = {};
            allPosts.forEach(post => {
                if (post.tags) {
                    post.tags.forEach(tag => {
                        tagCount[tag] = (tagCount[tag] || 0) + 1;
                    });
                }
            });

            const sortedTags = Object.entries(tagCount)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 20);

            const container = document.getElementById('tagCloud');
            container.innerHTML = sortedTags.map(([tag, count]) => `
                <div class="tag-item" onclick="filterByTag('${tag}')">
                    ${tag} <span class="badge bg-secondary ms-1">${count}</span>
                </div>
            `).join('');
        }

        // 按分类筛选
        function filterByCategory(category) {
            currentCategory = category;
            
            // 更新按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            filterPosts();
        }

        // 按标签筛选
        function filterByTag(tag) {
            document.getElementById('searchInput').value = tag;
            filterPosts();
        }

        // 筛选帖子
        function filterPosts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            
            filteredPosts = allPosts.filter(post => {
                const matchesCategory = !currentCategory || post.category === currentCategory;
                const matchesSearch = !searchTerm || 
                    post.title.toLowerCase().includes(searchTerm) ||
                    post.content.toLowerCase().includes(searchTerm) ||
                    (post.tags && post.tags.some(tag => tag.toLowerCase().includes(searchTerm)));
                
                return matchesCategory && matchesSearch;
            });
            
            displayPosts();
        }

        // 排序帖子
        function sortPosts() {
            const sortBy = document.getElementById('sortBy').value;
            
            filteredPosts.sort((a, b) => {
                if (sortBy === 'created_at') {
                    return new Date(b.created_at) - new Date(a.created_at);
                } else {
                    return b[sortBy] - a[sortBy];
                }
            });
            
            displayPosts();
        }

        // 显示帖子
        function displayPosts() {
            const container = document.getElementById('postsContainer');
            document.getElementById('postCount').textContent = filteredPosts.length;
            
            if (filteredPosts.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-search fa-2x mb-3"></i>
                        <p>没有找到匹配的帖子</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredPosts.map(post => {
                const createdDate = new Date(post.created_at);
                const timeAgo = getTimeAgo(createdDate);
                
                return `
                    <div class="post-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    ${post.is_pinned ? '<i class="fas fa-thumbtack text-warning me-1"></i>' : ''}
                                    ${post.is_locked ? '<i class="fas fa-lock text-secondary me-1"></i>' : ''}
                                    ${post.is_featured ? '<i class="fas fa-star text-warning me-1"></i>' : ''}
                                    ${post.title}
                                </h6>
                                <p class="text-white-50 small mb-2">${post.content.substring(0, 150)}${post.content.length > 150 ? '...' : ''}</p>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">${post.author.charAt(0).toUpperCase()}</div>
                                <div>
                                    <small class="text-white">${post.author}</small>
                                    <br>
                                    <small class="text-white-50">${timeAgo}</small>
                                </div>
                            </div>

                            <div class="d-flex align-items-center">
                                <span class="category-badge me-2">${getCategoryName(post.category)}</span>
                                <div class="text-end">
                                    <div class="d-flex align-items-center text-white-50 small">
                                        <i class="fas fa-eye me-1"></i>${post.views.toLocaleString()}
                                        <i class="fas fa-heart ms-2 me-1"></i>${post.likes.toLocaleString()}
                                        <i class="fas fa-comments ms-2 me-1"></i>${post.reply_count}
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${post.tags && post.tags.length > 0 ? `
                            <div class="mt-2">
                                ${post.tags.slice(0, 5).map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        // 时间格式化函数
        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) {
                return '刚刚';
            } else if (diffInSeconds < 3600) {
                return `${Math.floor(diffInSeconds / 60)}分钟前`;
            } else if (diffInSeconds < 86400) {
                return `${Math.floor(diffInSeconds / 3600)}小时前`;
            } else if (diffInSeconds < 2592000) {
                return `${Math.floor(diffInSeconds / 86400)}天前`;
            } else {
                return date.toLocaleDateString();
            }
        }

        // 获取分类名称
        function getCategoryName(category) {
            const categories = {
                'general': '综合讨论',
                'strategy': '策略分享',
                'market': '市场分析',
                'help': '新手求助',
                'news': '行业资讯',
                'crypto': '数字货币',
                'risk': '风险管理',
                'data': '数据分析',
                'technology': '技术讨论',
                'education': '教育资源',
                'discussion': '讨论',
                'psychology': '交易心理',
                'arbitrage': '套利',
                'announcement': '公告',
                'resources': '资源',
                'feedback': '反馈',
                'regulation': '监管',
                'options': '期权',
                'futures': '期货',
                'forex': '外汇'
            };
            return categories[category] || category;
        }
    </script>
</body>
</html>
