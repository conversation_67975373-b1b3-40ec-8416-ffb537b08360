#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试会话修复效果的脚本
"""

import requests
import time
import random

def test_session_fix():
    """测试会话修复效果"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔧 测试会话修复效果")
    print("=" * 50)
    
    # 创建会话
    session = requests.Session()
    
    # 生成随机用户名避免冲突
    random_suffix = random.randint(1000, 9999)
    username = f"testuser{random_suffix}"
    
    print(f"测试用户: {username}")
    print("-" * 50)
    
    # 1. 测试注册并自动登录
    print("1. 测试注册...")
    register_data = {
        "username": username,
        "email": f"{username}@example.com",
        "password": "test123456",
        "full_name": f"测试用户{random_suffix}"
    }
    
    try:
        response = session.post(f"{base_url}/auth/register", json=register_data, timeout=10)
        result = response.json()
        print(f"   注册结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        if result.get('success'):
            print(f"   用户信息: {result.get('user', {}).get('username')}")
            print(f"   自动登录: {'✅ 是' if result.get('user') else '❌ 否'}")
        else:
            print(f"   错误: {result.get('error')}")
            return
    except Exception as e:
        print(f"   注册异常: {e}")
        return
    
    # 2. 立即检查认证状态
    print("\n2. 注册后立即检查认证状态...")
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        authenticated = result.get('authenticated', False)
        print(f"   认证状态: {'✅ 已认证' if authenticated else '❌ 未认证'}")
        if authenticated:
            user = result.get('user', {})
            print(f"   用户名: {user.get('username')}")
            print(f"   角色: {user.get('role')}")
            print(f"   全名: {user.get('full_name')}")
        else:
            print("   ❌ 注册后用户未自动登录！")
    except Exception as e:
        print(f"   检查认证状态异常: {e}")
    
    # 3. 测试访问需要认证的API
    print("\n3. 测试访问用户资料...")
    try:
        response = session.get(f"{base_url}/auth/profile", timeout=5)
        result = response.json()
        success = result.get('success', False)
        print(f"   访问结果: {'✅ 成功' if success else '❌ 失败'}")
        if success:
            user = result.get('user', {})
            print(f"   用户资料: {user.get('username')} ({user.get('full_name')})")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   访问资料异常: {e}")
    
    # 4. 等待10秒后再次检查（测试会话持久性）
    print("\n4. 等待10秒后再次检查会话...")
    time.sleep(10)
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        authenticated = result.get('authenticated', False)
        print(f"   认证状态: {'✅ 仍然认证' if authenticated else '❌ 会话丢失'}")
        if authenticated:
            user = result.get('user', {})
            print(f"   用户信息: {user.get('username')}")
        else:
            print("   ❌ 会话在10秒后丢失！")
    except Exception as e:
        print(f"   检查认证状态异常: {e}")
    
    # 5. 测试手动退出登录
    print("\n5. 测试退出登录...")
    try:
        response = session.post(f"{base_url}/auth/logout", json={}, timeout=5)
        result = response.json()
        success = result.get('success', False)
        print(f"   退出结果: {'✅ 成功' if success else '❌ 失败'}")
        if success:
            print(f"   消息: {result.get('message')}")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   退出登录异常: {e}")
    
    # 6. 退出后检查认证状态
    print("\n6. 退出后检查认证状态...")
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        authenticated = result.get('authenticated', False)
        print(f"   认证状态: {'❌ 仍然认证' if authenticated else '✅ 已退出'}")
        if not authenticated:
            print("   ✅ 退出登录成功")
        else:
            print("   ❌ 退出登录失败，用户仍然认证")
    except Exception as e:
        print(f"   检查认证状态异常: {e}")
    
    # 7. 测试重新登录
    print("\n7. 测试重新登录...")
    login_data = {
        "username": username,
        "password": "test123456"
    }
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        success = result.get('success', False)
        print(f"   登录结果: {'✅ 成功' if success else '❌ 失败'}")
        if success:
            user = result.get('user', {})
            print(f"   用户信息: {user.get('username')}")
            print(f"   重定向URL: {result.get('redirect_url')}")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   登录异常: {e}")
    
    # 8. 最终认证状态检查
    print("\n8. 最终认证状态检查...")
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        authenticated = result.get('authenticated', False)
        print(f"   认证状态: {'✅ 已认证' if authenticated else '❌ 未认证'}")
        if authenticated:
            user = result.get('user', {})
            print(f"   用户信息: {user.get('username')} ({user.get('full_name')})")
        
        # 检查Cookie
        cookies = session.cookies
        print(f"   会话Cookie: {'✅ 存在' if len(cookies) > 0 else '❌ 不存在'}")
        for cookie in cookies:
            if cookie.name == 'session':
                print(f"   Cookie值: {cookie.value[:20]}...")
                
    except Exception as e:
        print(f"   检查认证状态异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 会话测试完成")

if __name__ == "__main__":
    test_session_fix()
