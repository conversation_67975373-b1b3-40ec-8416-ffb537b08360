// QuantTradeX 主JavaScript文件

// 全局变量
window.QuantTradeX = {
    config: {
        apiUrl: window.location.origin,
        wsUrl: window.location.origin
    },
    user: null,
    socket: null
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('QuantTradeX 应用初始化...');
    
    // 初始化应用
    QuantTradeX.init();
});

// 主应用对象
QuantTradeX.init = function() {
    // 检查用户登录状态
    this.checkAuthStatus();
    
    // 初始化导航
    this.initNavigation();
    
    // 初始化通用功能
    this.initCommonFeatures();
    
    console.log('QuantTradeX 应用初始化完成');
};

// 检查认证状态
QuantTradeX.checkAuthStatus = function() {
    fetch('/auth/check')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.authenticated) {
                this.user = data.user;
                this.updateUIForLoggedInUser();
            } else {
                this.updateUIForGuestUser();
            }
        })
        .catch(error => {
            console.error('检查认证状态失败:', error);
        });
};

// 更新已登录用户的UI
QuantTradeX.updateUIForLoggedInUser = function() {
    const userElements = document.querySelectorAll('.user-info');
    userElements.forEach(element => {
        if (this.user) {
            element.textContent = this.user.full_name || this.user.username;
        }
    });
    
    // 显示/隐藏相关元素
    const loginElements = document.querySelectorAll('.login-required');
    loginElements.forEach(element => {
        element.style.display = 'block';
    });
    
    const guestElements = document.querySelectorAll('.guest-only');
    guestElements.forEach(element => {
        element.style.display = 'none';
    });
};

// 更新访客用户的UI
QuantTradeX.updateUIForGuestUser = function() {
    const loginElements = document.querySelectorAll('.login-required');
    loginElements.forEach(element => {
        element.style.display = 'none';
    });
    
    const guestElements = document.querySelectorAll('.guest-only');
    guestElements.forEach(element => {
        element.style.display = 'block';
    });
};

// 初始化导航
QuantTradeX.initNavigation = function() {
    // 高亮当前页面
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav a');
    
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
};

// 初始化通用功能
QuantTradeX.initCommonFeatures = function() {
    // 初始化工具提示
    this.initTooltips();
    
    // 初始化模态框
    this.initModals();
    
    // 初始化表单验证
    this.initFormValidation();
};

// 初始化工具提示
QuantTradeX.initTooltips = function() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = this.getAttribute('data-tooltip');
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.position = 'absolute';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
            tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
            tooltip.style.backgroundColor = '#333';
            tooltip.style.color = 'white';
            tooltip.style.padding = '5px 10px';
            tooltip.style.borderRadius = '4px';
            tooltip.style.fontSize = '12px';
            tooltip.style.zIndex = '1000';
        });
        
        element.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
};

// 初始化模态框
QuantTradeX.initModals = function() {
    const modalTriggers = document.querySelectorAll('[data-modal]');
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
            }
        });
    });
    
    // 关闭模态框
    const closeButtons = document.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });
    
    // 点击背景关闭模态框
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });
};

// 初始化表单验证
QuantTradeX.initFormValidation = function() {
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!QuantTradeX.validateForm(this)) {
                e.preventDefault();
            }
        });
    });
};

// 表单验证
QuantTradeX.validateForm = function(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            this.showFieldError(field, '此字段为必填项');
            isValid = false;
        } else {
            this.clearFieldError(field);
        }
    });
    
    // 邮箱验证
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !this.isValidEmail(field.value)) {
            this.showFieldError(field, '请输入有效的邮箱地址');
            isValid = false;
        }
    });
    
    return isValid;
};

// 显示字段错误
QuantTradeX.showFieldError = function(field, message) {
    this.clearFieldError(field);
    
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.color = '#e74c3c';
    errorElement.style.fontSize = '12px';
    errorElement.style.marginTop = '5px';
    
    field.parentNode.appendChild(errorElement);
    field.style.borderColor = '#e74c3c';
};

// 清除字段错误
QuantTradeX.clearFieldError = function(field) {
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
    field.style.borderColor = '';
};

// 邮箱验证
QuantTradeX.isValidEmail = function(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

// 显示通知
QuantTradeX.showNotification = function(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 样式
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.padding = '15px 20px';
    notification.style.borderRadius = '4px';
    notification.style.color = 'white';
    notification.style.zIndex = '9999';
    notification.style.maxWidth = '300px';
    
    // 根据类型设置颜色
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#27ae60';
            break;
        case 'error':
            notification.style.backgroundColor = '#e74c3c';
            break;
        case 'warning':
            notification.style.backgroundColor = '#f39c12';
            break;
        default:
            notification.style.backgroundColor = '#3498db';
    }
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
};

// API请求封装
QuantTradeX.api = {
    get: function(url) {
        return fetch(QuantTradeX.config.apiUrl + url)
            .then(response => response.json());
    },
    
    post: function(url, data) {
        return fetch(QuantTradeX.config.apiUrl + url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        }).then(response => response.json());
    },
    
    put: function(url, data) {
        return fetch(QuantTradeX.config.apiUrl + url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        }).then(response => response.json());
    },
    
    delete: function(url) {
        return fetch(QuantTradeX.config.apiUrl + url, {
            method: 'DELETE'
        }).then(response => response.json());
    }
};

// 工具函数
QuantTradeX.utils = {
    formatNumber: function(num, decimals = 2) {
        return Number(num).toFixed(decimals);
    },
    
    formatCurrency: function(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    formatDate: function(date) {
        return new Date(date).toLocaleDateString();
    },
    
    formatDateTime: function(date) {
        return new Date(date).toLocaleString();
    }
};
