/**
 * 用户认证和状态管理通用JavaScript库
 * 用于QuantTradeX平台的用户登录状态检查和导航栏更新
 */

// 全局用户状态
let currentUser = null;

/**
 * 检查用户登录状态
 * @param {boolean} redirectIfNotLoggedIn - 如果未登录是否重定向到首页
 */
async function checkLoginStatus(redirectIfNotLoggedIn = false) {
    try {
        const response = await fetch('/api/user/status', {
            method: 'GET',
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.logged_in) {
                currentUser = data.user;
                updateNavbarForLoggedInUser(data.user);
                showMemberFeatures();
                return true;
            } else {
                currentUser = null;
                updateNavbarForGuest();
                hideMemberFeatures();
                if (redirectIfNotLoggedIn) {
                    window.location.href = '/';
                }
                return false;
            }
        } else {
            currentUser = null;
            updateNavbarForGuest();
            hideMemberFeatures();
            if (redirectIfNotLoggedIn) {
                window.location.href = '/';
            }
            return false;
        }
    } catch (error) {
        console.error('检查登录状态失败:', error);
        currentUser = null;
        updateNavbarForGuest();
        hideMemberFeatures();
        if (redirectIfNotLoggedIn) {
            window.location.href = '/';
        }
        return false;
    }
}

/**
 * 更新导航栏为已登录用户状态
 * @param {Object} user - 用户信息对象
 */
function updateNavbarForLoggedInUser(user) {
    const navbarUser = document.getElementById('navbarUser');
    if (!navbarUser) return;
    
    navbarUser.innerHTML = `
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-user-circle me-1"></i>${user.username}
            </a>
            <ul class="dropdown-menu dropdown-menu-end" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                <li><a class="dropdown-item text-white" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                <li><a class="dropdown-item text-white" href="/my-strategies"><i class="fas fa-code me-2"></i>我的策略</a></li>
                <li><a class="dropdown-item text-white" href="/settings"><i class="fas fa-cog me-2"></i>设置</a></li>
                <li><hr class="dropdown-divider" style="border-color: var(--glass-border);"></li>
                <li><a class="dropdown-item text-white" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
            </ul>
        </li>
    `;
}

/**
 * 更新导航栏为访客状态
 */
function updateNavbarForGuest() {
    const navbarUser = document.getElementById('navbarUser');
    if (!navbarUser) return;
    
    navbarUser.innerHTML = `
        <li class="nav-item">
            <a class="nav-link" href="#" onclick="showLogin()">
                <i class="fas fa-sign-in-alt me-1"></i>登录
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="#" onclick="showRegister()">
                <i class="fas fa-user-plus me-1"></i>注册
            </a>
        </li>
    `;
}

/**
 * 显示会员功能
 */
function showMemberFeatures() {
    const strategyEditorNav = document.getElementById('strategyEditorNav');
    const strategyEditorBtn = document.getElementById('strategyEditorBtn');
    if (strategyEditorNav) strategyEditorNav.style.display = 'block';
    if (strategyEditorBtn) strategyEditorBtn.style.display = 'block';
}

/**
 * 隐藏会员功能
 */
function hideMemberFeatures() {
    const strategyEditorNav = document.getElementById('strategyEditorNav');
    const strategyEditorBtn = document.getElementById('strategyEditorBtn');
    if (strategyEditorNav) strategyEditorNav.style.display = 'none';
    if (strategyEditorBtn) strategyEditorBtn.style.display = 'none';
}

/**
 * 显示登录模态框
 */
function showLogin() {
    const loginModal = document.getElementById('loginModal');
    if (loginModal) {
        const modal = new bootstrap.Modal(loginModal);
        modal.show();
    } else {
        // 如果页面没有登录模态框，跳转到首页
        window.location.href = '/#login';
    }
}

/**
 * 显示注册模态框
 */
function showRegister() {
    const registerModal = document.getElementById('registerModal');
    if (registerModal) {
        const modal = new bootstrap.Modal(registerModal);
        modal.show();
    } else {
        // 如果页面没有注册模态框，跳转到首页
        window.location.href = '/#register';
    }
}

/**
 * 执行登录
 */
async function performLogin() {
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe')?.checked || false;

    if (!email || !password) {
        showNotification('请填写完整的登录信息', 'error');
        return;
    }

    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                email: email,
                password: password,
                remember_me: rememberMe
            })
        });

        const data = await response.json();

        if (response.ok) {
            showNotification('登录成功！', 'success');
            currentUser = data.user;
            updateNavbarForLoggedInUser(data.user);
            showMemberFeatures();
            
            // 关闭模态框
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            if (loginModal) loginModal.hide();
            
            // 清空表单
            const loginForm = document.getElementById('loginForm');
            if (loginForm) loginForm.reset();
        } else {
            showNotification(data.message || '登录失败', 'error');
        }
    } catch (error) {
        console.error('登录错误:', error);
        showNotification('登录失败，请稍后重试', 'error');
    }
}

/**
 * 执行注册
 */
async function performRegister() {
    const username = document.getElementById('registerUsername').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword')?.value;

    if (!username || !email || !password) {
        showNotification('请填写完整的注册信息', 'error');
        return;
    }

    if (confirmPassword && password !== confirmPassword) {
        showNotification('两次输入的密码不一致', 'error');
        return;
    }

    try {
        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                email: email,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            showNotification('注册成功！请登录', 'success');
            
            // 关闭注册模态框
            const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
            if (registerModal) registerModal.hide();
            
            // 清空表单
            const registerForm = document.getElementById('registerForm');
            if (registerForm) registerForm.reset();
            
            // 显示登录模态框
            setTimeout(() => {
                showLogin();
            }, 500);
        } else {
            showNotification(data.message || '注册失败', 'error');
        }
    } catch (error) {
        console.error('注册错误:', error);
        showNotification('注册失败，请稍后重试', 'error');
    }
}

/**
 * 退出登录
 */
async function logout() {
    try {
        const response = await fetch('/api/auth/logout', {
            method: 'POST',
            credentials: 'include'
        });

        if (response.ok) {
            currentUser = null;
            updateNavbarForGuest();
            hideMemberFeatures();
            showNotification('已退出登录', 'success');
            
            // 如果在需要登录的页面，重定向到首页
            const protectedPages = ['/profile', '/my-strategies', '/settings'];
            if (protectedPages.some(page => window.location.pathname.startsWith(page))) {
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            }
        } else {
            showNotification('退出登录失败', 'error');
        }
    } catch (error) {
        console.error('退出登录错误:', error);
        showNotification('退出登录失败', 'error');
    }
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        border: none;
        border-radius: 10px;
    `;

    notification.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// 页面加载时自动检查登录状态
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否是需要登录的页面
    const protectedPages = ['/profile', '/my-strategies', '/settings'];
    const requiresLogin = protectedPages.some(page => window.location.pathname.startsWith(page));
    
    checkLoginStatus(requiresLogin);
});
