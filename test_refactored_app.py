#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重构后应用测试脚本
验证所有核心功能是否正常工作
"""

import requests
import json
import time

def test_app():
    """测试重构后的应用"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 开始测试重构后的QuantTradeX应用...")
    print("=" * 50)
    
    # 测试1: 主页访问
    print("1. 测试主页访问...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("   ✅ 主页访问正常")
        else:
            print(f"   ❌ 主页访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 主页访问异常: {e}")
    
    # 测试2: 认证API
    print("2. 测试认证API...")
    try:
        response = requests.get(f"{base_url}/auth/check")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 认证API正常: {data}")
        else:
            print(f"   ❌ 认证API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 认证API异常: {e}")
    
    # 测试3: 股票数据API
    print("3. 测试股票数据API...")
    try:
        response = requests.get(f"{base_url}/api/stock/AAPL", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ 股票数据API正常")
            else:
                print(f"   ⚠️ 股票数据API返回错误: {data.get('error')}")
        else:
            print(f"   ❌ 股票数据API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 股票数据API异常: {e}")
    
    # 测试4: 论坛API
    print("4. 测试论坛API...")
    try:
        response = requests.get(f"{base_url}/api/forum/posts")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ 论坛API正常，帖子数量: {data.get('total', 0)}")
            else:
                print(f"   ❌ 论坛API返回错误: {data.get('error')}")
        else:
            print(f"   ❌ 论坛API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 论坛API异常: {e}")
    
    # 测试5: 策略API
    print("5. 测试策略API...")
    try:
        response = requests.get(f"{base_url}/api/strategies/1")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ 策略API正常")
            else:
                print(f"   ❌ 策略API返回错误: {data.get('error')}")
        else:
            print(f"   ❌ 策略API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 策略API异常: {e}")
    
    # 测试6: 页面路由
    print("6. 测试页面路由...")
    pages = [
        ("/strategies", "策略市场"),
        ("/backtest", "回测系统"),
        ("/forum", "社区论坛"),
        ("/realtime", "实时数据"),
        ("/datacenter", "数据中心")
    ]
    
    for path, name in pages:
        try:
            response = requests.get(f"{base_url}{path}")
            if response.status_code == 200:
                print(f"   ✅ {name}页面正常")
            else:
                print(f"   ❌ {name}页面失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}页面异常: {e}")
    
    print("=" * 50)
    print("🎉 测试完成！")

if __name__ == "__main__":
    test_app()
