<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉框修复验证 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --text-primary: #f8fafc;
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            padding: 2rem;
        }

        .card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            margin-bottom: 2rem;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">下拉框修复验证</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>修复后的下拉框测试</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">资产类别</label>
                            <select class="form-select">
                                <option value="">请选择资产类别</option>
                                <option value="stocks">股票</option>
                                <option value="futures">期货</option>
                                <option value="crypto">加密货币</option>
                                <option value="forex">外汇</option>
                                <option value="gold">黄金</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">策略类型</label>
                            <select class="form-select">
                                <option value="">请选择策略类型</option>
                                <option value="trend">趋势跟踪</option>
                                <option value="mean_reversion">均值回归</option>
                                <option value="momentum">动量策略</option>
                                <option value="arbitrage">套利策略</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success">
                    ✅ 如果您能清楚看到下拉框选项的文字，说明修复成功！
                </div>
            </div>
        </div>
    </div>
</body>
</html>