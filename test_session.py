#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试会话管理的脚本
"""

import requests
import time

def test_session_persistence():
    """测试会话持久性"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 测试用户会话持久性")
    print("=" * 40)
    
    # 创建会话
    session = requests.Session()
    
    # 1. 测试注册
    print("1. 测试用户注册...")
    register_data = {
        "username": "sessiontest",
        "email": "<EMAIL>",
        "password": "test123456",
        "full_name": "会话测试用户"
    }
    
    try:
        response = session.post(f"{base_url}/auth/register", json=register_data, timeout=10)
        result = response.json()
        print(f"   注册结果: {result.get('success', False)}")
        if result.get('success'):
            print(f"   用户: {result.get('user', {}).get('username')}")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   注册异常: {e}")
        return
    
    # 2. 立即检查认证状态
    print("\n2. 注册后立即检查认证状态...")
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        print(f"   认证状态: {result.get('authenticated', False)}")
        if result.get('authenticated'):
            print(f"   用户信息: {result.get('user', {}).get('username')}")
        else:
            print("   用户未认证")
    except Exception as e:
        print(f"   检查认证状态异常: {e}")
    
    # 3. 等待5秒后再次检查
    print("\n3. 等待5秒后再次检查...")
    time.sleep(5)
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        print(f"   认证状态: {result.get('authenticated', False)}")
        if result.get('authenticated'):
            print(f"   用户信息: {result.get('user', {}).get('username')}")
        else:
            print("   ❌ 用户会话已丢失！")
    except Exception as e:
        print(f"   检查认证状态异常: {e}")
    
    # 4. 测试访问需要认证的API
    print("\n4. 测试访问用户资料...")
    try:
        response = session.get(f"{base_url}/auth/profile", timeout=5)
        result = response.json()
        print(f"   访问结果: {result.get('success', False)}")
        if not result.get('success'):
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   访问资料异常: {e}")
    
    # 5. 检查Cookie
    print("\n5. 检查会话Cookie...")
    cookies = session.cookies
    print(f"   Cookie数量: {len(cookies)}")
    for cookie in cookies:
        print(f"   Cookie: {cookie.name} = {cookie.value[:20]}...")
    
    # 6. 测试手动登录
    print("\n6. 测试手动登录...")
    login_data = {
        "username": "sessiontest",
        "password": "test123456"
    }
    
    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        print(f"   登录结果: {result.get('success', False)}")
        if result.get('success'):
            print(f"   用户: {result.get('user', {}).get('username')}")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   登录异常: {e}")
    
    # 7. 登录后再次检查认证状态
    print("\n7. 登录后检查认证状态...")
    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        print(f"   认证状态: {result.get('authenticated', False)}")
        if result.get('authenticated'):
            print(f"   用户信息: {result.get('user', {}).get('username')}")
        else:
            print("   ❌ 登录后仍未认证！")
    except Exception as e:
        print(f"   检查认证状态异常: {e}")

if __name__ == "__main__":
    test_session_persistence()
