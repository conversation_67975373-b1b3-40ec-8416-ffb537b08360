#!/bin/bash

# QuantTradeX 部署验证脚本
# 验证所有组件是否正确部署和运行

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印带颜色的消息
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "✅ ${GREEN}$message${NC}"
    elif [ "$status" = "WARN" ]; then
        echo -e "⚠️  ${YELLOW}$message${NC}"
    elif [ "$status" = "ERROR" ]; then
        echo -e "❌ ${RED}$message${NC}"
    else
        echo -e "ℹ️  ${BLUE}$message${NC}"
    fi
}

echo "🔍 QuantTradeX 部署验证开始..."
echo "=================================="

# 1. 检查项目目录
if [ -d "/www/wwwroot/www.gdpp.com" ]; then
    print_status "OK" "项目目录存在: /www/wwwroot/www.gdpp.com"
else
    print_status "ERROR" "项目目录不存在"
    exit 1
fi

# 2. 检查虚拟环境
if [ -d "/www/wwwroot/www.gdpp.com/venv" ]; then
    print_status "OK" "Python虚拟环境存在"
    
    # 检查Python版本
    python_version=$(/www/wwwroot/www.gdpp.com/venv/bin/python --version 2>&1)
    print_status "INFO" "Python版本: $python_version"
else
    print_status "ERROR" "Python虚拟环境不存在"
    exit 1
fi

# 3. 检查主应用文件
if [ -f "/www/wwwroot/www.gdpp.com/app.py" ]; then
    print_status "OK" "主应用文件存在: app.py"
else
    print_status "ERROR" "主应用文件不存在"
    exit 1
fi

# 4. 检查systemd服务文件
if [ -f "/etc/systemd/system/quanttradex.service" ]; then
    print_status "OK" "systemd服务文件存在"
else
    print_status "ERROR" "systemd服务文件不存在"
    exit 1
fi

# 5. 检查服务状态
service_status=$(sudo systemctl is-active quanttradex.service 2>/dev/null)
if [ "$service_status" = "active" ]; then
    print_status "OK" "quanttradex服务正在运行"
else
    print_status "ERROR" "quanttradex服务未运行 (状态: $service_status)"
fi

# 6. 检查自动启动
auto_start=$(sudo systemctl is-enabled quanttradex.service 2>/dev/null)
if [ "$auto_start" = "enabled" ]; then
    print_status "OK" "自动启动已启用"
else
    print_status "WARN" "自动启动未启用 (状态: $auto_start)"
fi

# 7. 检查端口监听
port_check=$(netstat -tlnp 2>/dev/null | grep :5000)
if [ -n "$port_check" ]; then
    print_status "OK" "端口5000正在监听"
    echo "   $port_check"
else
    print_status "ERROR" "端口5000未监听"
fi

# 8. 检查HTTP响应
echo ""
print_status "INFO" "测试HTTP连接..."
http_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000 2>/dev/null)
if [ "$http_response" = "200" ]; then
    print_status "OK" "HTTP响应正常 (200)"
else
    print_status "ERROR" "HTTP响应异常 ($http_response)"
fi

# 9. 检查关键依赖
echo ""
print_status "INFO" "检查Python依赖..."

dependencies=("flask" "requests" "pandas" "numpy" "yfinance")
for dep in "${dependencies[@]}"; do
    if /www/wwwroot/www.gdpp.com/venv/bin/python -c "import $dep" 2>/dev/null; then
        print_status "OK" "依赖 $dep 已安装"
    else
        print_status "WARN" "依赖 $dep 未安装或有问题"
    fi
done

# 10. 检查管理脚本
if [ -f "/www/wwwroot/www.gdpp.com/manage_service.sh" ] && [ -x "/www/wwwroot/www.gdpp.com/manage_service.sh" ]; then
    print_status "OK" "服务管理脚本存在且可执行"
else
    print_status "WARN" "服务管理脚本不存在或不可执行"
fi

# 11. 检查日志
echo ""
print_status "INFO" "最近的服务日志:"
sudo journalctl -u quanttradex.service -n 3 --no-pager 2>/dev/null | tail -3

# 12. 系统资源使用情况
echo ""
print_status "INFO" "系统资源使用情况:"
memory_usage=$(ps aux | grep "python app.py" | grep -v grep | awk '{print $6}' | head -1)
if [ -n "$memory_usage" ]; then
    memory_mb=$((memory_usage / 1024))
    print_status "INFO" "内存使用: ${memory_mb}MB"
fi

cpu_usage=$(ps aux | grep "python app.py" | grep -v grep | awk '{print $3}' | head -1)
if [ -n "$cpu_usage" ]; then
    print_status "INFO" "CPU使用: ${cpu_usage}%"
fi

# 总结
echo ""
echo "=================================="
print_status "INFO" "验证完成！"

# 检查是否有错误
if grep -q "❌" /tmp/verify_output 2>/dev/null; then
    print_status "WARN" "发现一些问题，请检查上述错误信息"
    echo ""
    echo "🔧 常用修复命令:"
    echo "   sudo systemctl start quanttradex.service    # 启动服务"
    echo "   sudo systemctl enable quanttradex.service   # 启用自动启动"
    echo "   ./manage_service.sh status                   # 查看详细状态"
    echo "   ./manage_service.sh logs                     # 查看日志"
else
    print_status "OK" "所有检查通过！QuantTradeX已成功部署并运行"
    echo ""
    echo "🌐 访问地址:"
    echo "   本地: http://localhost:5000"
    echo "   网络: http://$(hostname -I | awk '{print $1}'):5000"
    echo ""
    echo "🛠️ 管理命令:"
    echo "   ./manage_service.sh status    # 查看状态"
    echo "   ./manage_service.sh restart   # 重启服务"
    echo "   ./manage_service.sh logs      # 查看日志"
fi

echo ""
