# QuantTradeX 智能数据生成器使用说明

## 🎯 功能概述

智能数据生成器使用大模型API自动生成真实的模拟内容，包括：

- 📝 **论坛帖子**：专业的量化交易讨论内容
- 💻 **策略代码**：各种量化交易策略的完整Python代码
- 💬 **用户评论**：真实的用户互动评论
- 📊 **其他数据**：用户资料、交易记录等

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install requests python-dotenv
```

### 2. 配置API密钥

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，添加你的API密钥：
```bash
# 选择一个或多个API提供商
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here  
CLAUDE_API_KEY=your_claude_api_key_here
```

### 3. 运行数据生成器

```bash
python data_generator.py
```

## 🔧 API提供商配置

### DeepSeek API（推荐）
- **优势**：性价比高，中文支持好
- **获取方式**：访问 https://platform.deepseek.com/
- **费用**：相对便宜

### OpenAI API
- **优势**：质量稳定，功能强大
- **获取方式**：访问 https://platform.openai.com/
- **费用**：中等

### Claude API
- **优势**：逻辑性强，安全性高
- **获取方式**：访问 https://console.anthropic.com/
- **费用**：较高

## 📁 生成的数据结构

运行后会在 `generated_data/` 目录下生成：

```
generated_data/
├── forum_posts.json      # 论坛帖子数据
├── strategy_codes.json   # 策略代码数据
└── user_comments.json    # 用户评论数据
```

### 论坛帖子数据格式
```json
{
  "id": 1,
  "title": "移动平均策略的优化技巧",
  "content": "详细的帖子内容...",
  "author": "trader_1234",
  "created_at": "2025-01-27T10:30:00",
  "views": 156,
  "replies": 12,
  "likes": 8,
  "category": "策略讨论"
}
```

### 策略代码数据格式
```json
{
  "moving_average_crossover": "# 移动平均交叉策略\nimport pandas as pd...",
  "rsi_mean_reversion": "# RSI均值回归策略\nimport numpy as np...",
  "bollinger_bands": "# 布林带策略\nclass BollingerBandsStrategy..."
}
```

## 🔄 集成到QuantTradeX

### 1. 更新论坛数据

生成的论坛帖子可以直接集成到 `app.py` 中：

```python
# 在app.py中添加
def load_generated_forum_posts():
    try:
        with open('generated_data/forum_posts.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return []

# 更新论坛API
@app.route('/api/forum/posts')
def get_forum_posts():
    posts = load_generated_forum_posts()
    return jsonify({'success': True, 'posts': posts})
```

### 2. 更新策略代码

```python
# 加载生成的策略代码
def load_generated_strategy_codes():
    try:
        with open('generated_data/strategy_codes.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return {}

# 更新策略详情API
@app.route('/api/strategy/<strategy_id>')
def get_strategy_detail(strategy_id):
    codes = load_generated_strategy_codes()
    # 根据strategy_id返回对应的代码
    return jsonify({'success': True, 'code': codes.get(strategy_id, '')})
```

## ⚙️ 自定义配置

### 修改生成数量

在 `data_generator.py` 中修改：

```python
# 生成20个论坛帖子
self.generate_forum_posts(20)

# 生成50个用户评论  
self.generate_user_comments(50)
```

### 切换API提供商

```python
# 在__init__方法中修改
self.current_api = 'deepseek'  # 或 'openai', 'claude'
```

### 添加新的内容类型

```python
def generate_trading_signals(self, count: int = 30):
    """生成交易信号数据"""
    # 实现新的生成逻辑
    pass
```

## 🔍 故障排除

### API调用失败
- 检查API密钥是否正确
- 确认账户余额充足
- 检查网络连接

### 生成内容质量不佳
- 调整prompt提示词
- 修改temperature参数
- 尝试不同的API提供商

### 文件保存失败
- 检查目录权限
- 确保磁盘空间充足

## 📈 使用建议

1. **首次使用**：建议先用少量数据测试
2. **API选择**：DeepSeek性价比最高，适合大量生成
3. **内容质量**：可以多次生成，选择质量最好的内容
4. **成本控制**：设置合理的生成数量，避免过度消费

## 🔄 定期更新

建议定期运行数据生成器，保持内容的新鲜度：

```bash
# 每周运行一次
0 0 * * 0 cd /path/to/qclb.com && python data_generator.py
```

## 📞 技术支持

如有问题，请检查：
1. API密钥配置
2. 网络连接状态  
3. 生成的日志输出
4. 生成的数据文件格式

---

**注意**：请合理使用API资源，避免过度调用导致费用过高。建议在测试阶段使用较小的生成数量。
