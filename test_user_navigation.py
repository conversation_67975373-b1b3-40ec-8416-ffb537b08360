#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试用户导航和论坛功能的脚本
"""

import requests
import time
import random

def test_user_navigation_and_forum():
    """测试用户导航和论坛功能"""
    base_url = "http://127.0.0.1:5001"

    print("🔄 测试用户导航和论坛功能")
    print("=" * 60)

    # 创建会话
    session = requests.Session()

    # 1. 测试论坛API端点
    print("1. 测试论坛API端点...")

    # 测试论坛帖子列表
    try:
        response = session.get(f"{base_url}/api/forum/posts", timeout=10)
        result = response.json()
        print(f"   论坛帖子列表: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        if result.get('success'):
            posts = result.get('posts', [])
            print(f"   获取到 {len(posts)} 个帖子")
            print(f"   总帖子数: {result.get('total')}")
            print(f"   分页信息: 第{result.get('page')}页，共{result.get('pages')}页")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   论坛帖子列表异常: {e}")

    # 测试论坛帖子详情
    try:
        response = session.get(f"{base_url}/api/forum/posts/1", timeout=10)
        result = response.json()
        print(f"   论坛帖子详情: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        if result.get('success'):
            post = result.get('post', {})
            print(f"   帖子标题: {post.get('title')}")
            print(f"   帖子作者: {post.get('author')}")
            print(f"   帖子分类: {post.get('category')}")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   论坛帖子详情异常: {e}")

    # 测试论坛统计
    try:
        response = session.get(f"{base_url}/api/forum/stats", timeout=10)
        result = response.json()
        print(f"   论坛统计: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        if result.get('success'):
            stats = result.get('stats', {})
            print(f"   总帖子数: {stats.get('total_posts')}")
            print(f"   总用户数: {stats.get('total_users')}")
            print(f"   总浏览量: {stats.get('total_views')}")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   论坛统计异常: {e}")

    # 测试热门话题
    try:
        response = session.get(f"{base_url}/api/forum/hot-topics", timeout=10)
        result = response.json()
        print(f"   热门话题: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        if result.get('success'):
            topics = result.get('hot_topics', [])
            print(f"   获取到 {len(topics)} 个热门话题")
            for topic in topics[:3]:  # 显示前3个
                print(f"     - {topic.get('tag')}: {topic.get('count')} ({topic.get('trend')})")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   热门话题异常: {e}")

    # 2. 测试用户登录和页面访问
    print("\n2. 测试用户登录和页面访问...")

    # 登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    try:
        response = session.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        result = response.json()
        print(f"   用户登录: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        if result.get('success'):
            user = result.get('user', {})
            print(f"   用户信息: {user.get('username')} ({user.get('role')})")
            print(f"   重定向URL: {result.get('redirect_url')}")
        else:
            print(f"   登录错误: {result.get('error')}")
            return
    except Exception as e:
        print(f"   登录异常: {e}")
        return

    # 3. 测试各页面的用户状态检查
    print("\n3. 测试各页面的用户状态检查...")

    pages_to_test = [
        ("/", "首页"),
        ("/dashboard", "仪表板"),
        ("/strategies", "策略市场"),
        ("/forum", "社区论坛"),
        ("/backtest", "回测系统"),
        ("/realtime", "实时数据")
    ]

    for url, name in pages_to_test:
        try:
            response = session.get(f"{base_url}{url}", timeout=10)
            status = "✅ 成功" if response.status_code == 200 else f"❌ 失败 ({response.status_code})"
            print(f"   {name}: {status}")

            # 检查页面是否包含用户信息相关的内容
            if response.status_code == 200:
                content = response.text
                has_user_nav = 'navbarUser' in content
                has_login_check = 'checkLoginStatus' in content
                print(f"     - 用户导航栏: {'✅' if has_user_nav else '❌'}")
                print(f"     - 登录状态检查: {'✅' if has_login_check else '❌'}")
        except Exception as e:
            print(f"   {name}: ❌ 异常 ({e})")

    # 4. 测试用户状态检查API
    print("\n4. 测试用户状态检查API...")

    try:
        response = session.get(f"{base_url}/auth/check", timeout=5)
        result = response.json()
        authenticated = result.get('authenticated', False)
        print(f"   认证状态检查: {'✅ 已认证' if authenticated else '❌ 未认证'}")
        if authenticated:
            user = result.get('user', {})
            print(f"   当前用户: {user.get('username')}")
            print(f"   VIP状态: {'✅ VIP' if user.get('is_premium') else '❌ 普通用户'}")
        else:
            print("   ❌ 用户状态检查失败！")
    except Exception as e:
        print(f"   认证状态检查异常: {e}")

    # 5. 测试论坛页面的分类筛选
    print("\n5. 测试论坛页面的分类筛选...")

    categories = ['general', 'strategy', 'market', 'help', 'news']
    for category in categories:
        try:
            response = session.get(f"{base_url}/api/forum/posts?category={category}", timeout=5)
            result = response.json()
            success = result.get('success', False)
            print(f"   分类 {category}: {'✅ 成功' if success else '❌ 失败'}")
            if success:
                posts = result.get('posts', [])
                print(f"     获取到 {len(posts)} 个帖子")
        except Exception as e:
            print(f"   分类 {category}: ❌ 异常 ({e})")

    # 6. 测试退出登录
    print("\n6. 测试退出登录...")

    try:
        response = session.post(f"{base_url}/auth/logout", json={}, timeout=5)
        result = response.json()
        success = result.get('success', False)
        print(f"   退出登录: {'✅ 成功' if success else '❌ 失败'}")
        if success:
            print(f"   消息: {result.get('message')}")

            # 验证退出状态
            check_response = session.get(f"{base_url}/auth/check", timeout=5)
            check_result = check_response.json()
            if not check_result.get('authenticated'):
                print("   ✅ 退出登录验证成功")
            else:
                print("   ❌ 退出登录验证失败")
        else:
            print(f"   错误: {result.get('error')}")
    except Exception as e:
        print(f"   退出登录异常: {e}")

    print("\n" + "=" * 60)
    print("🎯 用户导航和论坛功能测试完成")

if __name__ == "__main__":
    test_user_navigation_and_forum()
