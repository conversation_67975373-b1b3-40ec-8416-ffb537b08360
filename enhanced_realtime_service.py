#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版实时数据服务
提升稳定性、性能和错误处理
"""

import threading
import time
import json
import logging
import queue
import random
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Any
import yfinance as yf
import requests
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import redis
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: str
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None

@dataclass
class ConnectionInfo:
    """连接信息结构"""
    sid: str
    user_info: Optional[Dict]
    connected_at: datetime
    last_ping: datetime
    subscriptions: Set[str]
    is_active: bool = True

class DataProviderManager:
    """数据提供商管理器"""
    
    def __init__(self):
        self.providers = {
            'yahoo': self._get_yahoo_data,
            'alpha_vantage': self._get_alpha_vantage_data,
            'mock': self._get_mock_data
        }
        self.current_provider = 'yahoo'
        self.fallback_providers = ['mock']
        self.provider_status = {}
        self.rate_limits = {
            'yahoo': {'calls': 0, 'reset_time': time.time() + 3600},
            'alpha_vantage': {'calls': 0, 'reset_time': time.time() + 60}
        }
    
    def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """获取市场数据，支持故障转移"""
        for provider in [self.current_provider] + self.fallback_providers:
            try:
                if self._check_rate_limit(provider):
                    data = self.providers[provider](symbol)
                    if data:
                        self.provider_status[provider] = {
                            'status': 'healthy',
                            'last_success': datetime.now(),
                            'error_count': 0
                        }
                        return data
            except Exception as e:
                logger.warning(f"Provider {provider} failed for {symbol}: {e}")
                self._record_provider_error(provider, e)
        
        return None
    
    def _check_rate_limit(self, provider: str) -> bool:
        """检查API频率限制"""
        if provider not in self.rate_limits:
            return True
        
        limit_info = self.rate_limits[provider]
        current_time = time.time()
        
        # 重置计数器
        if current_time > limit_info['reset_time']:
            limit_info['calls'] = 0
            limit_info['reset_time'] = current_time + 3600
        
        # 检查限制
        max_calls = 100 if provider == 'yahoo' else 5
        if limit_info['calls'] >= max_calls:
            return False
        
        limit_info['calls'] += 1
        return True
    
    def _record_provider_error(self, provider: str, error: Exception):
        """记录提供商错误"""
        if provider not in self.provider_status:
            self.provider_status[provider] = {'error_count': 0}
        
        self.provider_status[provider]['error_count'] += 1
        self.provider_status[provider]['last_error'] = str(error)
        self.provider_status[provider]['last_error_time'] = datetime.now()
    
    def _get_yahoo_data(self, symbol: str) -> Optional[MarketData]:
        """从Yahoo Finance获取数据"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period='1d', interval='1m')
            
            if hist.empty:
                return None
            
            latest = hist.iloc[-1]
            previous = hist.iloc[-2] if len(hist) > 1 else latest
            
            change = latest['Close'] - previous['Close']
            change_percent = (change / previous['Close']) * 100 if previous['Close'] != 0 else 0
            
            return MarketData(
                symbol=symbol,
                price=float(latest['Close']),
                change=float(change),
                change_percent=float(change_percent),
                volume=int(latest['Volume']),
                high=float(latest['High']),
                low=float(latest['Low']),
                open=float(latest['Open']),
                timestamp=datetime.now().isoformat()
            )
        except Exception as e:
            logger.error(f"Yahoo Finance error for {symbol}: {e}")
            return None
    
    def _get_alpha_vantage_data(self, symbol: str) -> Optional[MarketData]:
        """从Alpha Vantage获取数据"""
        # 这里可以实现Alpha Vantage API调用
        # 暂时返回None，使用fallback
        return None
    
    def _get_mock_data(self, symbol: str) -> MarketData:
        """生成模拟数据"""
        base_price = 100.0
        if symbol == 'AAPL':
            base_price = 150.0
        elif symbol == 'GOOGL':
            base_price = 2500.0
        elif symbol == 'TSLA':
            base_price = 200.0
        
        # 生成随机波动
        change = random.uniform(-5, 5)
        price = base_price + change
        change_percent = (change / base_price) * 100
        
        return MarketData(
            symbol=symbol,
            price=price,
            change=change,
            change_percent=change_percent,
            volume=random.randint(1000000, 10000000),
            high=price + random.uniform(0, 3),
            low=price - random.uniform(0, 3),
            open=price + random.uniform(-2, 2),
            timestamp=datetime.now().isoformat()
        )

class ConnectionManager:
    """连接管理器"""
    
    def __init__(self):
        self.connections: Dict[str, ConnectionInfo] = {}
        self.subscriptions: Dict[str, Set[str]] = defaultdict(set)  # symbol -> sids
        self.connection_lock = threading.RLock()
        self.max_connections = 1000
        self.ping_timeout = 60  # 60秒超时
    
    def add_connection(self, sid: str, user_info: Optional[Dict] = None) -> bool:
        """添加连接"""
        with self.connection_lock:
            if len(self.connections) >= self.max_connections:
                logger.warning(f"Connection limit reached, rejecting {sid}")
                return False
            
            self.connections[sid] = ConnectionInfo(
                sid=sid,
                user_info=user_info,
                connected_at=datetime.now(),
                last_ping=datetime.now(),
                subscriptions=set()
            )
            
            logger.info(f"Connection added: {sid}, total: {len(self.connections)}")
            return True
    
    def remove_connection(self, sid: str):
        """移除连接"""
        with self.connection_lock:
            if sid in self.connections:
                # 清理订阅
                conn_info = self.connections[sid]
                for symbol in conn_info.subscriptions:
                    self.subscriptions[symbol].discard(sid)
                    if not self.subscriptions[symbol]:
                        del self.subscriptions[symbol]
                
                del self.connections[sid]
                logger.info(f"Connection removed: {sid}, total: {len(self.connections)}")
    
    def subscribe_symbol(self, sid: str, symbol: str) -> bool:
        """订阅股票"""
        with self.connection_lock:
            if sid not in self.connections:
                return False
            
            self.connections[sid].subscriptions.add(symbol)
            self.subscriptions[symbol].add(sid)
            return True
    
    def unsubscribe_symbol(self, sid: str, symbol: str):
        """取消订阅"""
        with self.connection_lock:
            if sid in self.connections:
                self.connections[sid].subscriptions.discard(symbol)
                self.subscriptions[symbol].discard(sid)
                if not self.subscriptions[symbol]:
                    del self.subscriptions[symbol]
    
    def update_ping(self, sid: str):
        """更新心跳时间"""
        with self.connection_lock:
            if sid in self.connections:
                self.connections[sid].last_ping = datetime.now()
    
    def cleanup_stale_connections(self):
        """清理过期连接"""
        current_time = datetime.now()
        stale_sids = []
        
        with self.connection_lock:
            for sid, conn_info in self.connections.items():
                if (current_time - conn_info.last_ping).seconds > self.ping_timeout:
                    stale_sids.append(sid)
        
        for sid in stale_sids:
            self.remove_connection(sid)
            logger.info(f"Removed stale connection: {sid}")
    
    def get_subscribers(self, symbol: str) -> Set[str]:
        """获取股票订阅者"""
        return self.subscriptions.get(symbol, set()).copy()
    
    def get_stats(self) -> Dict:
        """获取连接统计"""
        with self.connection_lock:
            return {
                'total_connections': len(self.connections),
                'total_subscriptions': sum(len(subs) for subs in self.subscriptions.values()),
                'unique_symbols': len(self.subscriptions),
                'active_symbols': list(self.subscriptions.keys())
            }

class EnhancedRealtimeService:
    """增强版实时数据服务"""
    
    def __init__(self, socketio=None, redis_client=None):
        self.socketio = socketio
        self.redis_client = redis_client
        self.data_provider = DataProviderManager()
        self.connection_manager = ConnectionManager()
        
        # 数据缓存
        self.data_cache: Dict[str, MarketData] = {}
        self.cache_ttl = 5  # 5秒缓存
        self.cache_timestamps: Dict[str, datetime] = {}
        
        # 线程控制
        self.running = False
        self.data_thread = None
        self.cleanup_thread = None
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # 性能监控
        self.metrics = {
            'data_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'broadcast_count': 0,
            'error_count': 0
        }
    
    def start_service(self):
        """启动服务"""
        if self.running:
            return
        
        self.running = True
        
        # 启动数据更新线程
        self.data_thread = threading.Thread(target=self._data_update_loop, daemon=True)
        self.data_thread.start()
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        
        logger.info("Enhanced realtime service started")
    
    def stop_service(self):
        """停止服务"""
        self.running = False
        if self.data_thread:
            self.data_thread.join(timeout=5)
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
        self.executor.shutdown(wait=True)
        logger.info("Enhanced realtime service stopped")
    
    def _data_update_loop(self):
        """数据更新循环"""
        while self.running:
            try:
                # 获取所有需要更新的股票
                symbols = list(self.connection_manager.subscriptions.keys())
                
                if symbols:
                    # 并行获取数据
                    futures = []
                    for symbol in symbols:
                        future = self.executor.submit(self._update_symbol_data, symbol)
                        futures.append((symbol, future))
                    
                    # 收集结果并广播
                    for symbol, future in futures:
                        try:
                            data = future.result(timeout=5)
                            if data:
                                self._broadcast_data(symbol, data)
                        except Exception as e:
                            logger.error(f"Failed to update {symbol}: {e}")
                            self.metrics['error_count'] += 1
                
                time.sleep(2)  # 2秒更新间隔
                
            except Exception as e:
                logger.error(f"Data update loop error: {e}")
                time.sleep(5)
    
    def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                # 清理过期连接
                self.connection_manager.cleanup_stale_connections()
                
                # 清理过期缓存
                self._cleanup_cache()
                
                time.sleep(30)  # 30秒清理一次
                
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                time.sleep(60)
    
    def _update_symbol_data(self, symbol: str) -> Optional[MarketData]:
        """更新单个股票数据"""
        try:
            # 检查缓存
            if self._is_cache_valid(symbol):
                self.metrics['cache_hits'] += 1
                return self.data_cache[symbol]
            
            # 获取新数据
            data = self.data_provider.get_market_data(symbol)
            if data:
                self.data_cache[symbol] = data
                self.cache_timestamps[symbol] = datetime.now()
                self.metrics['cache_misses'] += 1
                
                # 保存到Redis
                if self.redis_client:
                    try:
                        self.redis_client.setex(
                            f"market_data:{symbol}",
                            self.cache_ttl,
                            json.dumps(asdict(data))
                        )
                    except Exception as e:
                        logger.warning(f"Redis save failed: {e}")
            
            self.metrics['data_requests'] += 1
            return data
            
        except Exception as e:
            logger.error(f"Update symbol data error for {symbol}: {e}")
            return None
    
    def _is_cache_valid(self, symbol: str) -> bool:
        """检查缓存是否有效"""
        if symbol not in self.data_cache or symbol not in self.cache_timestamps:
            return False
        
        age = (datetime.now() - self.cache_timestamps[symbol]).seconds
        return age < self.cache_ttl
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        current_time = datetime.now()
        expired_symbols = []
        
        for symbol, timestamp in self.cache_timestamps.items():
            if (current_time - timestamp).seconds > self.cache_ttl * 2:
                expired_symbols.append(symbol)
        
        for symbol in expired_symbols:
            self.data_cache.pop(symbol, None)
            self.cache_timestamps.pop(symbol, None)
    
    def _broadcast_data(self, symbol: str, data: MarketData):
        """广播数据到订阅者"""
        if not self.socketio:
            return
        
        subscribers = self.connection_manager.get_subscribers(symbol)
        if not subscribers:
            return
        
        try:
            # 准备广播数据
            broadcast_data = {
                'symbol': data.symbol,
                'price': data.price,
                'change': data.change,
                'change_percent': data.change_percent,
                'volume': data.volume,
                'timestamp': data.timestamp,
                'high': data.high,
                'low': data.low,
                'open': data.open
            }
            
            # 广播到所有订阅者
            for sid in subscribers:
                try:
                    self.socketio.emit('market_data', broadcast_data, room=sid)
                except Exception as e:
                    logger.warning(f"Failed to send data to {sid}: {e}")
            
            self.metrics['broadcast_count'] += 1
            
        except Exception as e:
            logger.error(f"Broadcast error for {symbol}: {e}")
    
    # 连接管理方法
    def add_connection(self, sid: str, user_info: Optional[Dict] = None) -> bool:
        return self.connection_manager.add_connection(sid, user_info)
    
    def remove_connection(self, sid: str):
        self.connection_manager.remove_connection(sid)
    
    def subscribe_symbol(self, sid: str, symbol: str, data_type: str = 'stock') -> bool:
        return self.connection_manager.subscribe_symbol(sid, symbol.upper())
    
    def unsubscribe_symbol(self, sid: str, symbol: str, data_type: str = 'stock'):
        self.connection_manager.unsubscribe_symbol(sid, symbol.upper())
    
    def update_ping(self, sid: str):
        self.connection_manager.update_ping(sid)
    
    def get_connection_stats(self) -> Dict:
        """获取连接统计"""
        stats = self.connection_manager.get_stats()
        stats.update({
            'performance_metrics': self.metrics,
            'provider_status': self.data_provider.provider_status,
            'cache_size': len(self.data_cache)
        })
        return stats
