#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantTradeX 下拉框样式修复脚本
统一修复所有页面的下拉框颜色问题
"""

import os
import re
from typing import List

class DropdownStyleFixer:
    def __init__(self):
        self.templates_dir = 'templates'
        self.fixed_files = []
        
        # 标准的下拉框样式修复CSS
        self.dropdown_fix_css = '''
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }'''
    
    def get_html_files(self) -> List[str]:
        """获取所有HTML文件"""
        html_files = []
        
        # 模板目录中的文件
        if os.path.exists(self.templates_dir):
            for file in os.listdir(self.templates_dir):
                if file.endswith('.html'):
                    html_files.append(os.path.join(self.templates_dir, file))
        
        # 根目录中的HTML文件
        for file in os.listdir('.'):
            if file.endswith('.html'):
                html_files.append(file)
        
        return html_files
    
    def check_needs_fix(self, content: str) -> bool:
        """检查文件是否需要修复"""
        # 检查是否有form-select但没有正确的option样式
        has_form_select = '.form-select' in content
        has_option_fix = '.form-select option' in content and 'background-color: var(--dark)' in content
        
        return has_form_select and not has_option_fix
    
    def fix_file(self, file_path: str) -> bool:
        """修复单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否需要修复
            if not self.check_needs_fix(content):
                return False
            
            # 查找</style>标签的位置
            style_end_pattern = r'</style>'
            match = re.search(style_end_pattern, content)
            
            if match:
                # 在</style>前插入修复CSS
                insert_position = match.start()
                new_content = (
                    content[:insert_position] + 
                    '\n        /* 下拉框样式修复 */' +
                    self.dropdown_fix_css + 
                    '\n        ' +
                    content[insert_position:]
                )
                
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                return True
            else:
                print(f"警告：{file_path} 中未找到</style>标签")
                return False
                
        except Exception as e:
            print(f"修复文件 {file_path} 失败: {e}")
            return False
    
    def fix_all_files(self):
        """修复所有文件"""
        print("=" * 50)
        print("QuantTradeX 下拉框样式修复器启动")
        print("=" * 50)
        
        html_files = self.get_html_files()
        print(f"找到 {len(html_files)} 个HTML文件")
        
        for file_path in html_files:
            print(f"检查文件: {file_path}")
            
            if self.fix_file(file_path):
                self.fixed_files.append(file_path)
                print(f"✅ 已修复: {file_path}")
            else:
                print(f"⏭️  跳过: {file_path} (不需要修复或已修复)")
        
        print("=" * 50)
        print(f"修复完成！共修复 {len(self.fixed_files)} 个文件")
        
        if self.fixed_files:
            print("已修复的文件:")
            for file in self.fixed_files:
                print(f"  - {file}")
        
        print("=" * 50)
    
    def create_test_page(self):
        """创建测试页面验证修复效果"""
        test_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉框修复验证 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --text-primary: #f8fafc;
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            padding: 2rem;
        }

        .card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            margin-bottom: 2rem;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">下拉框修复验证</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>修复后的下拉框测试</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">资产类别</label>
                            <select class="form-select">
                                <option value="">请选择资产类别</option>
                                <option value="stocks">股票</option>
                                <option value="futures">期货</option>
                                <option value="crypto">加密货币</option>
                                <option value="forex">外汇</option>
                                <option value="gold">黄金</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">策略类型</label>
                            <select class="form-select">
                                <option value="">请选择策略类型</option>
                                <option value="trend">趋势跟踪</option>
                                <option value="mean_reversion">均值回归</option>
                                <option value="momentum">动量策略</option>
                                <option value="arbitrage">套利策略</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success">
                    ✅ 如果您能清楚看到下拉框选项的文字，说明修复成功！
                </div>
            </div>
        </div>
    </div>
</body>
</html>'''
        
        with open('dropdown_fix_test.html', 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print("已创建测试页面: dropdown_fix_test.html")

if __name__ == "__main__":
    fixer = DropdownStyleFixer()
    fixer.fix_all_files()
    fixer.create_test_page()
