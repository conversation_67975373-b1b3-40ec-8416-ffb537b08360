#!/usr/bin/env python3
# QuantTradeX 论坛数据生成器
# 生成200条丰富的论坛帖子模拟数据

import json
import random
from datetime import datetime, timedelta

class ForumDataGenerator:
    """论坛数据生成器"""
    
    def __init__(self):
        # 用户名池
        self.usernames = [
            'QuantMaster', 'AlgoTrader', 'TechAnalyst', 'RiskManager', 'CryptoKing',
            'MLTrader', 'DataScientist', 'StrategyGuru', 'MarketMaker', 'HedgeFund',
            'RetailTrader', 'InstitutionalInvestor', 'QuantDev', 'BacktestPro', 'TradingBot',
            'FinancePhD', 'WallStreetVet', 'StartupFounder', 'VentureCapitalist', 'AngelInvestor',
            'DayTrader', 'SwingTrader', 'LongTermInvestor', 'ValueInvestor', 'GrowthInvestor',
            'TechnicalAnalyst', 'FundamentalAnalyst', 'QuantAnalyst', 'PortfolioManager', 'RiskAnalyst',
            'DerivativesTrader', 'FixedIncomeTrader', 'EquityTrader', 'ForexTrader', 'CommodityTrader',
            'OptionsTrader', 'FuturesTrader', 'CryptoTrader', 'ArbitrageTrader', 'HighFreqTrader',
            'AlgorithmicTrader', 'SystematicTrader', 'DiscretionaryTrader', 'MacroTrader', 'MicroTrader',
            'NewbieTrader', 'ExperiencedTrader', 'VeteranTrader', 'RetiredTrader', 'StudentTrader'
        ]
        
        # 分类
        self.categories = [
            'general', 'strategy', 'market', 'help', 'news', 'discussion',
            'crypto', 'risk', 'options', 'futures', 'forex', 'data',
            'psychology', 'arbitrage', 'announcement', 'resources',
            'feedback', 'technology', 'regulation', 'education'
        ]
        
        # 标签池
        self.tags = [
            '量化策略', '机器学习', '深度学习', '技术分析', '基本面分析',
            '风险管理', '回测', '实盘', '数据挖掘', '算法交易',
            '高频交易', '套利', '期权', '期货', '外汇',
            '数字货币', '比特币', '以太坊', '区块链', 'DeFi',
            '股票', '债券', '商品', '指数', 'ETF',
            'Python', 'R', 'C++', 'Java', 'JavaScript',
            '神经网络', 'LSTM', 'CNN', 'RNN', 'GAN',
            '时间序列', '统计套利', '配对交易', '动量策略', '均值回归',
            '趋势跟踪', '反转策略', '突破策略', '网格交易', '马丁格尔',
            '资产配置', '投资组合', '夏普比率', '最大回撤', '收益率',
            '波动率', '相关性', 'VaR', '压力测试', '蒙特卡洛',
            '黑天鹅', '肥尾分布', '市场微观结构', '流动性', '滑点',
            '交易成本', '税务优化', '监管合规', '市场情绪', '行为金融'
        ]
        
        # 帖子标题模板
        self.title_templates = [
            "{}策略分享：年化收益{}%",
            "新手求助：{}问题如何解决？",
            "{}市场分析：未来走势预测",
            "分享一个{}的实战经验",
            "{}技术指标的使用心得",
            "关于{}的深度思考",
            "{}策略的风险控制方法",
            "{}数据获取和处理技巧",
            "{}回测结果分析和优化",
            "{}实盘交易中的注意事项",
            "{}与{}的对比分析",
            "{}在{}中的应用",
            "{}策略的参数优化方法",
            "{}市场的机会和挑战",
            "{}工具推荐和使用指南"
        ]
        
        # 内容模板
        self.content_templates = [
            "经过长期研究和测试，我发现{}在{}市场中表现出色。通过{}方法，我们可以实现{}的收益率，同时将最大回撤控制在{}以内。",
            "最近在学习{}，遇到了一些问题。主要是{}方面的困难，希望有经验的朋友能够指点一下。我已经尝试了{}，但效果不太理想。",
            "分析当前{}市场的走势，我认为{}因素将对价格产生重要影响。从技术面看，{}指标显示{}信号，建议关注{}点位。",
            "在实际交易中，{}策略需要注意以下几个要点：1. {}；2. {}；3. {}。只有做好这些准备工作，才能在实盘中获得稳定收益。",
            "{}是量化交易中的重要概念，它可以帮助我们{}。通过{}算法，我们能够更好地理解市场规律，提高交易效率。",
            "今天想和大家讨论一下{}的问题。我的观点是{}，理由如下：首先，{}；其次，{}；最后，{}。欢迎大家发表不同意见。",
            "{}策略的核心思想是{}，它适用于{}市场环境。在实施过程中，需要重点关注{}参数的设置，以及{}风险的控制。",
            "数据质量对{}策略的影响非常大。我们需要对原始数据进行{}处理，包括{}、{}等步骤，确保数据的准确性和完整性。",
            "回测是验证策略有效性的重要手段。在进行{}策略回测时，要注意避免{}偏差，合理设置{}参数，并进行充分的{}测试。",
            "实盘交易与回测存在很大差异，主要体现在{}方面。为了缩小这种差距，我们需要在策略中考虑{}因素，并建立完善的{}机制。"
        ]
        
    def generate_random_content_elements(self):
        """生成随机内容元素"""
        strategies = ['双均线', '布林带', 'RSI', 'MACD', '动量', '均值回归', '趋势跟踪', '套利', '网格交易']
        markets = ['股票', '期货', '外汇', '数字货币', '期权', '债券']
        methods = ['机器学习', '深度学习', '统计分析', '技术分析', '基本面分析']
        returns = [15, 20, 25, 30, 35, 40, 45, 50]
        drawdowns = ['5%', '8%', '10%', '12%', '15%']
        factors = ['宏观经济', '政策变化', '市场情绪', '技术突破', '基本面改善']
        indicators = ['RSI', 'MACD', 'KDJ', '布林带', '移动平均线']
        signals = ['买入', '卖出', '观望', '加仓', '减仓']
        levels = ['支撑位', '阻力位', '关键点位', '突破点']
        
        return {
            'strategy': random.choice(strategies),
            'market': random.choice(markets),
            'method': random.choice(methods),
            'return': random.choice(returns),
            'drawdown': random.choice(drawdowns),
            'factor': random.choice(factors),
            'indicator': random.choice(indicators),
            'signal': random.choice(signals),
            'level': random.choice(levels)
        }
    
    def generate_post_title(self):
        """生成帖子标题"""
        template = random.choice(self.title_templates)
        elements = self.generate_random_content_elements()
        
        try:
            if '{}' in template:
                # 根据模板中的占位符数量填充内容
                placeholders = template.count('{}')
                if placeholders == 1:
                    return template.format(elements['strategy'])
                elif placeholders == 2:
                    return template.format(elements['strategy'], elements['return'])
                else:
                    return template.format(elements['strategy'], elements['market'], elements['method'])
        except:
            pass
        
        # 如果格式化失败，返回简单标题
        return f"{elements['strategy']}策略分享"
    
    def generate_post_content(self):
        """生成帖子内容"""
        template = random.choice(self.content_templates)
        elements = self.generate_random_content_elements()
        
        try:
            return template.format(
                elements['strategy'], elements['market'], elements['method'],
                elements['return'], elements['drawdown'], elements['factor'],
                elements['indicator'], elements['signal'], elements['level']
            )
        except:
            return f"这是一个关于{elements['strategy']}策略的讨论帖子。"
    
    def generate_random_tags(self, count=3):
        """生成随机标签"""
        return random.sample(self.tags, min(count, len(self.tags)))
    
    def generate_random_datetime(self, days_back=30):
        """生成随机时间"""
        start_date = datetime.now() - timedelta(days=days_back)
        random_days = random.randint(0, days_back)
        random_hours = random.randint(0, 23)
        random_minutes = random.randint(0, 59)
        
        return start_date + timedelta(days=random_days, hours=random_hours, minutes=random_minutes)
    
    def generate_single_post(self, post_id):
        """生成单个帖子"""
        return {
            'id': post_id,
            'title': self.generate_post_title(),
            'content': self.generate_post_content(),
            'category': random.choice(self.categories),
            'tags': self.generate_random_tags(random.randint(2, 5)),
            'views': random.randint(10, 5000),
            'likes': random.randint(0, 500),
            'dislikes': random.randint(0, 50),
            'is_pinned': random.random() < 0.05,  # 5%概率置顶
            'is_locked': random.random() < 0.02,  # 2%概率锁定
            'is_featured': random.random() < 0.1,  # 10%概率精华
            'author': random.choice(self.usernames),
            'reply_count': random.randint(0, 100),
            'last_reply_time': self.generate_random_datetime(7).isoformat(),
            'created_at': self.generate_random_datetime().isoformat(),
            'updated_at': self.generate_random_datetime(7).isoformat(),
            'vote_score': random.randint(-10, 100),
            'difficulty_level': random.choice(['初级', '中级', '高级', '专家']),
            'estimated_read_time': random.randint(2, 15),  # 分钟
            'language': 'zh-CN',
            'status': random.choice(['published', 'draft', 'pending']) if random.random() < 0.1 else 'published'
        }
    
    def generate_forum_data(self, count=200):
        """生成论坛数据"""
        posts = []
        
        print(f"🚀 开始生成{count}条论坛帖子数据...")
        
        for i in range(1, count + 1):
            post = self.generate_single_post(i)
            posts.append(post)
            
            if i % 20 == 0:
                print(f"✅ 已生成 {i}/{count} 条帖子")
        
        # 生成统计数据
        stats = self.generate_forum_stats(posts)
        
        result = {
            'posts': posts,
            'stats': stats,
            'generated_at': datetime.now().isoformat(),
            'total_posts': len(posts)
        }
        
        print(f"🎉 论坛数据生成完成！")
        print(f"📊 统计信息：")
        print(f"   - 总帖子数：{stats['total_posts']}")
        print(f"   - 总用户数：{stats['total_users']}")
        print(f"   - 总浏览量：{stats['total_views']:,}")
        print(f"   - 总点赞数：{stats['total_likes']:,}")
        print(f"   - 置顶帖子：{stats['pinned_posts']}")
        print(f"   - 精华帖子：{stats['featured_posts']}")
        
        return result
    
    def generate_forum_stats(self, posts):
        """生成论坛统计数据"""
        total_views = sum(post['views'] for post in posts)
        total_likes = sum(post['likes'] for post in posts)
        total_replies = sum(post['reply_count'] for post in posts)
        pinned_posts = len([p for p in posts if p['is_pinned']])
        featured_posts = len([p for p in posts if p['is_featured']])
        locked_posts = len([p for p in posts if p['is_locked']])
        
        # 分类统计
        category_stats = {}
        for post in posts:
            category = post['category']
            if category not in category_stats:
                category_stats[category] = 0
            category_stats[category] += 1
        
        # 用户统计
        unique_users = len(set(post['author'] for post in posts))
        
        return {
            'total_posts': len(posts),
            'total_users': unique_users,
            'total_views': total_views,
            'total_likes': total_likes,
            'total_replies': total_replies,
            'pinned_posts': pinned_posts,
            'featured_posts': featured_posts,
            'locked_posts': locked_posts,
            'category_distribution': category_stats,
            'avg_views_per_post': round(total_views / len(posts), 1),
            'avg_likes_per_post': round(total_likes / len(posts), 1),
            'avg_replies_per_post': round(total_replies / len(posts), 1)
        }
    
    def save_to_file(self, data, filename='forum_data.json'):
        """保存数据到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 数据已保存到 {filename}")
            return True
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False

def main():
    """主函数"""
    generator = ForumDataGenerator()
    
    # 生成200条帖子数据
    forum_data = generator.generate_forum_data(200)
    
    # 保存到文件
    generator.save_to_file(forum_data, 'forum_data.json')
    
    # 生成简化版本用于API
    simplified_posts = []
    for post in forum_data['posts'][:50]:  # 只取前50条用于API展示
        simplified_post = {
            'id': post['id'],
            'title': post['title'],
            'content': post['content'][:200] + '...' if len(post['content']) > 200 else post['content'],
            'category': post['category'],
            'tags': post['tags'][:3],  # 只保留前3个标签
            'views': post['views'],
            'likes': post['likes'],
            'is_pinned': post['is_pinned'],
            'is_locked': post['is_locked'],
            'author': post['author'],
            'reply_count': post['reply_count'],
            'created_at': post['created_at']
        }
        simplified_posts.append(simplified_post)
    
    # 保存简化版本
    simplified_data = {
        'posts': simplified_posts,
        'stats': forum_data['stats']
    }
    generator.save_to_file(simplified_data, 'forum_api_data.json')
    
    print(f"\n📋 生成完成！")
    print(f"   - 完整数据：forum_data.json ({len(forum_data['posts'])}条)")
    print(f"   - API数据：forum_api_data.json ({len(simplified_posts)}条)")

if __name__ == '__main__':
    main()
