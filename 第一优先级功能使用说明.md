# QuantTradeX 第一优先级功能使用说明

## 📋 概述

本文档详细介绍QuantTradeX平台第一优先级功能的使用方法，包括关注列表功能、API数据源集成和支付系统基础版。

**开发完成时间**: 2025年1月27日
**版本**: v1.0
**访问地址**: http://gdpp.com

---

## 🌟 功能1：关注列表功能完善

### 访问地址
- **数据中心主页**: http://gdpp.com/datacenter

### 功能特性
- ✅ 支持多种资产类型：股票、加密货币、外汇、期货、黄金
- ✅ 实时价格更新和涨跌幅显示
- ✅ 个人化关注列表管理
- ✅ 统计信息和数据分析
- ✅ 一键添加/移除关注品种

### 使用步骤

#### 1. 访问数据中心
1. 打开浏览器，访问 http://gdpp.com/datacenter
2. 如未登录，系统会提示先登录
3. 使用现有账户登录（如：admin/admin123）

#### 2. 添加关注品种
1. 在页面顶部的搜索区域：
   - **交易品种代码**: 输入代码（如：AAPL, BTC-USD, EURUSD）
   - **资产类型**: 选择类型（股票/加密货币/外汇/期货/黄金）
   - **名称**: 可选，输入自定义名称
2. 点击 **"添加关注"** 按钮
3. 系统会验证并添加到您的关注列表

#### 3. 管理关注列表
- **查看详情**: 关注列表显示实时价格、涨跌幅、交易量等信息
- **移除品种**: 点击每个品种右侧的 ❌ 按钮
- **刷新数据**: 点击右上角的 🔄 刷新按钮
- **清空列表**: 点击 🗑️ 清空按钮（需确认）

#### 4. 统计信息
页面顶部显示：
- **关注品种**: 总数量
- **上涨品种**: 当前上涨的数量
- **下跌品种**: 当前下跌的数量
- **最后更新**: 数据更新时间

### API接口使用

#### 获取关注列表
```bash
GET /api/watchlist
```

#### 添加关注品种
```bash
POST /api/watchlist
Content-Type: application/json

{
  "symbol": "AAPL",
  "type": "stock",
  "name": "苹果公司"
}
```

#### 移除关注品种
```bash
DELETE /api/watchlist
Content-Type: application/json

{
  "symbol": "AAPL",
  "type": "stock"
}
```

---

## 🔌 功能2：API数据源集成

### 访问地址
- **API状态查看**: http://gdpp.com/api/data-providers
- **功能演示页面**: http://gdpp.com/demo

### 支持的数据提供商

#### 1. Alpha Vantage（股票和外汇）
- **用途**: 股票价格、技术指标、外汇汇率
- **免费额度**: 每分钟5次请求，每天500次请求
- **申请地址**: https://www.alphavantage.co/support/#api-key
- **配置**: 设置环境变量 `ALPHA_VANTAGE_API_KEY`

#### 2. CoinGecko（加密货币）
- **用途**: 加密货币价格、市值、交易量
- **免费额度**: 每分钟10-50次请求
- **申请地址**: https://www.coingecko.com/en/api
- **配置**: 设置环境变量 `COINGECKO_API_KEY`（可选）

#### 3. Yahoo Finance（免费股票数据）
- **用途**: 股票价格、指数数据
- **免费额度**: 每小时2000次请求
- **配置**: 无需配置，默认启用

#### 4. Twelve Data（综合金融数据）
- **用途**: 股票、外汇、加密货币、期货
- **免费额度**: 每分钟8次请求，每天800次请求
- **申请地址**: https://twelvedata.com/
- **配置**: 设置环境变量 `TWELVE_DATA_API_KEY`

### 使用步骤

#### 1. 查看API状态
1. 访问 http://gdpp.com/api/data-providers
2. 查看所有数据提供商的状态（已配置/未配置）
3. 了解每个提供商支持的数据类型

#### 2. 配置API密钥
1. 根据需要申请相应的API密钥
2. 在服务器上设置环境变量：
   ```bash
   export ALPHA_VANTAGE_API_KEY="your_api_key"
   export TWELVE_DATA_API_KEY="your_api_key"
   ```
3. 重启应用使配置生效

#### 3. 测试API连接
1. 访问功能演示页面：http://gdpp.com/demo
2. 点击 "API数据源集成" 功能卡片
3. 点击 "测试连接" 按钮验证API是否正常工作

### API接口使用

#### 查看提供商状态
```bash
GET /api/data-providers
```

#### 测试特定提供商
```bash
GET /api/data-providers/test/yahoo_finance
```

#### 更新提供商配置（管理员）
```bash
POST /api/data-providers/config
Content-Type: application/json

{
  "provider_id": "alpha_vantage",
  "api_key": "your_new_api_key"
}
```

---

## 💳 功能3：支付系统基础版

### 访问地址
- **VIP升级**: http://gdpp.com （主页VIP升级按钮）
- **支付方式查看**: http://gdpp.com/api/payment/methods
- **功能演示**: http://gdpp.com/demo

### 支持的支付方式

#### 1. 支付宝（Alipay）
- **用途**: 国内用户VIP升级支付
- **状态**: 需要配置API密钥
- **配置**: 设置 `ALIPAY_APP_ID`, `ALIPAY_PRIVATE_KEY`, `ALIPAY_PUBLIC_KEY`

#### 2. 微信支付（WeChat Pay）
- **用途**: 国内用户VIP升级支付
- **状态**: 需要配置API密钥
- **配置**: 设置 `WECHAT_APP_ID`, `WECHAT_MCH_ID`, `WECHAT_API_KEY`

#### 3. PayPal
- **用途**: 国际用户VIP升级支付
- **状态**: 需要配置API密钥
- **配置**: 设置 `PAYPAL_CLIENT_ID`, `PAYPAL_CLIENT_SECRET`

#### 4. Stripe
- **用途**: 国际信用卡支付
- **状态**: 需要配置API密钥
- **配置**: 设置 `STRIPE_PUBLIC_KEY`, `STRIPE_SECRET_KEY`

#### 5. 模拟支付
- **用途**: 测试和演示
- **状态**: 默认启用
- **配置**: 无需配置

### VIP会员计划

#### 月度会员
- **价格**: ¥99.99
- **有效期**: 30天
- **功能**: 所有高级功能访问权限

#### 年度会员
- **价格**: ¥999.99
- **有效期**: 365天
- **功能**: 所有高级功能访问权限

#### 终身会员
- **价格**: ¥2999.99
- **有效期**: 终身
- **功能**: 所有高级功能访问权限

### 使用步骤

#### 1. VIP升级流程
1. 访问主页：http://gdpp.com
2. 登录您的账户
3. 点击页面上的 **"升级VIP"** 按钮
4. 选择会员计划（月度/年度/终身）
5. 选择支付方式
6. 完成支付流程

#### 2. 模拟支付测试
1. 在支付方式选择中选择 **"模拟支付"**
2. 系统会跳转到模拟支付页面
3. 点击 **"确认支付"** 按钮
4. 支付成功后自动跳转并更新会员状态

#### 3. 查看订单状态
1. 支付完成后，系统会显示订单信息
2. 用户资料中会更新VIP状态和到期时间
3. 可以通过API查询订单详情

### API接口使用

#### 获取支付方式
```bash
GET /api/payment/methods
```

#### 创建升级订单
```bash
POST /auth/upgrade
Content-Type: application/json

{
  "plan": "monthly",
  "payment_method": "mock"
}
```

#### 查看订单信息
```bash
GET /api/payment/order/{order_id}
```

#### 完成升级
```bash
GET /auth/upgrade/complete/{order_id}
```

---

## 🎮 功能演示页面

### 访问地址
http://gdpp.com/demo

### 演示内容
1. **关注列表功能演示**
   - 功能特性介绍
   - 直接跳转到数据中心
   - API接口测试

2. **API数据源集成演示**
   - 提供商状态查看
   - 连接测试功能
   - 配置指南链接

3. **支付系统演示**
   - 支付方式状态
   - 模拟支付测试
   - VIP升级演示

### 使用方法
1. 访问演示页面
2. 点击相应的功能卡片
3. 查看功能介绍和状态
4. 使用演示按钮测试功能

---

## 🔧 管理员功能

### 用户管理
- **管理员账户**: admin / admin123
- **权限**: 可以查看所有用户数据、配置API、管理支付设置

### 系统监控
- **服务状态**: `systemctl status quanttradex`
- **日志查看**: `journalctl -u quanttradex -f`
- **Nginx状态**: `systemctl status nginx`

### 配置管理
- **环境变量**: 在 `/www/wwwroot/gdpp.com/.env` 文件中配置
- **重启服务**: `systemctl restart quanttradex`

---

## 📞 技术支持

### 常见问题
1. **登录问题**: 使用 admin/admin123 或其他预设账户
2. **API不工作**: 检查API密钥配置和网络连接
3. **支付失败**: 确认支付方式配置正确

### 联系方式
- **技术支持**: <EMAIL>
- **文档地址**: http://gdpp.com/API配置指南.md
- **项目地址**: /www/wwwroot/gdpp.com

### 更新日志
- **2025-01-27**: 第一优先级功能开发完成
  - 关注列表功能完善
  - API数据源集成
  - 支付系统基础版

---

## 🚀 快速入门指南

### 5分钟体验新功能

#### 第1步：访问平台
1. 打开浏览器，访问：http://gdpp.com
2. 使用测试账户登录：`admin` / `admin123`

#### 第2步：体验关注列表
1. 访问数据中心：http://gdpp.com/datacenter
2. 添加关注品种：输入 `AAPL`，选择 `股票`，点击添加
3. 查看实时数据和统计信息

#### 第3步：测试支付功能
1. 返回主页，点击 **"升级VIP"** 按钮
2. 选择 **"月度会员"**
3. 选择 **"模拟支付"** 方式
4. 完成支付流程，体验VIP升级

#### 第4步：查看功能演示
1. 访问演示页面：http://gdpp.com/demo
2. 点击各个功能卡片查看详细介绍
3. 使用测试按钮验证功能

### 核心功能访问链接
- 🏠 **主页**: http://gdpp.com
- 📊 **数据中心**: http://gdpp.com/datacenter
- 🎮 **功能演示**: http://gdpp.com/demo
- 💼 **策略市场**: http://gdpp.com/strategies
- 🔬 **回测系统**: http://gdpp.com/backtest
- 💬 **社区论坛**: http://gdpp.com/forum

### 测试账户
| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 完整权限 |
| trader1 | password123 | 普通用户 | 基础功能 |
| vip_user | password123 | VIP用户 | 高级功能 |

---

**文档版本**: v1.0
**最后更新**: 2025年1月27日
**维护团队**: QuantTradeX开发团队
