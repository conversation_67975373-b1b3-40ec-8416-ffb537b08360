#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
常量定义
包含应用程序中使用的各种常量
"""

# 用户角色
USER_ROLES = {
    'ADMIN': 'admin',
    'PREMIUM': 'premium', 
    'USER': 'user',
    'GUEST': 'guest'
}

# 策略类型
STRATEGY_TYPES = {
    'TREND_FOLLOWING': 'trend_following',
    'MEAN_REVERSION': 'mean_reversion',
    'MOMENTUM': 'momentum',
    'ARBITRAGE': 'arbitrage',
    'MARKET_MAKING': 'market_making',
    'CUSTOM': 'custom'
}

# 资产类别
ASSET_CLASSES = {
    'STOCKS': 'stocks',
    'CRYPTO': 'crypto',
    'FOREX': 'forex',
    'FUTURES': 'futures',
    'OPTIONS': 'options',
    'BONDS': 'bonds'
}

# 策略状态
STRATEGY_STATUS = {
    'DRAFT': 'draft',
    'TESTING': 'testing',
    'ACTIVE': 'active',
    'PAUSED': 'paused',
    'ARCHIVED': 'archived'
}

# 回测时间间隔
BACKTEST_INTERVALS = {
    '1m': '1分钟',
    '5m': '5分钟',
    '15m': '15分钟',
    '30m': '30分钟',
    '1h': '1小时',
    '4h': '4小时',
    '1d': '1天',
    '1wk': '1周',
    '1mo': '1月'
}

# 风险偏好
RISK_PREFERENCES = {
    'CONSERVATIVE': 'conservative',
    'MODERATE': 'moderate',
    'AGGRESSIVE': 'aggressive'
}

# 经验水平
EXPERIENCE_LEVELS = {
    'BEGINNER': 'beginner',
    'INTERMEDIATE': 'intermediate',
    'ADVANCED': 'advanced',
    'EXPERT': 'expert'
}

# 地区代码
REGIONS = {
    'CN': 'cn',
    'US': 'us',
    'HK': 'hk',
    'TW': 'tw',
    'SG': 'sg',
    'JP': 'jp',
    'KR': 'kr',
    'EU': 'eu',
    'UK': 'uk'
}

# 支付方式
PAYMENT_METHODS = {
    'ALIPAY': 'alipay',
    'WECHAT': 'wechat',
    'PAYPAL': 'paypal',
    'STRIPE': 'stripe',
    'BANK_TRANSFER': 'bank_transfer'
}

# 订阅类型
SUBSCRIPTION_TYPES = {
    'FREE': 'free',
    'PREMIUM': 'premium',
    'TRIAL': 'trial',
    'ENTERPRISE': 'enterprise'
}

# 通知类型
NOTIFICATION_TYPES = {
    'EMAIL': 'email',
    'PUSH': 'push',
    'SMS': 'sms',
    'IN_APP': 'in_app'
}

# 数据源
DATA_SOURCES = {
    'YAHOO_FINANCE': 'yahoo_finance',
    'ALPHA_VANTAGE': 'alpha_vantage',
    'QUANDL': 'quandl',
    'IEX_CLOUD': 'iex_cloud',
    'POLYGON': 'polygon',
    'COINGECKO': 'coingecko',
    'BINANCE': 'binance'
}

# 技术指标
TECHNICAL_INDICATORS = {
    'SMA': 'Simple Moving Average',
    'EMA': 'Exponential Moving Average',
    'RSI': 'Relative Strength Index',
    'MACD': 'Moving Average Convergence Divergence',
    'BB': 'Bollinger Bands',
    'ATR': 'Average True Range',
    'STOCH': 'Stochastic Oscillator',
    'CCI': 'Commodity Channel Index',
    'ADX': 'Average Directional Index',
    'OBV': 'On Balance Volume'
}

# 市场状态
MARKET_STATUS = {
    'OPEN': 'open',
    'CLOSED': 'closed',
    'PRE_MARKET': 'pre_market',
    'AFTER_HOURS': 'after_hours',
    'HOLIDAY': 'holiday'
}

# 订单类型
ORDER_TYPES = {
    'MARKET': 'market',
    'LIMIT': 'limit',
    'STOP': 'stop',
    'STOP_LIMIT': 'stop_limit',
    'TRAILING_STOP': 'trailing_stop'
}

# 订单状态
ORDER_STATUS = {
    'PENDING': 'pending',
    'FILLED': 'filled',
    'PARTIALLY_FILLED': 'partially_filled',
    'CANCELLED': 'cancelled',
    'REJECTED': 'rejected',
    'EXPIRED': 'expired'
}

# 交易方向
TRADE_DIRECTIONS = {
    'BUY': 'buy',
    'SELL': 'sell',
    'LONG': 'long',
    'SHORT': 'short'
}

# 时区
TIMEZONES = {
    'UTC': 'UTC',
    'US_EASTERN': 'US/Eastern',
    'US_CENTRAL': 'US/Central',
    'US_MOUNTAIN': 'US/Mountain',
    'US_PACIFIC': 'US/Pacific',
    'ASIA_SHANGHAI': 'Asia/Shanghai',
    'ASIA_TOKYO': 'Asia/Tokyo',
    'ASIA_HONG_KONG': 'Asia/Hong_Kong',
    'EUROPE_LONDON': 'Europe/London',
    'EUROPE_PARIS': 'Europe/Paris'
}

# 文件类型
FILE_TYPES = {
    'STRATEGY': 'strategy',
    'BACKTEST': 'backtest',
    'REPORT': 'report',
    'DATA': 'data',
    'LOG': 'log',
    'CONFIG': 'config'
}

# API限制
API_LIMITS = {
    'REQUESTS_PER_MINUTE': 60,
    'REQUESTS_PER_HOUR': 1000,
    'REQUESTS_PER_DAY': 10000,
    'MAX_SYMBOLS_PER_REQUEST': 100,
    'MAX_BACKTEST_DURATION_DAYS': 365 * 5,  # 5年
    'MAX_STRATEGY_CODE_LENGTH': 50000,  # 50KB
    'MAX_FILE_SIZE_MB': 10
}

# 默认值
DEFAULTS = {
    'INITIAL_CAPITAL': 100000,
    'COMMISSION_RATE': 0.001,
    'SLIPPAGE': 0.0005,
    'CACHE_TIMEOUT': 300,  # 5分钟
    'SESSION_TIMEOUT': 3600,  # 1小时
    'PAGE_SIZE': 20,
    'MAX_WATCHLIST_SIZE': 50,
    'MAX_STRATEGIES_PER_USER': 100
}

# 错误代码
ERROR_CODES = {
    'INVALID_REQUEST': 'INVALID_REQUEST',
    'UNAUTHORIZED': 'UNAUTHORIZED',
    'FORBIDDEN': 'FORBIDDEN',
    'NOT_FOUND': 'NOT_FOUND',
    'RATE_LIMITED': 'RATE_LIMITED',
    'INTERNAL_ERROR': 'INTERNAL_ERROR',
    'DATA_ERROR': 'DATA_ERROR',
    'VALIDATION_ERROR': 'VALIDATION_ERROR'
}

# 成功代码
SUCCESS_CODES = {
    'OK': 'OK',
    'CREATED': 'CREATED',
    'UPDATED': 'UPDATED',
    'DELETED': 'DELETED',
    'PROCESSED': 'PROCESSED'
}
