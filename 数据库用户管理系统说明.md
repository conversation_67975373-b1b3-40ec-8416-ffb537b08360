# QuantTradeX 数据库用户管理系统说明

## 📋 概述

为了解决用户注册后服务器重启导致数据丢失的问题，我们已经成功实现了真正的数据库用户管理系统，替换了之前的内存存储方式。

**实施时间**: 2025年1月27日  
**问题解决**: 用户数据永久保存，服务器重启不会丢失

## 🔧 技术实现

### 1. 数据库表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(64) NOT NULL,
    full_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user',
    avatar_url VARCHAR(255),
    bio TEXT,
    phone VARCHAR(20),
    region VARCHAR(10),
    risk_preference VARCHAR(20),
    experience VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_premium BOOLEAN DEFAULT FALSE,
    premium_expires TIMESTAMP,
    total_strategies INTEGER DEFAULT 0,
    total_backtests INTEGER DEFAULT 0,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    win_rate DECIMAL(5,2) DEFAULT 0.00,
    followers INTEGER DEFAULT 0,
    following INTEGER DEFAULT 0,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    backup_codes TEXT
);
```

#### 用户关注列表表 (user_watchlists)
```sql
CREATE TABLE user_watchlists (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    asset_type VARCHAR(20) NOT NULL,
    name VARCHAR(100),
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, symbol, asset_type)
);
```

#### 支付订单表 (payment_orders)
```sql
CREATE TABLE payment_orders (
    id SERIAL PRIMARY KEY,
    order_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    plan VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    payment_method VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    payment_data TEXT
);
```

### 2. 核心模块

#### database_init.py
- **功能**: 数据库初始化脚本
- **作用**: 创建表结构，插入默认用户数据
- **运行**: `python database_init.py`

#### user_manager.py
- **功能**: 用户管理类
- **作用**: 提供完整的用户CRUD操作
- **特性**: 密码哈希、数据验证、关注列表管理

### 3. 系统架构改进

#### 之前的问题
```python
# 内存存储 - 服务器重启后数据丢失
MOCK_USERS = {
    'admin': {'password': 'admin123', ...},
    'trader1': {'password': 'password123', ...}
}
```

#### 现在的解决方案
```python
# 数据库存储 - 永久保存
from user_manager import user_manager

# 用户注册
success, message = user_manager.create_user(username, email, password)

# 用户登录
user = user_manager.get_user_by_username(username)
if user_manager.verify_password(password, user['password']):
    # 登录成功
```

## 🚀 功能特性

### 1. 用户管理
- ✅ **用户注册**: 数据永久保存到PostgreSQL
- ✅ **用户登录**: 密码哈希验证
- ✅ **用户信息更新**: 支持资料修改
- ✅ **最后登录时间**: 自动更新
- ✅ **VIP升级**: 会员状态管理

### 2. 关注列表管理
- ✅ **添加关注**: 保存到数据库
- ✅ **移除关注**: 从数据库删除
- ✅ **查看关注**: 从数据库读取
- ✅ **用户隔离**: 每个用户独立的关注列表

### 3. 安全特性
- ✅ **密码哈希**: SHA256加密存储
- ✅ **数据验证**: 用户名和邮箱唯一性检查
- ✅ **SQL注入防护**: 使用参数化查询
- ✅ **错误处理**: 完善的异常处理机制

### 4. 兼容性设计
- ✅ **向后兼容**: 如果数据库不可用，自动回退到MOCK_USERS
- ✅ **平滑迁移**: 现有功能不受影响
- ✅ **错误恢复**: 数据库连接失败时的优雅处理

## 📊 测试验证

### 1. 用户注册测试
```bash
curl -X POST http://127.0.0.1:5000/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"test123","full_name":"测试用户"}'
```

**结果**: ✅ 注册成功，用户ID: 6

### 2. 用户登录测试
```bash
curl -X POST http://127.0.0.1:5000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"test123"}'
```

**结果**: ✅ 登录成功，返回完整用户信息

### 3. 数据持久化测试
1. 注册新用户
2. 重启服务器
3. 尝试登录

**结果**: ✅ 服务器重启后，用户数据依然存在，可以正常登录

### 4. 关注列表测试
```bash
# 添加关注
curl -X POST http://127.0.0.1:5000/api/watchlist \
  -H "Content-Type: application/json" \
  -d '{"symbol":"AAPL","type":"stock","name":"苹果公司"}'

# 查看关注列表
curl http://127.0.0.1:5000/api/watchlist
```

**结果**: ✅ 关注列表数据保存到数据库，重启后依然存在

## 🔧 部署和维护

### 1. 初始化数据库
```bash
cd gdpp.com
python database_init.py
```

### 2. 检查数据库连接
```bash
psql -h localhost -U quanttradex_user -d quanttradex -c "\d users"
```

### 3. 查看用户数据
```sql
SELECT id, username, email, full_name, role, is_premium, created_at 
FROM users ORDER BY created_at DESC;
```

### 4. 备份用户数据
```bash
pg_dump -h localhost -U quanttradex_user quanttradex > user_backup.sql
```

## 📈 性能优化

### 1. 数据库索引
- ✅ 用户名唯一索引
- ✅ 邮箱唯一索引
- ✅ 关注列表复合索引

### 2. 连接池管理
- ✅ 单例模式的用户管理器
- ✅ 自动重连机制
- ✅ 连接错误处理

### 3. 查询优化
- ✅ 参数化查询
- ✅ 批量操作支持
- ✅ 事务管理

## 🔍 监控和日志

### 1. 日志记录
```python
logger.info(f"创建用户成功: {username} (ID: {user_id})")
logger.info(f"用户 {username} 登录成功")
logger.error(f"数据库连接失败: {e}")
```

### 2. 状态监控
- 数据库连接状态
- 用户注册/登录统计
- 错误率监控

## 🎯 未来扩展

### 1. 高级功能
- 用户权限管理
- 多因素认证
- 用户行为分析

### 2. 性能优化
- Redis缓存集成
- 读写分离
- 分库分表

### 3. 安全增强
- 密码强度检查
- 登录频率限制
- 异常登录检测

## 📞 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证数据库配置信息
   - 确认网络连接

2. **用户注册失败**
   - 检查用户名/邮箱是否已存在
   - 验证数据格式
   - 查看错误日志

3. **登录验证失败**
   - 确认密码哈希算法一致
   - 检查用户状态
   - 验证session配置

### 联系支持
- **技术支持**: 查看系统日志
- **数据恢复**: 使用备份文件
- **紧急处理**: 启用MOCK_USERS兼容模式

---

## ✅ 总结

通过实施数据库用户管理系统，我们成功解决了以下问题：

1. ✅ **数据持久化**: 用户注册后数据永久保存
2. ✅ **服务器重启**: 不再丢失用户数据
3. ✅ **关注列表**: 用户关注的品种永久保存
4. ✅ **安全性**: 密码哈希存储，防止明文泄露
5. ✅ **扩展性**: 支持更多用户功能扩展
6. ✅ **兼容性**: 向后兼容，平滑迁移

**现在用户可以放心注册和使用平台，不用担心数据丢失问题！**

---

**文档版本**: v1.0  
**创建时间**: 2025年1月27日  
**维护团队**: QuantTradeX开发团队
