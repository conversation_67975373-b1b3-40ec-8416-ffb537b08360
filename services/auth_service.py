#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
认证服务
负责用户登录、注册、权限验证等
"""

import io
import base64
import json
import os
import logging
from datetime import datetime
from functools import wraps
from flask import session, jsonify, request

# 可选导入
try:
    import pyotp
except ImportError:
    pyotp = None

try:
    import qrcode
except ImportError:
    qrcode = None

logger = logging.getLogger(__name__)

class AuthService:
    """认证服务类"""

    def __init__(self):
        # 用户数据文件路径
        self.users_file = 'data/users.json'
        # 确保数据目录存在
        os.makedirs('data', exist_ok=True)
        # 加载用户数据
        self.users = self._load_users()

    def _load_users(self):
        """加载用户数据"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 如果文件不存在，创建默认用户并保存
                default_users = self._get_default_users()
                self._save_users(default_users)
                return default_users
        except Exception as e:
            logger.error(f"加载用户数据失败: {e}")
            return self._get_default_users()

    def _get_default_users(self):
        """获取默认用户数据"""
        return {
            'admin': {
                'id': 1,
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'full_name': '系统管理员',
                'role': 'admin',
                'avatar_url': '/static/img/avatar-admin.png',
                'bio': 'QuantTradeX平台管理员，负责平台运营和技术支持',
                'phone': '+86 138****0001',
                'region': 'cn',
                'risk_preference': 'moderate',
                'experience': 'expert',
                'created_at': '2025-01-01T00:00:00',
                'updated_at': '2025-01-01T00:00:00',
                'last_login': '2025-01-27T01:00:00',
                'is_premium': True,
                'premium_expires': '2026-01-01T00:00:00',
                'total_strategies': 5,
                'total_backtests': 25,
                'total_profit': 156780.50,
                'win_rate': 78.5,
                'followers': 1250,
                'following': 45,
                'two_factor_enabled': False,
                'two_factor_secret': None,
                'backup_codes': []
            },
            'demo_user': {
                'id': 2,
                'username': 'demo_user',
                'email': '<EMAIL>',
                'password': 'demo123',
                'full_name': '演示用户',
                'role': 'user',
                'avatar_url': '/static/img/avatar-demo.png',
                'bio': '量化交易新手，正在学习各种策略和技术分析',
                'phone': '+86 138****0002',
                'region': 'cn',
                'risk_preference': 'conservative',
                'experience': 'beginner',
                'created_at': '2025-01-10T10:00:00',
                'updated_at': '2025-01-10T10:00:00',
                'last_login': '2025-01-26T18:30:00',
                'is_premium': False,
                'premium_expires': None,
                'total_strategies': 2,
                'total_backtests': 8,
                'total_profit': 2340.80,
                'win_rate': 62.5,
                'followers': 23,
                'following': 156,
                'two_factor_enabled': False,
                'two_factor_secret': None,
                'backup_codes': []
            }
        }

    def _save_users(self, users=None):
        """保存用户数据到文件"""
        try:
            if users is None:
                users = self.users
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(users, f, ensure_ascii=False, indent=2)
            logger.info("用户数据已保存")
        except Exception as e:
            logger.error(f"保存用户数据失败: {e}")

    def authenticate_user(self, username, password):
        """验证用户登录"""
        try:
            user = self.users.get(username)
            if user and user['password'] == password:
                # 更新最后登录时间
                user['last_login'] = datetime.now().isoformat()
                self._save_users()  # 保存更新的登录时间
                return {
                    'success': True,
                    'user': {
                        'id': user['id'],
                        'username': user['username'],
                        'email': user['email'],
                        'full_name': user['full_name'],
                        'role': user['role'],
                        'avatar_url': user['avatar_url'],
                        'is_premium': user['is_premium']
                    }
                }
            else:
                return {'success': False, 'error': '用户名或密码错误'}
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return {'success': False, 'error': '认证服务异常'}

    def authenticate_user_by_email(self, email, password):
        """通过邮箱验证用户登录"""
        try:
            # 查找邮箱对应的用户
            user = None
            username = None
            for uname, udata in self.users.items():
                if udata['email'] == email:
                    user = udata
                    username = uname
                    break

            if user and user['password'] == password:
                # 更新最后登录时间
                user['last_login'] = datetime.now().isoformat()
                self._save_users()  # 保存更新的登录时间
                return {
                    'success': True,
                    'user': {
                        'id': user['id'],
                        'username': user['username'],
                        'email': user['email'],
                        'full_name': user['full_name'],
                        'role': user['role'],
                        'avatar_url': user['avatar_url'],
                        'is_premium': user['is_premium']
                    }
                }
            else:
                return {'success': False, 'error': '邮箱或密码错误'}
        except Exception as e:
            logger.error(f"邮箱认证失败: {e}")
            return {'success': False, 'error': '认证服务异常'}

    def register_user(self, user_data):
        """用户注册"""
        try:
            username = user_data.get('username')
            email = user_data.get('email')
            password = user_data.get('password')

            # 检查用户名是否已存在
            if username in self.users:
                return {'success': False, 'error': '用户名已存在'}

            # 检查邮箱是否已存在
            for user in self.users.values():
                if user['email'] == email:
                    return {'success': False, 'error': '邮箱已被注册'}

            # 创建新用户
            new_user = {
                'id': len(self.users) + 1,
                'username': username,
                'email': email,
                'password': password,
                'full_name': user_data.get('full_name', username),
                'role': 'user',
                'avatar_url': '/static/img/avatar-default.png',
                'bio': '',
                'phone': '',
                'region': user_data.get('region', 'cn'),
                'risk_preference': user_data.get('risk_preference', 'moderate'),
                'experience': user_data.get('experience', 'beginner'),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'last_login': datetime.now().isoformat(),
                'is_premium': False,
                'premium_expires': None,
                'total_strategies': 0,
                'total_backtests': 0,
                'total_profit': 0,
                'win_rate': 0,
                'followers': 0,
                'following': 0,
                'two_factor_enabled': False,
                'two_factor_secret': None,
                'backup_codes': []
            }

            self.users[username] = new_user
            # 保存用户数据到文件
            self._save_users()

            return {
                'success': True,
                'user': {
                    'id': new_user['id'],
                    'username': new_user['username'],
                    'email': new_user['email'],
                    'full_name': new_user['full_name'],
                    'role': new_user['role']
                }
            }
        except Exception as e:
            logger.error(f"用户注册失败: {e}")
            return {'success': False, 'error': '注册服务异常'}

    def get_user_profile(self, username):
        """获取用户资料"""
        try:
            user = self.users.get(username)
            if user:
                # 返回用户信息（不包含密码）
                profile = user.copy()
                del profile['password']
                return {'success': True, 'user': profile}
            else:
                return {'success': False, 'error': '用户不存在'}
        except Exception as e:
            logger.error(f"获取用户资料失败: {e}")
            return {'success': False, 'error': '获取资料失败'}

    def update_user_profile(self, username, update_data):
        """更新用户资料"""
        try:
            user = self.users.get(username)
            if not user:
                return {'success': False, 'error': '用户不存在'}

            # 允许更新的字段
            allowed_fields = [
                'full_name', 'bio', 'phone', 'region',
                'risk_preference', 'experience', 'avatar_url'
            ]

            for field in allowed_fields:
                if field in update_data:
                    user[field] = update_data[field]

            user['updated_at'] = datetime.now().isoformat()
            # 保存用户数据到文件
            self._save_users()

            return {'success': True, 'message': '资料更新成功'}
        except Exception as e:
            logger.error(f"更新用户资料失败: {e}")
            return {'success': False, 'error': '更新失败'}

    def setup_two_factor(self, username):
        """设置双因子认证"""
        try:
            user = self.users.get(username)
            if not user:
                return {'success': False, 'error': '用户不存在'}

            # 生成密钥
            secret = pyotp.random_base32()
            user['two_factor_secret'] = secret

            # 生成QR码
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=user['email'],
                issuer_name="QuantTradeX"
            )

            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(totp_uri)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_str = base64.b64encode(img_buffer.getvalue()).decode()

            return {
                'success': True,
                'secret': secret,
                'qr_code': f"data:image/png;base64,{img_str}"
            }
        except Exception as e:
            logger.error(f"设置双因子认证失败: {e}")
            return {'success': False, 'error': '设置失败'}

    def verify_two_factor(self, username, token):
        """验证双因子认证"""
        try:
            user = self.users.get(username)
            if not user or not user.get('two_factor_secret'):
                return {'success': False, 'error': '双因子认证未设置'}

            totp = pyotp.TOTP(user['two_factor_secret'])
            if totp.verify(token):
                user['two_factor_enabled'] = True
                return {'success': True, 'message': '双因子认证验证成功'}
            else:
                return {'success': False, 'error': '验证码错误'}
        except Exception as e:
            logger.error(f"验证双因子认证失败: {e}")
            return {'success': False, 'error': '验证失败'}

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            return jsonify({'success': False, 'error': '请先登录'}), 401
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            return jsonify({'success': False, 'error': '请先登录'}), 401
        if session.get('role') != 'admin':
            return jsonify({'success': False, 'error': '权限不足'}), 403
        return f(*args, **kwargs)
    return decorated_function

# 全局认证服务实例
auth_service = AuthService()
