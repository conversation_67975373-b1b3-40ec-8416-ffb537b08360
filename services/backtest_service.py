#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级回测引擎服务
负责策略回测、性能分析和风险评估
"""

import numpy as np
import logging
from datetime import datetime
from config.settings import get_config

# 可选导入
try:
    import pandas as pd
except ImportError:
    pd = None

try:
    import yfinance as yf
except ImportError:
    yf = None

try:
    from scipy import stats
except ImportError:
    stats = None

logger = logging.getLogger(__name__)

class AdvancedBacktestEngine:
    """高级回测引擎"""

    def __init__(self):
        self.config = get_config()
        backtest_config = self.config.BACKTEST_CONFIG
        self.commission = backtest_config['commission']  # 手续费率
        self.slippage = backtest_config['slippage']   # 滑点
        self.min_trade_amount = backtest_config['min_trade_amount']  # 最小交易金额

    def run_advanced_backtest(self, strategy_config):
        """运行高级回测"""
        try:
            # 获取历史数据
            data = self._get_historical_data(
                strategy_config['symbol'],
                strategy_config['start_date'],
                strategy_config['end_date'],
                strategy_config.get('interval', '1d')
            )

            if data is None or data.empty:
                return {'error': '无法获取历史数据'}

            # 初始化回测环境
            portfolio = self._initialize_portfolio(strategy_config['initial_capital'])

            # 执行策略
            results = self._execute_strategy(data, strategy_config, portfolio)

            # 计算性能指标
            performance = self._calculate_performance_metrics(results, data)

            # 风险分析
            risk_metrics = self._calculate_risk_metrics(results, data)

            # 交易分析
            trade_analysis = self._analyze_trades(results['trades'])

            return {
                'success': True,
                'performance': performance,
                'risk_metrics': risk_metrics,
                'trade_analysis': trade_analysis,
                'equity_curve': results['equity_curve'],
                'trades': results['trades'],
                'drawdown_curve': results['drawdown_curve'],
                'benchmark_comparison': results.get('benchmark_comparison', {}),
                'monthly_returns': results.get('monthly_returns', []),
                'strategy_config': strategy_config
            }

        except Exception as e:
            logger.error(f"高级回测失败: {e}")
            return {'error': str(e)}

    def _get_historical_data(self, symbol, start_date, end_date, interval='1d'):
        """获取历史数据"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(start=start_date, end=end_date, interval=interval)

            if data.empty:
                return None

            # 添加技术指标
            data = self._add_technical_indicators(data)

            return data
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return None

    def _add_technical_indicators(self, data):
        """添加技术指标"""
        try:
            # 移动平均线
            data['SMA_5'] = data['Close'].rolling(window=5).mean()
            data['SMA_10'] = data['Close'].rolling(window=10).mean()
            data['SMA_20'] = data['Close'].rolling(window=20).mean()
            data['SMA_50'] = data['Close'].rolling(window=50).mean()
            data['SMA_200'] = data['Close'].rolling(window=200).mean()

            # 指数移动平均线
            data['EMA_12'] = data['Close'].ewm(span=12).mean()
            data['EMA_26'] = data['Close'].ewm(span=26).mean()

            # MACD
            data['MACD'] = data['EMA_12'] - data['EMA_26']
            data['MACD_Signal'] = data['MACD'].ewm(span=9).mean()
            data['MACD_Histogram'] = data['MACD'] - data['MACD_Signal']

            # RSI
            delta = data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['RSI'] = 100 - (100 / (1 + rs))

            # 布林带
            data['BB_Middle'] = data['Close'].rolling(window=20).mean()
            bb_std = data['Close'].rolling(window=20).std()
            data['BB_Upper'] = data['BB_Middle'] + (bb_std * 2)
            data['BB_Lower'] = data['BB_Middle'] - (bb_std * 2)
            data['BB_Width'] = (data['BB_Upper'] - data['BB_Lower']) / data['BB_Middle']
            data['BB_Position'] = (data['Close'] - data['BB_Lower']) / (data['BB_Upper'] - data['BB_Lower'])

            # ATR (平均真实波幅)
            high_low = data['High'] - data['Low']
            high_close = np.abs(data['High'] - data['Close'].shift())
            low_close = np.abs(data['Low'] - data['Close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            data['ATR'] = true_range.rolling(window=14).mean()

            # 成交量指标
            data['Volume_SMA'] = data['Volume'].rolling(window=20).mean()
            data['Volume_Ratio'] = data['Volume'] / data['Volume_SMA']

            return data
        except Exception as e:
            logger.error(f"添加技术指标失败: {e}")
            return data

    def _initialize_portfolio(self, initial_capital):
        """初始化投资组合"""
        return {
            'cash': initial_capital,
            'positions': {},
            'total_value': initial_capital,
            'initial_capital': initial_capital,
            'equity_curve': [initial_capital],
            'trades': [],
            'daily_returns': [],
            'drawdown_curve': [0]
        }

    def _execute_strategy(self, data, strategy_config, portfolio):
        """执行策略"""
        try:
            strategy_type = strategy_config.get('strategy_type', 'buy_and_hold')
            parameters = strategy_config.get('parameters', {})

            if strategy_type == 'moving_average_crossover':
                return self._execute_ma_crossover_strategy(data, parameters, portfolio)
            elif strategy_type == 'rsi_mean_reversion':
                return self._execute_rsi_strategy(data, parameters, portfolio)
            elif strategy_type == 'bollinger_bands':
                return self._execute_bollinger_strategy(data, parameters, portfolio)
            elif strategy_type == 'macd_momentum':
                return self._execute_macd_strategy(data, parameters, portfolio)
            elif strategy_type == 'custom':
                return self._execute_custom_strategy(data, strategy_config, portfolio)
            else:
                return self._execute_buy_and_hold_strategy(data, portfolio)

        except Exception as e:
            logger.error(f"执行策略失败: {e}")
            return portfolio

    def _execute_ma_crossover_strategy(self, data, parameters, portfolio):
        """执行移动平均交叉策略"""
        fast_period = parameters.get('fast_period', 10)
        slow_period = parameters.get('slow_period', 20)
        position_size = parameters.get('position_size', 0.95)  # 使用95%资金

        symbol = 'STOCK'  # 简化处理
        position = 0

        for i in range(slow_period, len(data)):
            current_price = data['Close'].iloc[i]
            fast_ma = data['Close'].iloc[i-fast_period:i].mean()
            slow_ma = data['Close'].iloc[i-slow_period:i].mean()
            prev_fast_ma = data['Close'].iloc[i-fast_period-1:i-1].mean()
            prev_slow_ma = data['Close'].iloc[i-slow_period-1:i-1].mean()

            # 金叉买入信号
            if fast_ma > slow_ma and prev_fast_ma <= prev_slow_ma and position == 0:
                shares_to_buy = int((portfolio['cash'] * position_size) / current_price)
                if shares_to_buy > 0:
                    cost = shares_to_buy * current_price * (1 + self.commission + self.slippage)
                    if cost <= portfolio['cash']:
                        portfolio['cash'] -= cost
                        position = shares_to_buy
                        portfolio['positions'][symbol] = position

                        # 记录交易
                        trade = {
                            'date': data.index[i].strftime('%Y-%m-%d'),
                            'type': 'buy',
                            'shares': shares_to_buy,
                            'price': current_price,
                            'cost': cost,
                            'signal': 'MA_Golden_Cross'
                        }
                        portfolio['trades'].append(trade)

            # 死叉卖出信号
            elif fast_ma < slow_ma and prev_fast_ma >= prev_slow_ma and position > 0:
                proceeds = position * current_price * (1 - self.commission - self.slippage)
                portfolio['cash'] += proceeds

                # 记录交易
                trade = {
                    'date': data.index[i].strftime('%Y-%m-%d'),
                    'type': 'sell',
                    'shares': position,
                    'price': current_price,
                    'proceeds': proceeds,
                    'signal': 'MA_Death_Cross'
                }
                portfolio['trades'].append(trade)

                position = 0
                portfolio['positions'][symbol] = 0

            # 更新投资组合价值
            portfolio_value = portfolio['cash'] + (position * current_price)
            portfolio['equity_curve'].append(portfolio_value)
            portfolio['total_value'] = portfolio_value

            # 计算日收益率
            if len(portfolio['equity_curve']) > 1:
                daily_return = (portfolio_value - portfolio['equity_curve'][-2]) / portfolio['equity_curve'][-2]
                portfolio['daily_returns'].append(daily_return)

            # 计算回撤
            peak = max(portfolio['equity_curve'])
            drawdown = (portfolio_value - peak) / peak
            portfolio['drawdown_curve'].append(drawdown)

        return portfolio

    def _execute_rsi_strategy(self, data, parameters, portfolio):
        """执行RSI均值回归策略"""
        rsi_oversold = parameters.get('rsi_oversold', 30)
        rsi_overbought = parameters.get('rsi_overbought', 70)
        position_size = parameters.get('position_size', 0.95)

        symbol = 'STOCK'
        position = 0

        for i in range(14, len(data)):  # RSI需要14天数据
            current_price = data['Close'].iloc[i]
            rsi = data['RSI'].iloc[i]

            # RSI超卖买入信号
            if rsi < rsi_oversold and position == 0:
                shares_to_buy = int((portfolio['cash'] * position_size) / current_price)
                if shares_to_buy > 0:
                    cost = shares_to_buy * current_price * (1 + self.commission + self.slippage)
                    if cost <= portfolio['cash']:
                        portfolio['cash'] -= cost
                        position = shares_to_buy
                        portfolio['positions'][symbol] = position

                        trade = {
                            'date': data.index[i].strftime('%Y-%m-%d'),
                            'type': 'buy',
                            'shares': shares_to_buy,
                            'price': current_price,
                            'cost': cost,
                            'signal': f'RSI_Oversold_{rsi:.1f}'
                        }
                        portfolio['trades'].append(trade)

            # RSI超买卖出信号
            elif rsi > rsi_overbought and position > 0:
                proceeds = position * current_price * (1 - self.commission - self.slippage)
                portfolio['cash'] += proceeds

                trade = {
                    'date': data.index[i].strftime('%Y-%m-%d'),
                    'type': 'sell',
                    'shares': position,
                    'price': current_price,
                    'proceeds': proceeds,
                    'signal': f'RSI_Overbought_{rsi:.1f}'
                }
                portfolio['trades'].append(trade)

                position = 0
                portfolio['positions'][symbol] = 0

            # 更新投资组合
            portfolio_value = portfolio['cash'] + (position * current_price)
            portfolio['equity_curve'].append(portfolio_value)
            portfolio['total_value'] = portfolio_value

            if len(portfolio['equity_curve']) > 1:
                daily_return = (portfolio_value - portfolio['equity_curve'][-2]) / portfolio['equity_curve'][-2]
                portfolio['daily_returns'].append(daily_return)

            peak = max(portfolio['equity_curve'])
            drawdown = (portfolio_value - peak) / peak
            portfolio['drawdown_curve'].append(drawdown)

        return portfolio
