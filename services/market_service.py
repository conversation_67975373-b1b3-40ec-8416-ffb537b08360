#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
市场数据服务
负责获取股票数据、计算技术指标等
"""

import json
import numpy as np
import logging
from datetime import datetime
from config.database import db_manager

# 可选导入
try:
    import pandas as pd
except ImportError:
    pd = None

try:
    import yfinance as yf
except ImportError:
    yf = None

logger = logging.getLogger(__name__)

class QuantTradeXService:
    """量化交易核心服务"""

    def __init__(self):
        self.redis_client = None
        self.db_connection = None
        self.init_connections()

    def init_connections(self):
        """初始化数据库连接"""
        self.redis_client = db_manager.get_redis_client()
        self.db_connection = db_manager.get_db_connection()

    def get_stock_data(self, symbol, period='1mo'):
        """获取股票数据"""
        try:
            # 先从缓存获取
            cache_key = f"stock_data:{symbol}:{period}"
            if self.redis_client:
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)

            # 尝试从Yahoo Finance获取数据
            if yf is not None:
                try:
                    ticker = yf.Ticker(symbol)
                    hist = ticker.history(period=period)

                    if not hist.empty:
                        # 转换为JSON格式
                        data = {
                            'symbol': symbol,
                            'dates': hist.index.strftime('%Y-%m-%d').tolist(),
                            'open': hist['Open'].tolist(),
                            'high': hist['High'].tolist(),
                            'low': hist['Low'].tolist(),
                            'close': hist['Close'].tolist(),
                            'volume': hist['Volume'].tolist(),
                            'last_update': datetime.now().isoformat()
                        }

                        # 缓存数据（5分钟）
                        if self.redis_client:
                            self.redis_client.setex(cache_key, 300, json.dumps(data))

                        return data
                except Exception as yf_error:
                    logger.warning(f"Yahoo Finance获取失败: {yf_error}, 使用模拟数据")

            # 如果Yahoo Finance失败，使用模拟数据
            return self._generate_mock_stock_data(symbol, period)

        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return self._generate_mock_stock_data(symbol, period)

    def _generate_mock_stock_data(self, symbol, period='1mo'):
        """生成模拟股票数据"""
        import random
        from datetime import datetime, timedelta

        # 基础价格
        base_prices = {
            'AAPL': 150.0,
            'GOOGL': 2800.0,
            'MSFT': 380.0,
            'TSLA': 250.0,
            'AMZN': 3200.0,
            'META': 320.0,
            'NVDA': 450.0,
            'NFLX': 400.0
        }

        base_price = base_prices.get(symbol.upper(), 100.0)

        # 生成日期序列
        if period == '1mo':
            days = 30
        elif period == '3mo':
            days = 90
        elif period == '1y':
            days = 365
        else:
            days = 30

        end_date = datetime.now()
        dates = []
        for i in range(days):
            date = end_date - timedelta(days=days-i-1)
            dates.append(date.strftime('%Y-%m-%d'))

        # 生成价格数据
        prices = []
        current_price = base_price

        for i in range(days):
            # 随机波动 -2% 到 +2%
            change = random.uniform(-0.02, 0.02)
            current_price = current_price * (1 + change)
            prices.append(current_price)

        # 生成OHLC数据
        opens = []
        highs = []
        lows = []
        closes = []
        volumes = []

        for i, price in enumerate(prices):
            if i == 0:
                open_price = price
            else:
                open_price = closes[i-1] * random.uniform(0.995, 1.005)

            close_price = price
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.02)
            low_price = min(open_price, close_price) * random.uniform(0.98, 1.0)
            volume = random.randint(1000000, 10000000)

            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)

        data = {
            'symbol': symbol,
            'dates': dates,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'last_update': datetime.now().isoformat(),
            'source': 'MOCK_DATA'
        }

        return data

    def calculate_indicators(self, data):
        """计算技术指标"""
        try:
            if not data or len(data['close']) < 20:
                return {}

            closes = np.array(data['close'])

            # 简单移动平均线
            sma_20 = np.convolve(closes, np.ones(20)/20, mode='valid')
            sma_50 = np.convolve(closes, np.ones(50)/50, mode='valid') if len(closes) >= 50 else []

            # RSI计算
            def calculate_rsi(prices, period=14):
                deltas = np.diff(prices)
                gains = np.where(deltas > 0, deltas, 0)
                losses = np.where(deltas < 0, -deltas, 0)

                avg_gains = np.convolve(gains, np.ones(period)/period, mode='valid')
                avg_losses = np.convolve(losses, np.ones(period)/period, mode='valid')

                rs = avg_gains / (avg_losses + 1e-10)
                rsi = 100 - (100 / (1 + rs))
                return rsi

            rsi = calculate_rsi(closes) if len(closes) > 14 else []

            return {
                'sma_20': sma_20.tolist() if hasattr(sma_20, 'tolist') else list(sma_20),
                'sma_50': sma_50.tolist() if hasattr(sma_50, 'tolist') else list(sma_50),
                'rsi': rsi.tolist() if hasattr(rsi, 'tolist') else list(rsi),
                'current_price': float(closes[-1]),
                'price_change': float(closes[-1] - closes[-2]) if len(closes) > 1 else 0,
                'price_change_pct': float(((closes[-1] - closes[-2]) / closes[-2] * 100)) if len(closes) > 1 else 0
            }
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}

    def run_backtest(self, strategy_code, symbol, start_date, end_date, initial_capital=100000, parameters=None):
        """运行回测"""
        try:
            # 获取历史数据
            ticker = yf.Ticker(symbol)
            hist = ticker.history(start=start_date, end=end_date)

            if hist.empty:
                return {'error': '无法获取历史数据'}

            # 这里应该实现策略执行逻辑
            # 简化版本：计算买入持有策略的收益
            initial_price = hist['Close'].iloc[0]
            final_price = hist['Close'].iloc[-1]
            total_return = (final_price - initial_price) / initial_price * 100

            results = {
                'total_return': total_return,
                'final_value': initial_capital * (1 + total_return / 100),
                'max_drawdown': 0,  # 简化
                'sharpe_ratio': 0,  # 简化
                'trades_count': 1,
                'win_rate': 100 if total_return > 0 else 0,
                'start_date': start_date,
                'end_date': end_date,
                'symbol': symbol
            }

            return results
        except Exception as e:
            logger.error(f"回测失败: {e}")
            return {'error': str(e)}

    def get_market_overview(self):
        """获取市场概览"""
        try:
            # 主要指数
            indices = ['SPY', 'QQQ', 'IWM', 'VTI']
            market_data = {}

            for symbol in indices:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period='2d')
                if not hist.empty:
                    current = hist['Close'].iloc[-1]
                    previous = hist['Close'].iloc[-2] if len(hist) > 1 else current
                    change = current - previous
                    change_pct = (change / previous) * 100

                    market_data[symbol] = {
                        'price': float(current),
                        'change': float(change),
                        'change_percent': float(change_pct),
                        'volume': int(hist['Volume'].iloc[-1])
                    }

            return {
                'success': True,
                'data': market_data,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_sector_performance(self):
        """获取行业表现"""
        try:
            # 行业ETF
            sectors = {
                'XLK': '科技',
                'XLF': '金融',
                'XLV': '医疗',
                'XLE': '能源',
                'XLI': '工业',
                'XLY': '消费',
                'XLP': '必需消费品',
                'XLU': '公用事业',
                'XLB': '材料'
            }

            sector_data = {}
            for symbol, name in sectors.items():
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period='5d')
                if not hist.empty:
                    current = hist['Close'].iloc[-1]
                    week_ago = hist['Close'].iloc[0]
                    change_pct = ((current - week_ago) / week_ago) * 100

                    sector_data[symbol] = {
                        'name': name,
                        'symbol': symbol,
                        'price': float(current),
                        'change_percent_5d': float(change_pct)
                    }

            return {
                'success': True,
                'data': sector_data,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取行业表现失败: {e}")
            return {'success': False, 'error': str(e)}

    def search_stocks(self, query):
        """搜索股票"""
        try:
            # 简化的股票搜索
            # 在实际应用中，这里应该连接到股票数据库或API
            common_stocks = {
                'AAPL': 'Apple Inc.',
                'GOOGL': 'Alphabet Inc.',
                'MSFT': 'Microsoft Corporation',
                'AMZN': 'Amazon.com Inc.',
                'TSLA': 'Tesla Inc.',
                'META': 'Meta Platforms Inc.',
                'NVDA': 'NVIDIA Corporation',
                'NFLX': 'Netflix Inc.',
                'SPY': 'SPDR S&P 500 ETF',
                'QQQ': 'Invesco QQQ Trust'
            }

            results = []
            query_upper = query.upper()

            for symbol, name in common_stocks.items():
                if query_upper in symbol or query_upper in name.upper():
                    results.append({
                        'symbol': symbol,
                        'name': name,
                        'type': 'ETF' if symbol in ['SPY', 'QQQ'] else 'Stock'
                    })

            return {
                'success': True,
                'results': results[:10],  # 限制返回10个结果
                'query': query
            }
        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            return {'success': False, 'error': str(e)}
