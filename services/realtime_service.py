#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时数据流服务
负责处理WebSocket连接、数据订阅和实时数据推送
"""

import threading
import time
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from config.settings import get_config

# 可选导入
try:
    import numpy as np
except ImportError:
    np = None

try:
    import yfinance as yf
except ImportError:
    yf = None

logger = logging.getLogger(__name__)

class RealTimeDataService:
    """实时数据流服务"""

    def __init__(self, socketio_instance):
        self.socketio = socketio_instance
        self.config = get_config()
        self.active_connections = {}  # 活跃连接
        self.subscriptions = {}  # 订阅管理
        self.data_cache = {}  # 数据缓存
        self.cache_timestamps = {}  # 缓存时间戳
        self.executor = ThreadPoolExecutor(max_workers=self.config.REALTIME_CONFIG['max_workers'])
        self.running = False
        self.update_thread = None

        # 集成统一API
        try:
            from api_config import unified_api
            self.unified_api = unified_api
            logger.info("实时数据服务已集成统一API")
        except ImportError:
            self.unified_api = None
            logger.warning("统一API不可用，将使用备用数据源")

    def start_service(self):
        """启动实时数据服务"""
        if not self.running:
            self.running = True
            self.update_thread = threading.Thread(target=self._data_update_loop, daemon=True)
            self.update_thread.start()
            logger.info("实时数据服务已启动")

    def stop_service(self):
        """停止实时数据服务"""
        self.running = False
        if self.update_thread:
            self.update_thread.join()
        logger.info("实时数据服务已停止")

    def add_connection(self, sid, user_info=None):
        """添加连接"""
        self.active_connections[sid] = {
            'connected_at': datetime.now(),
            'user_info': user_info,
            'subscriptions': set()
        }
        logger.info(f"新连接已添加: {sid}")

    def remove_connection(self, sid):
        """移除连接"""
        if sid in self.active_connections:
            # 清理订阅
            for symbol in self.active_connections[sid]['subscriptions']:
                self.unsubscribe_symbol(sid, symbol)
            del self.active_connections[sid]
            logger.info(f"连接已移除: {sid}")

    def subscribe_symbol(self, sid, symbol, data_type='stock'):
        """订阅股票/数据"""
        if sid not in self.active_connections:
            return False

        subscription_key = f"{data_type}:{symbol}"

        # 添加到连接的订阅列表
        self.active_connections[sid]['subscriptions'].add(subscription_key)

        # 添加到全局订阅管理
        if subscription_key not in self.subscriptions:
            self.subscriptions[subscription_key] = set()
        self.subscriptions[subscription_key].add(sid)

        # 立即发送当前数据
        self._send_current_data(sid, symbol, data_type)

        logger.info(f"订阅添加: {sid} -> {subscription_key}")
        return True

    def unsubscribe_symbol(self, sid, symbol, data_type='stock'):
        """取消订阅"""
        subscription_key = f"{data_type}:{symbol}"

        if sid in self.active_connections:
            self.active_connections[sid]['subscriptions'].discard(subscription_key)

        if subscription_key in self.subscriptions:
            self.subscriptions[subscription_key].discard(sid)
            if not self.subscriptions[subscription_key]:
                del self.subscriptions[subscription_key]

        logger.info(f"订阅移除: {sid} -> {subscription_key}")

    def _send_current_data(self, sid, symbol, data_type):
        """发送当前数据"""
        try:
            if data_type == 'stock':
                data = self._get_stock_realtime_data(symbol)
            elif data_type == 'crypto':
                data = self._get_crypto_realtime_data(symbol)
            else:
                data = None

            if data:
                self.socketio.emit('market_data', {
                    'symbol': symbol,
                    'type': data_type,
                    'data': data,
                    'timestamp': datetime.now().isoformat()
                }, room=sid)
        except Exception as e:
            logger.error(f"发送当前数据失败: {e}")

    def _get_stock_realtime_data(self, symbol):
        """获取股票实时数据"""
        cache_key = f"realtime_stock:{symbol}"
        now = datetime.now()
        cache_timeout = self.config.REALTIME_CONFIG['cache_timeout']

        # 检查缓存
        if (cache_key in self.cache_timestamps and
            (now - self.cache_timestamps[cache_key]).seconds < cache_timeout):
            return self.data_cache.get(cache_key)

        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period='1d', interval='1m')

            if not hist.empty:
                latest = hist.iloc[-1]
                data = {
                    'price': float(latest['Close']),
                    'open': float(latest['Open']),
                    'high': float(latest['High']),
                    'low': float(latest['Low']),
                    'volume': int(latest['Volume']),
                    'change': 0,
                    'change_percent': 0,
                    'market_cap': info.get('marketCap', 0),
                    'pe_ratio': info.get('trailingPE', 0),
                    'last_update': now.isoformat()
                }

                # 计算涨跌
                if len(hist) > 1:
                    prev_close = hist.iloc[-2]['Close']
                    data['change'] = float(latest['Close'] - prev_close)
                    data['change_percent'] = float((data['change'] / prev_close) * 100)

                # 缓存数据
                self.data_cache[cache_key] = data
                self.cache_timestamps[cache_key] = now

                return data
        except Exception as e:
            logger.error(f"获取股票实时数据失败 {symbol}: {e}")

        return None

    def _get_crypto_realtime_data(self, symbol):
        """获取加密货币实时数据"""
        cache_key = f"realtime_crypto:{symbol}"
        now = datetime.now()
        cache_timeout = self.config.REALTIME_CONFIG['cache_timeout']

        # 检查缓存
        if (cache_key in self.cache_timestamps and
            (now - self.cache_timestamps[cache_key]).seconds < cache_timeout):
            return self.data_cache.get(cache_key)

        try:
            # 使用统一API获取真实数据
            if hasattr(self, 'unified_api') and self.unified_api:
                # 映射符号到CoinGecko ID
                symbol_mapping = {
                    'BTC': 'bitcoin',
                    'ETH': 'ethereum',
                    'BNB': 'binancecoin',
                    'ADA': 'cardano',
                    'SOL': 'solana',
                    'DOT': 'polkadot',
                    'MATIC': 'polygon',
                    'AVAX': 'avalanche-2',
                    'LINK': 'chainlink',
                    'UNI': 'uniswap'
                }

                crypto_id = symbol_mapping.get(symbol.upper(), symbol.lower())
                crypto_data = self.unified_api.get_crypto_data(crypto_id)

                if crypto_data and crypto_data.get('success'):
                    data = crypto_data['data']
                    result = {
                        'price': data.get('price', 0),
                        'change': data.get('change_24h', 0),
                        'change_percent': data.get('change_24h_percent', 0),
                        'volume': data.get('volume_24h', 0),
                        'market_cap': data.get('market_cap', 0),
                        'last_update': datetime.now().isoformat(),
                        'source': 'CoinGecko'
                    }

                    # 缓存数据
                    self.data_cache[cache_key] = result
                    self.cache_timestamps[cache_key] = now
                    return result

            # 如果API不可用，使用CoinGecko直接API
            import requests
            symbol_mapping = {
                'BTC': 'bitcoin',
                'ETH': 'ethereum',
                'BNB': 'binancecoin',
                'ADA': 'cardano',
                'SOL': 'solana'
            }

            crypto_id = symbol_mapping.get(symbol.upper(), 'bitcoin')
            url = f"https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': crypto_id,
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true',
                'include_market_cap': 'true'
            }

            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            data = response.json()

            if crypto_id in data:
                crypto_info = data[crypto_id]
                result = {
                    'price': crypto_info.get('usd', 0),
                    'change': crypto_info.get('usd_24h_change', 0),
                    'change_percent': crypto_info.get('usd_24h_change', 0),
                    'volume': crypto_info.get('usd_24h_vol', 0),
                    'market_cap': crypto_info.get('usd_market_cap', 0),
                    'last_update': datetime.now().isoformat(),
                    'source': 'CoinGecko_Direct'
                }

                # 缓存数据
                self.data_cache[cache_key] = result
                self.cache_timestamps[cache_key] = now
                return result

        except Exception as e:
            logger.error(f"获取加密货币实时数据失败 {symbol}: {e}")

        # 如果所有方法都失败，返回模拟数据（但标记为模拟）
        import random
        base_prices = {
            'BTC': 43000,  # 更接近真实价格
            'ETH': 2600,
            'BNB': 300,
            'ADA': 0.5,
            'SOL': 100
        }

        base_price = base_prices.get(symbol.upper(), 1000)

        return {
            'price': base_price + random.uniform(-base_price*0.02, base_price*0.02),
            'change': random.uniform(-base_price*0.05, base_price*0.05),
            'change_percent': random.uniform(-5, 5),
            'volume': random.randint(1000000, 100000000),
            'last_update': datetime.now().isoformat(),
            'source': 'SIMULATED_DATA'  # 标记为模拟数据
        }

    def _data_update_loop(self):
        """数据更新循环"""
        update_interval = self.config.REALTIME_CONFIG['update_interval']

        while self.running:
            try:
                # 获取所有需要更新的订阅
                for subscription_key, sids in self.subscriptions.items():
                    if not sids:  # 没有订阅者
                        continue

                    data_type, symbol = subscription_key.split(':', 1)

                    # 获取最新数据
                    if data_type == 'stock':
                        data = self._get_stock_realtime_data(symbol)
                    elif data_type == 'crypto':
                        data = self._get_crypto_realtime_data(symbol)
                    else:
                        continue

                    if data:
                        # 广播给所有订阅者
                        for sid in list(sids):  # 使用list避免运行时修改
                            try:
                                self.socketio.emit('market_data', {
                                    'symbol': symbol,
                                    'type': data_type,
                                    'data': data,
                                    'timestamp': datetime.now().isoformat()
                                }, room=sid)
                            except Exception as e:
                                logger.error(f"发送数据到 {sid} 失败: {e}")
                                # 移除无效连接
                                self.remove_connection(sid)

                # 等待下次更新
                time.sleep(update_interval)

            except Exception as e:
                logger.error(f"数据更新循环错误: {e}")
                time.sleep(10)  # 错误时等待更长时间

    def get_connection_stats(self):
        """获取连接统计"""
        return {
            'total_connections': len(self.active_connections),
            'total_subscriptions': sum(len(subs) for subs in self.subscriptions.values()),
            'unique_symbols': len(self.subscriptions),
            'cache_size': len(self.data_cache)
        }
