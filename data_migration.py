#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantTradeX 数据迁移脚本
用途: 从源服务器导出数据并导入到新服务器
目标: www.gdpp.com
"""

import json
import os
import sys
import shutil
import argparse
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataMigration:
    def __init__(self, source_path=None, target_path=None):
        self.source_path = source_path or '/www/wwwroot/qclb.com'
        self.target_path = target_path or '/www/wwwroot/www.gdpp.com'
        self.backup_dir = f'migration_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        
    def export_user_data(self):
        """导出用户数据"""
        logger.info("开始导出用户数据...")
        
        try:
            # 导入用户管理器
            sys.path.append(self.source_path)
            from user_manager import UserManager
            
            um = UserManager()
            users = um.get_all_users()
            
            # 创建备份目录
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 导出用户数据
            users_file = os.path.join(self.backup_dir, 'users_export.json')
            with open(users_file, 'w', encoding='utf-8') as f:
                json.dump(users, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"用户数据导出完成: {users_file}")
            logger.info(f"导出用户数量: {len(users)}")
            
            return users_file
            
        except Exception as e:
            logger.error(f"用户数据导出失败: {e}")
            return None
    
    def export_strategy_data(self):
        """导出策略数据"""
        logger.info("开始导出策略数据...")
        
        try:
            strategy_files = [
                'strategy_codes_mapping.json',
                'forum_api_data.json',
                'user_strategies.json'
            ]
            
            exported_files = []
            
            for filename in strategy_files:
                source_file = os.path.join(self.source_path, filename)
                if os.path.exists(source_file):
                    target_file = os.path.join(self.backup_dir, filename)
                    shutil.copy2(source_file, target_file)
                    exported_files.append(target_file)
                    logger.info(f"策略文件导出: {filename}")
                else:
                    logger.warning(f"策略文件不存在: {filename}")
            
            logger.info(f"策略数据导出完成，共导出 {len(exported_files)} 个文件")
            return exported_files
            
        except Exception as e:
            logger.error(f"策略数据导出失败: {e}")
            return []
    
    def export_configuration(self):
        """导出配置文件"""
        logger.info("开始导出配置文件...")
        
        try:
            config_files = [
                'app.py',
                'user_manager.py',
                'database_init.py',
                'config.py'
            ]
            
            config_dir = os.path.join(self.backup_dir, 'config')
            os.makedirs(config_dir, exist_ok=True)
            
            exported_configs = []
            
            for filename in config_files:
                source_file = os.path.join(self.source_path, filename)
                if os.path.exists(source_file):
                    target_file = os.path.join(config_dir, filename)
                    shutil.copy2(source_file, target_file)
                    exported_configs.append(target_file)
                    logger.info(f"配置文件导出: {filename}")
            
            # 导出静态文件和模板
            for dirname in ['static', 'templates']:
                source_dir = os.path.join(self.source_path, dirname)
                if os.path.exists(source_dir):
                    target_dir = os.path.join(config_dir, dirname)
                    shutil.copytree(source_dir, target_dir)
                    logger.info(f"目录导出: {dirname}")
            
            logger.info("配置文件导出完成")
            return config_dir
            
        except Exception as e:
            logger.error(f"配置文件导出失败: {e}")
            return None
    
    def import_user_data(self, users_file):
        """导入用户数据到新服务器"""
        logger.info("开始导入用户数据...")
        
        try:
            # 导入新服务器的用户管理器
            sys.path.append(self.target_path)
            from user_manager import UserManager
            
            # 读取用户数据
            with open(users_file, 'r', encoding='utf-8') as f:
                users = json.load(f)
            
            um = UserManager()
            imported_count = 0
            
            for user in users:
                try:
                    # 检查用户是否已存在
                    if not um.user_exists(user.get('email', '')):
                        um.create_user(user)
                        imported_count += 1
                        logger.info(f"用户导入成功: {user.get('username', 'Unknown')}")
                    else:
                        logger.warning(f"用户已存在，跳过: {user.get('username', 'Unknown')}")
                except Exception as e:
                    logger.error(f"用户导入失败: {user.get('username', 'Unknown')} - {e}")
            
            logger.info(f"用户数据导入完成，成功导入 {imported_count} 个用户")
            return imported_count
            
        except Exception as e:
            logger.error(f"用户数据导入失败: {e}")
            return 0
    
    def import_strategy_data(self, strategy_files):
        """导入策略数据到新服务器"""
        logger.info("开始导入策略数据...")
        
        try:
            imported_count = 0
            
            for source_file in strategy_files:
                filename = os.path.basename(source_file)
                target_file = os.path.join(self.target_path, filename)
                
                # 备份现有文件
                if os.path.exists(target_file):
                    backup_file = f"{target_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(target_file, backup_file)
                    logger.info(f"备份现有文件: {backup_file}")
                
                # 复制新文件
                shutil.copy2(source_file, target_file)
                imported_count += 1
                logger.info(f"策略文件导入: {filename}")
            
            logger.info(f"策略数据导入完成，共导入 {imported_count} 个文件")
            return imported_count
            
        except Exception as e:
            logger.error(f"策略数据导入失败: {e}")
            return 0
    
    def update_domain_references(self):
        """更新域名引用"""
        logger.info("开始更新域名引用...")
        
        try:
            # 需要更新的文件类型
            file_patterns = [
                ('templates', '*.html'),
                ('static/js', '*.js'),
                ('static/css', '*.css'),
                ('', '*.py')
            ]
            
            updated_files = 0
            
            for directory, pattern in file_patterns:
                search_dir = os.path.join(self.target_path, directory) if directory else self.target_path
                
                if os.path.exists(search_dir):
                    for root, dirs, files in os.walk(search_dir):
                        for file in files:
                            if pattern == '*.html' and file.endswith('.html'):
                                self._update_file_domain(os.path.join(root, file))
                                updated_files += 1
                            elif pattern == '*.js' and file.endswith('.js'):
                                self._update_file_domain(os.path.join(root, file))
                                updated_files += 1
                            elif pattern == '*.css' and file.endswith('.css'):
                                self._update_file_domain(os.path.join(root, file))
                                updated_files += 1
                            elif pattern == '*.py' and file.endswith('.py'):
                                self._update_file_domain(os.path.join(root, file))
                                updated_files += 1
            
            logger.info(f"域名引用更新完成，共更新 {updated_files} 个文件")
            return updated_files
            
        except Exception as e:
            logger.error(f"域名引用更新失败: {e}")
            return 0
    
    def _update_file_domain(self, filepath):
        """更新单个文件中的域名引用"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换域名
            updated_content = content.replace('qclb.com', 'gdpp.com')
            updated_content = updated_content.replace('www.qclb.com', 'www.gdpp.com')
            
            if content != updated_content:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                logger.debug(f"更新文件域名: {filepath}")
                
        except Exception as e:
            logger.error(f"更新文件域名失败 {filepath}: {e}")
    
    def create_migration_report(self):
        """创建迁移报告"""
        report_file = os.path.join(self.backup_dir, 'migration_report.md')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"""# QuantTradeX 数据迁移报告

## 迁移信息
- 迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 源服务器: qclb.com
- 目标服务器: www.gdpp.com
- 备份目录: {self.backup_dir}

## 迁移内容
- [x] 用户数据
- [x] 策略数据
- [x] 配置文件
- [x] 静态资源
- [x] 模板文件
- [x] 域名引用更新

## 验证步骤
1. 检查网站是否可以正常访问
2. 测试用户登录功能
3. 验证策略市场功能
4. 检查我的策略页面
5. 测试策略开发工具
6. 验证回测系统
7. 检查实时数据功能
8. 测试社区论坛

## 注意事项
- 请在宝塔面板中配置SSL证书
- 确保DNS解析指向新服务器
- 检查防火墙和安全设置
- 监控系统性能和日志

## 联系支持
如有问题，请查看migration.log日志文件
""")
        
        logger.info(f"迁移报告已创建: {report_file}")
        return report_file

def main():
    parser = argparse.ArgumentParser(description='QuantTradeX 数据迁移工具')
    parser.add_argument('--action', choices=['export', 'import', 'full'], 
                       default='full', help='执行的操作类型')
    parser.add_argument('--source', help='源服务器路径')
    parser.add_argument('--target', help='目标服务器路径')
    parser.add_argument('--backup-dir', help='备份目录')
    
    args = parser.parse_args()
    
    migration = DataMigration(args.source, args.target)
    
    if args.backup_dir:
        migration.backup_dir = args.backup_dir
    
    if args.action in ['export', 'full']:
        logger.info("🚀 开始数据导出...")
        
        # 导出用户数据
        users_file = migration.export_user_data()
        
        # 导出策略数据
        strategy_files = migration.export_strategy_data()
        
        # 导出配置文件
        config_dir = migration.export_configuration()
        
        logger.info("✅ 数据导出完成")
    
    if args.action in ['import', 'full']:
        logger.info("🔄 开始数据导入...")
        
        # 导入用户数据
        if os.path.exists(os.path.join(migration.backup_dir, 'users_export.json')):
            migration.import_user_data(os.path.join(migration.backup_dir, 'users_export.json'))
        
        # 导入策略数据
        strategy_files = []
        for filename in ['strategy_codes_mapping.json', 'forum_api_data.json', 'user_strategies.json']:
            filepath = os.path.join(migration.backup_dir, filename)
            if os.path.exists(filepath):
                strategy_files.append(filepath)
        
        if strategy_files:
            migration.import_strategy_data(strategy_files)
        
        # 更新域名引用
        migration.update_domain_references()
        
        logger.info("✅ 数据导入完成")
    
    # 创建迁移报告
    migration.create_migration_report()
    
    logger.info("🎉 数据迁移完成！")
    logger.info(f"备份目录: {migration.backup_dir}")
    logger.info("请查看migration_report.md了解详细信息")

if __name__ == '__main__':
    main()
