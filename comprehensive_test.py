#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QuantTradeX 全面功能测试脚本
系统性测试所有页面和API功能
"""

import requests
import json
import time
from datetime import datetime

class QuantTradeXTester:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, status, message="", data=None):
        """记录测试结果"""
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {message}")
        
    def test_page_access(self, path, page_name, expected_content=None):
        """测试页面访问"""
        try:
            response = self.session.get(f"{self.base_url}{path}")
            if response.status_code == 200:
                content = response.text
                if expected_content and expected_content not in content:
                    self.log_test(f"{page_name}页面内容", "WARN", f"缺少预期内容: {expected_content}")
                else:
                    self.log_test(f"{page_name}页面访问", "PASS", f"状态码: {response.status_code}")
                return True
            else:
                self.log_test(f"{page_name}页面访问", "FAIL", f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test(f"{page_name}页面访问", "FAIL", f"异常: {str(e)}")
            return False
    
    def test_api_endpoint(self, path, api_name, method="GET", data=None, expected_keys=None):
        """测试API接口"""
        try:
            if method == "GET":
                response = self.session.get(f"{self.base_url}{path}")
            elif method == "POST":
                response = self.session.post(f"{self.base_url}{path}", json=data)
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    if expected_keys:
                        missing_keys = [key for key in expected_keys if key not in json_data]
                        if missing_keys:
                            self.log_test(f"{api_name} API", "WARN", f"缺少字段: {missing_keys}", json_data)
                        else:
                            self.log_test(f"{api_name} API", "PASS", "响应正常", json_data)
                    else:
                        self.log_test(f"{api_name} API", "PASS", "响应正常", json_data)
                    return json_data
                except json.JSONDecodeError:
                    self.log_test(f"{api_name} API", "FAIL", "响应不是有效JSON")
                    return None
            else:
                self.log_test(f"{api_name} API", "FAIL", f"状态码: {response.status_code}")
                return None
        except Exception as e:
            self.log_test(f"{api_name} API", "FAIL", f"异常: {str(e)}")
            return None
    
    def run_comprehensive_test(self):
        """运行全面测试"""
        print("🧪 开始QuantTradeX全面功能测试")
        print("=" * 60)
        
        # 1. 测试主要页面
        print("\n📄 1. 测试主要页面访问")
        print("-" * 30)
        pages = [
            ("/", "主页", "QuantTradeX"),
            ("/strategies", "策略市场", "策略市场"),
            ("/backtest", "回测系统", "回测"),
            ("/forum", "社区论坛", "论坛"),
            ("/datacenter", "数据中心", "数据中心"),
            ("/realtime", "实时数据", "实时数据"),
            ("/strategy-editor", "策略编辑器", "策略开发"),
            ("/security", "安全设置", "安全设置"),
            ("/advanced_backtest", "高级回测", "高级回测")
        ]
        
        for path, name, expected in pages:
            self.test_page_access(path, name, expected)
        
        # 2. 测试认证API
        print("\n🔐 2. 测试认证相关API")
        print("-" * 30)
        auth_apis = [
            ("/auth/check", "认证状态检查", ["success", "authenticated"]),
            ("/auth/status", "用户状态", ["success"])
        ]
        
        for path, name, expected_keys in auth_apis:
            self.test_api_endpoint(path, name, expected_keys=expected_keys)
        
        # 3. 测试数据API
        print("\n📊 3. 测试数据相关API")
        print("-" * 30)
        data_apis = [
            ("/api/forum/posts", "论坛帖子", ["success", "posts"]),
            ("/api/strategies/1", "策略详情", ["success", "strategy"]),
            ("/api/stock/AAPL", "股票数据", ["success"]),
            ("/api/realtime/crypto/BTC", "加密货币数据", None),
            ("/api/watchlist", "关注列表", None)
        ]
        
        for path, name, expected_keys in data_apis:
            self.test_api_endpoint(path, name, expected_keys=expected_keys)
        
        # 4. 测试用户功能API
        print("\n👤 4. 测试用户功能API")
        print("-" * 30)
        
        # 测试注册
        register_data = {
            "username": f"test_user_{int(time.time())}",
            "email": f"test_{int(time.time())}@example.com",
            "password": "test123456",
            "full_name": "测试用户"
        }
        register_result = self.test_api_endpoint("/auth/register", "用户注册", "POST", register_data, ["success"])
        
        if register_result and register_result.get("success"):
            # 测试登录
            login_data = {
                "username": register_data["username"],
                "password": register_data["password"]
            }
            login_result = self.test_api_endpoint("/auth/login", "用户登录", "POST", login_data, ["success"])
        
        # 5. 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 测试报告总结")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warning_tests = len([r for r in self.test_results if r['status'] == 'WARN'])
        
        print(f"总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⚠️ 警告: {warning_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  - {result['test']}: {result['message']}")
        
        if warning_tests > 0:
            print("\n⚠️ 需要注意的测试:")
            for result in self.test_results:
                if result['status'] == 'WARN':
                    print(f"  - {result['test']}: {result['message']}")
        
        # 保存详细报告
        with open('test_report.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        print(f"\n📄 详细测试报告已保存到: test_report.json")

if __name__ == "__main__":
    tester = QuantTradeXTester()
    tester.run_comprehensive_test()
