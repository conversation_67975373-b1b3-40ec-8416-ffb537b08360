{"1": {"name": "双均线策略", "code": "def strategy(data):\n    # 计算移动平均线\n    sma_20 = data['close'].rolling(20).mean()\n    sma_50 = data['close'].rolling(50).mean()\n\n    # 生成交易信号\n    signals = []\n    for i in range(len(data)):\n        if sma_20.iloc[i] > sma_50.iloc[i]:\n            signals.append('buy')\n        else:\n            signals.append('sell')\n\n    return signals"}, "2": {"name": "RSI均值回归策略", "code": "def strategy(data):\n    # 计算RSI指标\n    def calculate_rsi(prices, period=14):\n        delta = prices.diff()\n        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()\n        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()\n        rs = gain / loss\n        rsi = 100 - (100 / (1 + rs))\n        return rsi\n\n    rsi = calculate_rsi(data['close'])\n    signals = []\n\n    for i in range(len(data)):\n        if rsi.iloc[i] < 30:  # 超卖\n            signals.append('buy')\n        elif rsi.iloc[i] > 70:  # 超买\n            signals.append('sell')\n        else:\n            signals.append('hold')\n\n    return signals"}, "3": {"name": "布林带策略", "code": "def strategy(data):\n    # 计算布林带\n    period = 20\n    std_dev = 2\n\n    sma = data['close'].rolling(period).mean()\n    std = data['close'].rolling(period).std()\n\n    upper_band = sma + (std * std_dev)\n    lower_band = sma - (std * std_dev)\n\n    signals = []\n    for i in range(len(data)):\n        if data['close'].iloc[i] < lower_band.iloc[i]:\n            signals.append('buy')  # 价格触及下轨，买入\n        elif data['close'].iloc[i] > upper_band.iloc[i]:\n            signals.append('sell')  # 价格触及上轨，卖出\n        else:\n            signals.append('hold')\n\n    return signals"}, "4": {"name": "MACD动量策略", "code": "def strategy(data):\n    # 计算MACD指标\n    def calculate_macd(prices, fast=12, slow=26, signal=9):\n        ema_fast = prices.ewm(span=fast).mean()\n        ema_slow = prices.ewm(span=slow).mean()\n        macd_line = ema_fast - ema_slow\n        signal_line = macd_line.ewm(span=signal).mean()\n        histogram = macd_line - signal_line\n        return macd_line, signal_line, histogram\n\n    macd, signal, histogram = calculate_macd(data['close'])\n    signals = []\n\n    for i in range(1, len(data)):\n        # MACD金叉买入，死叉卖出\n        if macd.iloc[i] > signal.iloc[i] and macd.iloc[i-1] <= signal.iloc[i-1]:\n            signals.append('buy')\n        elif macd.iloc[i] < signal.iloc[i] and macd.iloc[i-1] >= signal.iloc[i-1]:\n            signals.append('sell')\n        else:\n            signals.append('hold')\n\n    return ['hold'] + signals"}, "5": {"name": "均值回归策略", "code": "def strategy(data):\n    # 计算价格偏离度\n    period = 20\n    mean_price = data['close'].rolling(period).mean()\n    std_price = data['close'].rolling(period).std()\n\n    # 计算Z-Score\n    z_score = (data['close'] - mean_price) / std_price\n\n    signals = []\n    for i in range(len(data)):\n        if z_score.iloc[i] < -2:  # 价格严重低于均值\n            signals.append('buy')\n        elif z_score.iloc[i] > 2:  # 价格严重高于均值\n            signals.append('sell')\n        elif abs(z_score.iloc[i]) < 0.5:  # 价格接近均值，平仓\n            signals.append('close')\n        else:\n            signals.append('hold')\n\n    return signals"}, "6": {"name": "趋势跟踪策略", "code": "def strategy(data):\n    # 使用ATR和移动平均确定趋势\n    def calculate_atr(data, period=14):\n        high_low = data['high'] - data['low']\n        high_close = abs(data['high'] - data['close'].shift())\n        low_close = abs(data['low'] - data['close'].shift())\n        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)\n        return true_range.rolling(period).mean()\n\n    atr = calculate_atr(data)\n    sma_50 = data['close'].rolling(50).mean()\n\n    signals = []\n    for i in range(len(data)):\n        # 趋势确认：价格在均线上方且ATR增加\n        if (data['close'].iloc[i] > sma_50.iloc[i] and\n            i > 0 and atr.iloc[i] > atr.iloc[i-1]):\n            signals.append('buy')\n        elif (data['close'].iloc[i] < sma_50.iloc[i] and\n              i > 0 and atr.iloc[i] > atr.iloc[i-1]):\n            signals.append('sell')\n        else:\n            signals.append('hold')\n\n    return signals"}, "7": {"name": "配对交易策略", "code": "def strategy(data_a, data_b):\n    # 配对交易：寻找两个相关资产的价差机会\n    import numpy as np\n\n    # 计算价格比率\n    ratio = data_a['close'] / data_b['close']\n    ratio_mean = ratio.rolling(30).mean()\n    ratio_std = ratio.rolling(30).std()\n\n    # 计算Z-Score\n    z_score = (ratio - ratio_mean) / ratio_std\n\n    signals = []\n    for i in range(len(data_a)):\n        if z_score.iloc[i] > 2:  # 比率过高，做空A做多B\n            signals.append('short_a_long_b')\n        elif z_score.iloc[i] < -2:  # 比率过低，做多A做空B\n            signals.append('long_a_short_b')\n        elif abs(z_score.iloc[i]) < 0.5:  # 比率回归，平仓\n            signals.append('close_all')\n        else:\n            signals.append('hold')\n\n    return signals"}, "8": {"name": "波动率突破策略", "code": "def strategy(data):\n    # 基于波动率的突破策略\n    period = 20\n\n    # 计算历史波动率\n    returns = data['close'].pct_change()\n    volatility = returns.rolling(period).std() * (252 ** 0.5)  # 年化波动率\n\n    # 计算价格通道\n    high_channel = data['high'].rolling(period).max()\n    low_channel = data['low'].rolling(period).min()\n\n    signals = []\n    for i in range(len(data)):\n        # 高波动率环境下的突破信号\n        if (volatility.iloc[i] > volatility.rolling(50).mean().iloc[i] and\n            data['close'].iloc[i] > high_channel.iloc[i-1] if i > 0 else False):\n            signals.append('buy')\n        elif (volatility.iloc[i] > volatility.rolling(50).mean().iloc[i] and\n              data['close'].iloc[i] < low_channel.iloc[i-1] if i > 0 else False):\n            signals.append('sell')\n        else:\n            signals.append('hold')\n\n    return signals"}}