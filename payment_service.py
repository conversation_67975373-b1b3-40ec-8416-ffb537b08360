"""
支付服务模块
支持多种支付方式：支付宝、微信支付、PayPal等
"""

import os
import json
import time
import hashlib
import hmac
import base64
import requests
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class PaymentConfig:
    """支付配置类"""
    
    # 支付宝配置
    ALIPAY_APP_ID = os.getenv('ALIPAY_APP_ID', 'YOUR_ALIPAY_APP_ID')
    ALIPAY_PRIVATE_KEY = os.getenv('ALIPAY_PRIVATE_KEY', 'YOUR_ALIPAY_PRIVATE_KEY')
    ALIPAY_PUBLIC_KEY = os.getenv('ALIPAY_PUBLIC_KEY', 'YOUR_ALIPAY_PUBLIC_KEY')
    ALIPAY_GATEWAY = 'https://openapi.alipay.com/gateway.do'
    
    # 微信支付配置
    WECHAT_APP_ID = os.getenv('WECHAT_APP_ID', 'YOUR_WECHAT_APP_ID')
    WECHAT_MCH_ID = os.getenv('WECHAT_MCH_ID', 'YOUR_WECHAT_MCH_ID')
    WECHAT_API_KEY = os.getenv('WECHAT_API_KEY', 'YOUR_WECHAT_API_KEY')
    WECHAT_GATEWAY = 'https://api.mch.weixin.qq.com'
    
    # PayPal配置
    PAYPAL_CLIENT_ID = os.getenv('PAYPAL_CLIENT_ID', 'YOUR_PAYPAL_CLIENT_ID')
    PAYPAL_CLIENT_SECRET = os.getenv('PAYPAL_CLIENT_SECRET', 'YOUR_PAYPAL_CLIENT_SECRET')
    PAYPAL_SANDBOX = os.getenv('PAYPAL_SANDBOX', 'true').lower() == 'true'
    PAYPAL_BASE_URL = 'https://api.sandbox.paypal.com' if PAYPAL_SANDBOX else 'https://api.paypal.com'
    
    # Stripe配置
    STRIPE_PUBLIC_KEY = os.getenv('STRIPE_PUBLIC_KEY', 'YOUR_STRIPE_PUBLIC_KEY')
    STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY', 'YOUR_STRIPE_SECRET_KEY')

class PaymentOrder:
    """支付订单类"""
    
    def __init__(self, order_id, user_id, amount, currency='CNY', description='', payment_method='alipay'):
        self.order_id = order_id
        self.user_id = user_id
        self.amount = amount
        self.currency = currency
        self.description = description
        self.payment_method = payment_method
        self.status = 'pending'  # pending, paid, failed, cancelled
        self.created_at = datetime.now()
        self.paid_at = None
        self.transaction_id = None
        self.callback_url = None
        self.return_url = None

class PaymentService:
    """支付服务类"""
    
    def __init__(self):
        self.orders = {}  # 简单的内存存储，实际应用中应使用数据库
        self.supported_methods = {
            'alipay': '支付宝',
            'wechat': '微信支付',
            'paypal': 'PayPal',
            'stripe': 'Stripe',
            'mock': '模拟支付'  # 用于测试
        }
    
    def create_order(self, user_id, plan_type, payment_method='mock'):
        """创建支付订单"""
        try:
            # 根据计划类型确定金额
            plan_config = self._get_plan_config(plan_type)
            if not plan_config:
                return {'success': False, 'error': '无效的会员计划'}
            
            # 生成订单ID
            order_id = self._generate_order_id()
            
            # 创建订单
            order = PaymentOrder(
                order_id=order_id,
                user_id=user_id,
                amount=plan_config['price'],
                currency=plan_config['currency'],
                description=f"QuantTradeX {plan_config['name']} 会员",
                payment_method=payment_method
            )
            
            # 保存订单
            self.orders[order_id] = order
            
            # 根据支付方式创建支付链接
            payment_url = self._create_payment_url(order)
            
            return {
                'success': True,
                'order_id': order_id,
                'amount': order.amount,
                'currency': order.currency,
                'description': order.description,
                'payment_url': payment_url,
                'payment_method': payment_method,
                'expires_at': (order.created_at + timedelta(minutes=30)).isoformat()
            }
            
        except Exception as e:
            logger.error(f"创建支付订单失败: {e}")
            return {'success': False, 'error': '创建订单失败'}
    
    def _get_plan_config(self, plan_type):
        """获取会员计划配置"""
        plans = {
            'monthly': {
                'name': '月度会员',
                'price': 99.99,
                'currency': 'CNY',
                'duration_days': 30
            },
            'yearly': {
                'name': '年度会员',
                'price': 999.99,
                'currency': 'CNY',
                'duration_days': 365
            },
            'lifetime': {
                'name': '终身会员',
                'price': 2999.99,
                'currency': 'CNY',
                'duration_days': 36500  # 100年
            }
        }
        return plans.get(plan_type)
    
    def _generate_order_id(self):
        """生成订单ID"""
        timestamp = str(int(time.time()))
        random_str = hashlib.md5(f"{timestamp}{time.time()}".encode()).hexdigest()[:8]
        return f"QTX{timestamp}{random_str}".upper()
    
    def _create_payment_url(self, order):
        """创建支付链接"""
        if order.payment_method == 'mock':
            return f"/payment/mock/{order.order_id}"
        elif order.payment_method == 'alipay':
            return self._create_alipay_url(order)
        elif order.payment_method == 'wechat':
            return self._create_wechat_url(order)
        elif order.payment_method == 'paypal':
            return self._create_paypal_url(order)
        elif order.payment_method == 'stripe':
            return self._create_stripe_url(order)
        else:
            return f"/payment/unsupported/{order.order_id}"
    
    def _create_alipay_url(self, order):
        """创建支付宝支付链接"""
        # 这里应该集成真实的支付宝SDK
        # 为了演示，返回模拟链接
        return f"/payment/alipay/{order.order_id}"
    
    def _create_wechat_url(self, order):
        """创建微信支付链接"""
        # 这里应该集成真实的微信支付SDK
        # 为了演示，返回模拟链接
        return f"/payment/wechat/{order.order_id}"
    
    def _create_paypal_url(self, order):
        """创建PayPal支付链接"""
        try:
            # 获取PayPal访问令牌
            access_token = self._get_paypal_access_token()
            if not access_token:
                return f"/payment/error/{order.order_id}"
            
            # 创建PayPal订单
            paypal_order = {
                "intent": "CAPTURE",
                "purchase_units": [{
                    "amount": {
                        "currency_code": "USD",  # PayPal通常使用USD
                        "value": str(round(order.amount / 7, 2))  # 简单汇率转换
                    },
                    "description": order.description
                }],
                "application_context": {
                    "return_url": f"http://localhost:5000/payment/paypal/return/{order.order_id}",
                    "cancel_url": f"http://localhost:5000/payment/paypal/cancel/{order.order_id}"
                }
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {access_token}'
            }
            
            response = requests.post(
                f"{PaymentConfig.PAYPAL_BASE_URL}/v2/checkout/orders",
                json=paypal_order,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 201:
                paypal_response = response.json()
                # 查找approval链接
                for link in paypal_response.get('links', []):
                    if link.get('rel') == 'approve':
                        return link.get('href')
            
            logger.error(f"PayPal订单创建失败: {response.text}")
            return f"/payment/error/{order.order_id}"
            
        except Exception as e:
            logger.error(f"创建PayPal支付链接失败: {e}")
            return f"/payment/error/{order.order_id}"
    
    def _create_stripe_url(self, order):
        """创建Stripe支付链接"""
        # 这里应该集成真实的Stripe SDK
        # 为了演示，返回模拟链接
        return f"/payment/stripe/{order.order_id}"
    
    def _get_paypal_access_token(self):
        """获取PayPal访问令牌"""
        try:
            if PaymentConfig.PAYPAL_CLIENT_ID == 'YOUR_PAYPAL_CLIENT_ID':
                return None  # 未配置
            
            auth = base64.b64encode(
                f"{PaymentConfig.PAYPAL_CLIENT_ID}:{PaymentConfig.PAYPAL_CLIENT_SECRET}".encode()
            ).decode()
            
            headers = {
                'Accept': 'application/json',
                'Accept-Language': 'en_US',
                'Authorization': f'Basic {auth}'
            }
            
            data = 'grant_type=client_credentials'
            
            response = requests.post(
                f"{PaymentConfig.PAYPAL_BASE_URL}/v1/oauth2/token",
                headers=headers,
                data=data,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json().get('access_token')
            
            logger.error(f"获取PayPal访问令牌失败: {response.text}")
            return None
            
        except Exception as e:
            logger.error(f"获取PayPal访问令牌异常: {e}")
            return None
    
    def get_order(self, order_id):
        """获取订单信息"""
        return self.orders.get(order_id)
    
    def update_order_status(self, order_id, status, transaction_id=None):
        """更新订单状态"""
        order = self.orders.get(order_id)
        if order:
            order.status = status
            if status == 'paid':
                order.paid_at = datetime.now()
            if transaction_id:
                order.transaction_id = transaction_id
            return True
        return False
    
    def process_mock_payment(self, order_id):
        """处理模拟支付"""
        order = self.get_order(order_id)
        if not order:
            return {'success': False, 'error': '订单不存在'}
        
        if order.status != 'pending':
            return {'success': False, 'error': '订单状态无效'}
        
        # 模拟支付成功
        self.update_order_status(order_id, 'paid', f'MOCK_{int(time.time())}')
        
        return {
            'success': True,
            'order_id': order_id,
            'transaction_id': order.transaction_id,
            'amount': order.amount,
            'currency': order.currency,
            'paid_at': order.paid_at.isoformat()
        }
    
    def get_supported_methods(self):
        """获取支持的支付方式"""
        methods = []
        for method_id, method_name in self.supported_methods.items():
            status = 'active'
            
            # 检查配置状态
            if method_id == 'alipay' and PaymentConfig.ALIPAY_APP_ID == 'YOUR_ALIPAY_APP_ID':
                status = 'inactive'
            elif method_id == 'wechat' and PaymentConfig.WECHAT_APP_ID == 'YOUR_WECHAT_APP_ID':
                status = 'inactive'
            elif method_id == 'paypal' and PaymentConfig.PAYPAL_CLIENT_ID == 'YOUR_PAYPAL_CLIENT_ID':
                status = 'inactive'
            elif method_id == 'stripe' and PaymentConfig.STRIPE_SECRET_KEY == 'YOUR_STRIPE_SECRET_KEY':
                status = 'inactive'
            
            methods.append({
                'id': method_id,
                'name': method_name,
                'status': status
            })
        
        return methods

# 全局支付服务实例
payment_service = PaymentService()
