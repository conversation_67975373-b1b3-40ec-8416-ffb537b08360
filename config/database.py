#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接和配置管理
"""

import logging
from .settings import get_config

# 可选导入，如果模块不存在则设为None
try:
    import redis
except ImportError:
    redis = None

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
except ImportError:
    psycopg2 = None
    RealDictCursor = None

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.config = get_config()
        self.redis_client = None
        self.db_connection = None
        self.init_connections()

    def init_connections(self):
        """初始化数据库连接"""
        self.init_redis()
        self.init_postgresql()

    def init_redis(self):
        """初始化Redis连接"""
        if redis is None:
            logger.warning("Redis模块未安装，跳过Redis连接")
            self.redis_client = None
            return

        try:
            self.redis_client = redis.Redis(**self.config.REDIS_CONFIG)
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.redis_client = None

    def init_postgresql(self):
        """初始化PostgreSQL连接"""
        if psycopg2 is None:
            logger.warning("psycopg2模块未安装，跳过PostgreSQL连接")
            self.db_connection = None
            return

        try:
            self.db_connection = psycopg2.connect(**self.config.DB_CONFIG)
            logger.info("PostgreSQL连接成功")
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {e}")
            self.db_connection = None

    def get_redis_client(self):
        """获取Redis客户端"""
        if self.redis_client is None:
            self.init_redis()
        return self.redis_client

    def get_db_connection(self):
        """获取数据库连接"""
        if self.db_connection is None:
            self.init_postgresql()
        return self.db_connection

    def execute_query(self, query, params=None, fetch=True):
        """执行数据库查询"""
        try:
            conn = self.get_db_connection()
            if conn is None:
                return None

            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)

                if fetch:
                    if query.strip().upper().startswith('SELECT'):
                        return cursor.fetchall()
                    else:
                        conn.commit()
                        return cursor.rowcount
                else:
                    conn.commit()
                    return True

        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            if self.db_connection:
                self.db_connection.rollback()
            return None

    def close_connections(self):
        """关闭所有连接"""
        if self.redis_client:
            self.redis_client.close()
        if self.db_connection:
            self.db_connection.close()

# 全局数据库管理器实例
db_manager = DatabaseManager()
