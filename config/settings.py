#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QuantTradeX 配置设置
包含应用程序的所有配置参数
"""

import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask应用配置
class Config:
    """基础配置类"""
    SECRET_KEY = 'quanttradex_secret_key_2025_advanced'

    # 会话配置
    PERMANENT_SESSION_LIFETIME = 86400  # 24小时（秒）
    SESSION_COOKIE_SECURE = False  # 开发环境设为False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

    # Redis配置
    REDIS_CONFIG = {
        'host': 'localhost',
        'port': 6379,
        'db': 0
    }

    # 数据库配置
    DB_CONFIG = {
        'host': 'localhost',
        'port': 5432,
        'database': 'quanttradex',
        'user': 'quanttradex_user',
        'password': 'quanttradex123'
    }

    # SocketIO配置
    SOCKETIO_CONFIG = {
        'cors_allowed_origins': "*",
        'async_mode': 'threading'
    }

    # 回测引擎配置
    BACKTEST_CONFIG = {
        'commission': 0.001,  # 手续费率
        'slippage': 0.0005,   # 滑点
        'min_trade_amount': 100  # 最小交易金额
    }

    # 实时数据服务配置
    REALTIME_CONFIG = {
        'cache_timeout': 30,  # 缓存超时时间（秒）
        'update_interval': 5,  # 数据更新间隔（秒）
        'max_workers': 10     # 最大工作线程数
    }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True

# 根据环境变量选择配置
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前配置"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config.get(env, config['default'])
