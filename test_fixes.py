#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复效果的脚本
"""

import requests
import time

def test_fixes():
    base_url = "http://127.0.0.1:5000"
    
    print("🔧 测试修复效果")
    print("=" * 40)
    
    tests = [
        ("仪表板页面", lambda: requests.get(f"{base_url}/dashboard", timeout=5).status_code == 200),
        ("策略列表API", lambda: requests.get(f"{base_url}/api/strategies", timeout=5).json().get('success')),
        ("论坛热门话题API", lambda: requests.get(f"{base_url}/api/forum/hot-topics", timeout=5).json().get('success')),
        ("论坛统计API", lambda: requests.get(f"{base_url}/api/forum/stats", timeout=5).json().get('success')),
        ("回测列表API", lambda: requests.get(f"{base_url}/api/backtests", timeout=5).json().get('success')),
        ("系统状态API", lambda: requests.get(f"{base_url}/api/system/status", timeout=5).json().get('database')),
        ("股票数据API", lambda: requests.get(f"{base_url}/api/stock/AAPL", timeout=10).json().get('success')),
        ("关注列表API", lambda: requests.get(f"{base_url}/api/watchlist", timeout=5).json().get('success')),
        ("加密货币API", lambda: requests.get(f"{base_url}/api/realtime/crypto/BTC", timeout=5).json().get('success')),
        ("CSS文件", lambda: requests.get(f"{base_url}/static/css/style.css", timeout=5).status_code == 200),
        ("JS文件", lambda: requests.get(f"{base_url}/static/js/main.js", timeout=5).status_code == 200),
    ]
    
    passed = 0
    failed = 0
    
    for name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {name}")
                passed += 1
            else:
                print(f"❌ {name}")
                failed += 1
        except Exception as e:
            print(f"❌ {name} - 异常: {e}")
            failed += 1
    
    total = passed + failed
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print("=" * 40)
    print(f"总测试: {total}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 修复成功！网站功能正常")
    elif success_rate >= 80:
        print("\n👍 大部分问题已修复")
    else:
        print("\n⚠️ 仍有问题需要解决")

if __name__ == "__main__":
    test_fixes()
