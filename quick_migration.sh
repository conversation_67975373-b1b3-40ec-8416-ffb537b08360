#!/bin/bash

# QuantTradeX 快速移植脚本
# 用途: 自动化执行项目移植到新服务器
# 目标: www.gdpp.com (Ubuntu 22.04 + 宝塔面板)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查Ubuntu版本
    if ! grep -q "Ubuntu 22.04" /etc/os-release; then
        log_warning "建议使用Ubuntu 22.04系统"
    fi
    
    # 检查宝塔面板
    if [ ! -f "/www/server/panel/BT-Panel" ]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    
    log_success "系统环境检查完成"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    apt update
    apt install -y python3-pip python3-venv python3-dev build-essential
    apt install -y libpq-dev git curl wget unzip
    apt install -y postgresql postgresql-contrib redis-server
    
    # 安装Node.js和PM2
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt install -y nodejs
    npm install -g pm2
    
    log_success "系统依赖安装完成"
}

# 配置数据库
setup_database() {
    log_info "配置PostgreSQL数据库..."
    
    # 启动PostgreSQL
    systemctl start postgresql
    systemctl enable postgresql
    
    # 创建数据库和用户
    sudo -u postgres psql << EOF
CREATE DATABASE quanttradex;
CREATE USER quanttradex_user WITH PASSWORD 'QuantTradeX2025!';
GRANT ALL PRIVILEGES ON DATABASE quanttradex TO quanttradex_user;
ALTER USER quanttradex_user CREATEDB;
\q
EOF
    
    log_success "PostgreSQL配置完成"
}

# 配置Redis
setup_redis() {
    log_info "配置Redis..."
    
    systemctl start redis-server
    systemctl enable redis-server
    
    # 基础Redis配置
    cat >> /etc/redis/redis.conf << EOF
maxmemory 512mb
maxmemory-policy allkeys-lru
EOF
    
    systemctl restart redis-server
    log_success "Redis配置完成"
}

# 创建项目目录
create_project_directory() {
    log_info "创建项目目录..."
    
    PROJECT_DIR="/www/wwwroot/www.gdpp.com"
    mkdir -p $PROJECT_DIR
    cd $PROJECT_DIR
    
    # 设置权限
    chown -R www:www $PROJECT_DIR
    chmod -R 755 $PROJECT_DIR
    
    log_success "项目目录创建完成: $PROJECT_DIR"
}

# 下载项目文件
download_project() {
    log_info "准备项目文件..."
    
    # 这里需要您手动上传项目文件
    log_warning "请手动执行以下步骤:"
    echo "1. 在源服务器创建项目备份:"
    echo "   cd /www/wwwroot/qclb.com"
    echo "   tar -czf quanttradex_backup.tar.gz --exclude='venv' --exclude='__pycache__' ."
    echo ""
    echo "2. 将备份文件上传到新服务器:"
    echo "   scp quanttradex_backup.tar.gz root@new-server:/www/wwwroot/www.gdpp.com/"
    echo ""
    echo "3. 解压项目文件:"
    echo "   cd /www/wwwroot/www.gdpp.com"
    echo "   tar -xzf quanttradex_backup.tar.gz"
    echo ""
    
    read -p "项目文件是否已经上传并解压? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "请先完成项目文件上传"
        exit 1
    fi
}

# 设置Python环境
setup_python_environment() {
    log_info "设置Python虚拟环境..."
    
    cd /www/wwwroot/www.gdpp.com
    
    # 创建虚拟环境
    python3 -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装基础依赖
    pip install flask flask-socketio
    pip install psycopg2-binary redis
    pip install pandas numpy yfinance
    pip install requests pyotp qrcode[pil] cryptography
    pip install gunicorn eventlet
    pip install psutil
    
    log_success "Python环境配置完成"
}

# 更新配置文件
update_configuration() {
    log_info "更新配置文件..."
    
    cd /www/wwwroot/www.gdpp.com
    
    # 更新数据库配置
    if [ -f "app.py" ]; then
        sed -i "s/'password': 'quanttradex123'/'password': 'QuantTradeX2025!'/g" app.py
        sed -i "s/qclb\.com/gdpp.com/g" app.py
    fi
    
    # 更新模板文件中的域名
    find templates -name "*.html" -exec sed -i 's/qclb\.com/gdpp.com/g' {} \; 2>/dev/null || true
    find static -name "*.js" -exec sed -i 's/qclb\.com/gdpp.com/g' {} \; 2>/dev/null || true
    
    log_success "配置文件更新完成"
}

# 初始化数据库
initialize_database() {
    log_info "初始化数据库..."
    
    cd /www/wwwroot/www.gdpp.com
    source venv/bin/activate
    
    # 运行数据库初始化脚本
    if [ -f "database_init.py" ]; then
        python database_init.py
    else
        log_warning "未找到database_init.py，请手动初始化数据库"
    fi
    
    log_success "数据库初始化完成"
}

# 配置Nginx
setup_nginx() {
    log_info "配置Nginx..."
    
    # 创建Nginx配置文件
    cat > /www/server/panel/vhost/nginx/www.gdpp.com.conf << 'EOF'
server {
    listen 80;
    server_name www.gdpp.com gdpp.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.gdpp.com gdpp.com;
    
    # SSL配置将由宝塔面板自动添加
    
    # 静态文件
    location /static/ {
        alias /www/wwwroot/www.gdpp.com/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 反向代理
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
}
EOF
    
    # 重载Nginx配置
    nginx -t && nginx -s reload
    
    log_success "Nginx配置完成"
}

# 配置进程管理
setup_process_management() {
    log_info "配置进程管理..."
    
    cd /www/wwwroot/www.gdpp.com
    
    # 创建PM2配置文件
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'quanttradex',
    script: '/www/wwwroot/www.gdpp.com/venv/bin/python',
    args: 'app.py',
    cwd: '/www/wwwroot/www.gdpp.com',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
EOF
    
    # 启动应用
    pm2 start ecosystem.config.js
    pm2 startup
    pm2 save
    
    log_success "进程管理配置完成"
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    ufw --force enable
    ufw allow 22/tcp    # SSH
    ufw allow 80/tcp    # HTTP
    ufw allow 443/tcp   # HTTPS
    ufw allow 8888/tcp  # 宝塔面板
    
    log_success "防火墙配置完成"
}

# 创建监控脚本
create_monitoring() {
    log_info "创建监控脚本..."
    
    mkdir -p /www/wwwroot/www.gdpp.com/logs
    
    # 创建验证脚本
    cat > /www/wwwroot/www.gdpp.com/verify_migration.sh << 'EOF'
#!/bin/bash
echo "🔍 QuantTradeX移植验证开始..."

# 检查基础服务
systemctl is-active nginx && echo "✅ Nginx运行正常" || echo "❌ Nginx未运行"
systemctl is-active postgresql && echo "✅ PostgreSQL运行正常" || echo "❌ PostgreSQL未运行"
systemctl is-active redis && echo "✅ Redis运行正常" || echo "❌ Redis未运行"

# 检查网站响应
curl -s -o /dev/null -w "%{http_code}" https://www.gdpp.com | grep 200 && echo "✅ 网站响应正常" || echo "❌ 网站响应异常"

echo "🎉 移植验证完成！"
EOF
    
    chmod +x /www/wwwroot/www.gdpp.com/verify_migration.sh
    
    log_success "监控脚本创建完成"
}

# 最终验证
final_verification() {
    log_info "执行最终验证..."
    
    cd /www/wwwroot/www.gdpp.com
    
    # 检查应用是否运行
    if pm2 list | grep -q "quanttradex"; then
        log_success "应用进程运行正常"
    else
        log_error "应用进程未运行"
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep -q ":5000"; then
        log_success "端口5000监听正常"
    else
        log_error "端口5000未监听"
    fi
    
    # 运行验证脚本
    ./verify_migration.sh
}

# 主函数
main() {
    echo "🚀 QuantTradeX项目移植脚本启动"
    echo "目标服务器: www.gdpp.com"
    echo "系统要求: Ubuntu 22.04 + 宝塔面板"
    echo ""
    
    read -p "确认开始移植? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "移植已取消"
        exit 0
    fi
    
    check_root
    check_environment
    install_dependencies
    setup_database
    setup_redis
    create_project_directory
    download_project
    setup_python_environment
    update_configuration
    initialize_database
    setup_nginx
    setup_process_management
    setup_firewall
    create_monitoring
    final_verification
    
    echo ""
    log_success "🎉 QuantTradeX项目移植完成！"
    echo ""
    echo "访问地址: https://www.gdpp.com"
    echo "管理命令:"
    echo "  pm2 status          # 查看应用状态"
    echo "  pm2 restart quanttradex  # 重启应用"
    echo "  pm2 logs quanttradex     # 查看日志"
    echo ""
    echo "下一步:"
    echo "1. 在宝塔面板申请SSL证书"
    echo "2. 配置域名DNS解析"
    echo "3. 测试所有功能"
    echo "4. 导入用户数据"
}

# 执行主函数
main "$@"
