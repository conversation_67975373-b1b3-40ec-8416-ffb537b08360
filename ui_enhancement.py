#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户界面优化模块
提升用户体验和界面响应性
"""

from flask import Flask, render_template, request, jsonify, session
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class UIEnhancementService:
    """用户界面增强服务"""
    
    def __init__(self):
        self.user_preferences = {}
        self.ui_themes = {
            'light': {
                'name': '浅色主题',
                'primary_color': '#007bff',
                'background_color': '#ffffff',
                'text_color': '#333333',
                'card_background': '#f8f9fa',
                'border_color': '#dee2e6'
            },
            'dark': {
                'name': '深色主题',
                'primary_color': '#0d6efd',
                'background_color': '#1a1a1a',
                'text_color': '#ffffff',
                'card_background': '#2d2d2d',
                'border_color': '#404040'
            },
            'blue': {
                'name': '蓝色主题',
                'primary_color': '#0066cc',
                'background_color': '#f0f8ff',
                'text_color': '#1a1a1a',
                'card_background': '#e6f3ff',
                'border_color': '#b3d9ff'
            },
            'green': {
                'name': '绿色主题',
                'primary_color': '#28a745',
                'background_color': '#f8fff8',
                'text_color': '#1a1a1a',
                'card_background': '#e6ffe6',
                'border_color': '#b3ffb3'
            }
        }
        self.layout_options = {
            'compact': '紧凑布局',
            'comfortable': '舒适布局',
            'spacious': '宽松布局'
        }
        self.notification_settings = {
            'price_alerts': True,
            'system_notifications': True,
            'trade_confirmations': True,
            'market_updates': False
        }
    
    def get_user_preferences(self, user_id: str) -> Dict:
        """获取用户偏好设置"""
        return self.user_preferences.get(user_id, {
            'theme': 'light',
            'layout': 'comfortable',
            'language': 'zh-CN',
            'timezone': 'Asia/Shanghai',
            'notifications': self.notification_settings.copy(),
            'dashboard_widgets': [
                'market_overview',
                'watchlist',
                'portfolio',
                'recent_trades'
            ],
            'chart_preferences': {
                'default_interval': '1d',
                'indicators': ['SMA', 'RSI'],
                'chart_type': 'candlestick'
            }
        })
    
    def update_user_preferences(self, user_id: str, preferences: Dict) -> bool:
        """更新用户偏好设置"""
        try:
            if user_id not in self.user_preferences:
                self.user_preferences[user_id] = self.get_user_preferences(user_id)
            
            self.user_preferences[user_id].update(preferences)
            return True
        except Exception as e:
            logger.error(f"更新用户偏好失败: {e}")
            return False
    
    def get_theme_css(self, theme_name: str) -> str:
        """生成主题CSS"""
        theme = self.ui_themes.get(theme_name, self.ui_themes['light'])
        
        css = f"""
        :root {{
            --primary-color: {theme['primary_color']};
            --background-color: {theme['background_color']};
            --text-color: {theme['text_color']};
            --card-background: {theme['card_background']};
            --border-color: {theme['border_color']};
        }}
        
        body {{
            background-color: var(--background-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }}
        
        .card {{
            background-color: var(--card-background);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }}
        
        .btn-primary {{
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }}
        
        .navbar {{
            background-color: var(--card-background) !important;
            border-bottom: 1px solid var(--border-color);
        }}
        
        .table {{
            color: var(--text-color);
        }}
        
        .table th {{
            border-color: var(--border-color);
        }}
        
        .table td {{
            border-color: var(--border-color);
        }}
        """
        
        return css
    
    def get_responsive_layout_css(self, layout_type: str) -> str:
        """生成响应式布局CSS"""
        layouts = {
            'compact': {
                'card_padding': '0.5rem',
                'margin': '0.25rem',
                'font_size': '0.875rem'
            },
            'comfortable': {
                'card_padding': '1rem',
                'margin': '0.5rem',
                'font_size': '1rem'
            },
            'spacious': {
                'card_padding': '1.5rem',
                'margin': '1rem',
                'font_size': '1.125rem'
            }
        }
        
        layout = layouts.get(layout_type, layouts['comfortable'])
        
        css = f"""
        .card-body {{
            padding: {layout['card_padding']};
        }}
        
        .container, .container-fluid {{
            margin-bottom: {layout['margin']};
        }}
        
        body {{
            font-size: {layout['font_size']};
        }}
        
        @media (max-width: 768px) {{
            .card-body {{
                padding: calc({layout['card_padding']} * 0.75);
            }}
            
            .container {{
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }}
        }}
        """
        
        return css

class NotificationService:
    """通知服务"""
    
    def __init__(self):
        self.notifications = {}
        self.notification_types = {
            'info': {'icon': 'fa-info-circle', 'class': 'alert-info'},
            'success': {'icon': 'fa-check-circle', 'class': 'alert-success'},
            'warning': {'icon': 'fa-exclamation-triangle', 'class': 'alert-warning'},
            'error': {'icon': 'fa-times-circle', 'class': 'alert-danger'}
        }
    
    def add_notification(self, user_id: str, message: str, type: str = 'info', 
                        title: str = None, persistent: bool = False) -> str:
        """添加通知"""
        notification_id = f"notif_{int(datetime.now().timestamp() * 1000)}"
        
        if user_id not in self.notifications:
            self.notifications[user_id] = []
        
        notification = {
            'id': notification_id,
            'title': title,
            'message': message,
            'type': type,
            'icon': self.notification_types[type]['icon'],
            'class': self.notification_types[type]['class'],
            'timestamp': datetime.now().isoformat(),
            'read': False,
            'persistent': persistent
        }
        
        self.notifications[user_id].append(notification)
        
        # 限制通知数量
        if len(self.notifications[user_id]) > 50:
            self.notifications[user_id] = self.notifications[user_id][-50:]
        
        return notification_id
    
    def get_notifications(self, user_id: str, unread_only: bool = False) -> List[Dict]:
        """获取用户通知"""
        user_notifications = self.notifications.get(user_id, [])
        
        if unread_only:
            return [n for n in user_notifications if not n['read']]
        
        return user_notifications
    
    def mark_as_read(self, user_id: str, notification_id: str = None) -> bool:
        """标记通知为已读"""
        if user_id not in self.notifications:
            return False
        
        if notification_id:
            # 标记特定通知
            for notification in self.notifications[user_id]:
                if notification['id'] == notification_id:
                    notification['read'] = True
                    return True
        else:
            # 标记所有通知
            for notification in self.notifications[user_id]:
                notification['read'] = True
            return True
        
        return False

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.cache_settings = {
            'static_cache_duration': 3600,  # 1小时
            'api_cache_duration': 300,      # 5分钟
            'page_cache_duration': 60       # 1分钟
        }
        self.compression_settings = {
            'enable_gzip': True,
            'min_size': 1024,
            'compression_level': 6
        }
    
    def get_cache_headers(self, resource_type: str) -> Dict[str, str]:
        """获取缓存头"""
        duration = self.cache_settings.get(f'{resource_type}_cache_duration', 300)
        
        return {
            'Cache-Control': f'public, max-age={duration}',
            'Expires': datetime.fromtimestamp(
                datetime.now().timestamp() + duration
            ).strftime('%a, %d %b %Y %H:%M:%S GMT')
        }
    
    def optimize_json_response(self, data: Any) -> str:
        """优化JSON响应"""
        return json.dumps(data, separators=(',', ':'), ensure_ascii=False)
    
    def get_lazy_loading_config(self) -> Dict:
        """获取懒加载配置"""
        return {
            'image_lazy_loading': True,
            'table_pagination': 50,
            'infinite_scroll': True,
            'defer_non_critical_js': True
        }

class AccessibilityEnhancer:
    """无障碍访问增强器"""
    
    def __init__(self):
        self.accessibility_features = {
            'high_contrast': False,
            'large_text': False,
            'keyboard_navigation': True,
            'screen_reader_support': True,
            'focus_indicators': True
        }
    
    def get_accessibility_css(self, features: Dict) -> str:
        """生成无障碍CSS"""
        css = ""
        
        if features.get('high_contrast'):
            css += """
            .high-contrast {
                filter: contrast(150%);
            }
            """
        
        if features.get('large_text'):
            css += """
            .large-text {
                font-size: 1.25em !important;
            }
            """
        
        if features.get('focus_indicators'):
            css += """
            *:focus {
                outline: 3px solid #007bff !important;
                outline-offset: 2px !important;
            }
            """
        
        return css
    
    def get_aria_attributes(self, element_type: str) -> Dict[str, str]:
        """获取ARIA属性"""
        aria_configs = {
            'button': {'role': 'button', 'aria-pressed': 'false'},
            'link': {'role': 'link'},
            'table': {'role': 'table'},
            'alert': {'role': 'alert', 'aria-live': 'polite'},
            'navigation': {'role': 'navigation', 'aria-label': '主导航'}
        }
        
        return aria_configs.get(element_type, {})

# 全局实例
ui_enhancement_service = UIEnhancementService()
notification_service = NotificationService()
performance_optimizer = PerformanceOptimizer()
accessibility_enhancer = AccessibilityEnhancer()

def register_ui_routes(app: Flask):
    """注册UI增强路由"""
    
    @app.route('/api/ui/preferences', methods=['GET', 'POST'])
    def handle_ui_preferences():
        """处理用户界面偏好"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'error': '未登录'})
        
        user_id = session['user_id']
        
        if request.method == 'GET':
            preferences = ui_enhancement_service.get_user_preferences(user_id)
            return jsonify({'success': True, 'preferences': preferences})
        
        elif request.method == 'POST':
            try:
                data = request.get_json()
                success = ui_enhancement_service.update_user_preferences(user_id, data)
                
                if success:
                    notification_service.add_notification(
                        user_id, '界面设置已保存', 'success', '设置更新'
                    )
                    return jsonify({'success': True, 'message': '设置已保存'})
                else:
                    return jsonify({'success': False, 'error': '保存失败'})
            
            except Exception as e:
                logger.error(f"保存UI偏好失败: {e}")
                return jsonify({'success': False, 'error': '保存失败'})
    
    @app.route('/api/ui/theme/<theme_name>')
    def get_theme_css(theme_name):
        """获取主题CSS"""
        css = ui_enhancement_service.get_theme_css(theme_name)
        response = app.response_class(css, mimetype='text/css')
        
        # 添加缓存头
        cache_headers = performance_optimizer.get_cache_headers('static')
        for key, value in cache_headers.items():
            response.headers[key] = value
        
        return response
    
    @app.route('/api/notifications')
    def get_notifications():
        """获取用户通知"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'error': '未登录'})
        
        user_id = session['user_id']
        unread_only = request.args.get('unread_only', 'false').lower() == 'true'
        
        notifications = notification_service.get_notifications(user_id, unread_only)
        return jsonify({
            'success': True,
            'notifications': notifications,
            'unread_count': len([n for n in notifications if not n['read']])
        })
    
    @app.route('/api/notifications/<notification_id>/read', methods=['POST'])
    def mark_notification_read(notification_id):
        """标记通知为已读"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'error': '未登录'})
        
        user_id = session['user_id']
        success = notification_service.mark_as_read(user_id, notification_id)
        
        return jsonify({'success': success})
    
    @app.route('/api/ui/performance-config')
    def get_performance_config():
        """获取性能优化配置"""
        config = {
            'lazy_loading': performance_optimizer.get_lazy_loading_config(),
            'cache_settings': performance_optimizer.cache_settings,
            'compression': performance_optimizer.compression_settings
        }
        return jsonify({'success': True, 'config': config})
