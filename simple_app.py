#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QuantTradeX 简化版Web应用
不依赖TA-Lib的快速启动版本
"""

from flask import Flask, render_template, request, jsonify
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yfinance as yf
import redis
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'quanttradex_secret_key_2025'

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'quanttradex',
    'user': 'quanttradex_user',
    'password': 'quanttradex123'
}

# Redis配置
REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0
}

class QuantTradeXApp:
    def __init__(self):
        self.redis_client = None
        self.db_connection = None
        self.init_connections()

    def init_connections(self):
        """初始化数据库连接"""
        try:
            # Redis连接
            self.redis_client = redis.Redis(**REDIS_CONFIG)
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.redis_client = None

        try:
            # PostgreSQL连接
            self.db_connection = psycopg2.connect(**DB_CONFIG)
            logger.info("PostgreSQL连接成功")
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {e}")
            self.db_connection = None

    def get_stock_data(self, symbol, period='1mo'):
        """获取股票数据"""
        try:
            # 先从缓存获取
            cache_key = f"stock_data:{symbol}:{period}"
            if self.redis_client:
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)

            # 从Yahoo Finance获取数据
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)

            if hist.empty:
                return None

            # 转换为JSON格式
            data = {
                'symbol': symbol,
                'dates': hist.index.strftime('%Y-%m-%d').tolist(),
                'open': hist['Open'].tolist(),
                'high': hist['High'].tolist(),
                'low': hist['Low'].tolist(),
                'close': hist['Close'].tolist(),
                'volume': hist['Volume'].tolist(),
                'last_update': datetime.now().isoformat()
            }

            # 缓存数据（5分钟）
            if self.redis_client:
                self.redis_client.setex(cache_key, 300, json.dumps(data))

            return data
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return None

    def calculate_simple_indicators(self, data):
        """计算简单技术指标（不使用TA-Lib）"""
        try:
            if not data or len(data['close']) < 20:
                return {}

            closes = np.array(data['close'])

            # 简单移动平均线
            def sma(prices, window):
                return np.convolve(prices, np.ones(window)/window, mode='valid')

            sma_20 = sma(closes, 20)
            sma_50 = sma(closes, 50) if len(closes) >= 50 else []

            # 简单RSI计算
            def calculate_rsi(prices, period=14):
                if len(prices) < period + 1:
                    return []

                deltas = np.diff(prices)
                gains = np.where(deltas > 0, deltas, 0)
                losses = np.where(deltas < 0, -deltas, 0)

                # 简单移动平均
                avg_gains = []
                avg_losses = []

                for i in range(period - 1, len(gains)):
                    avg_gain = np.mean(gains[i-period+1:i+1])
                    avg_loss = np.mean(losses[i-period+1:i+1])
                    avg_gains.append(avg_gain)
                    avg_losses.append(avg_loss)

                rsi = []
                for i in range(len(avg_gains)):
                    if avg_losses[i] == 0:
                        rsi.append(100)
                    else:
                        rs = avg_gains[i] / avg_losses[i]
                        rsi.append(100 - (100 / (1 + rs)))

                return rsi

            rsi = calculate_rsi(closes)

            # 布林带
            def bollinger_bands(prices, window=20, num_std=2):
                if len(prices) < window:
                    return [], [], []

                sma_values = sma(prices, window)
                std_values = []
                upper_band = []
                lower_band = []

                for i in range(window-1, len(prices)):
                    std_val = np.std(prices[i-window+1:i+1])
                    std_values.append(std_val)
                    upper_band.append(sma_values[i-window+1] + num_std * std_val)
                    lower_band.append(sma_values[i-window+1] - num_std * std_val)

                return upper_band, lower_band, sma_values

            bb_upper, bb_lower, bb_middle = bollinger_bands(closes)

            return {
                'sma_20': sma_20.tolist(),
                'sma_50': sma_50.tolist(),
                'rsi': rsi,
                'bb_upper': bb_upper,
                'bb_lower': bb_lower,
                'bb_middle': bb_middle.tolist() if len(bb_middle) > 0 else [],
                'current_price': closes[-1],
                'price_change': closes[-1] - closes[-2] if len(closes) > 1 else 0,
                'price_change_pct': ((closes[-1] - closes[-2]) / closes[-2] * 100) if len(closes) > 1 else 0,
                'volume_avg': np.mean(data['volume'][-20:]) if len(data['volume']) >= 20 else np.mean(data['volume']),
                'high_52w': np.max(closes[-252:]) if len(closes) >= 252 else np.max(closes),
                'low_52w': np.min(closes[-252:]) if len(closes) >= 252 else np.min(closes)
            }
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}

# 创建应用实例
quanttradex = QuantTradeXApp()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    """交易仪表板"""
    return render_template('dashboard.html')

@app.route('/api/stock/<symbol>')
def get_stock(symbol):
    """获取股票数据API"""
    period = request.args.get('period', '1mo')
    data = quanttradex.get_stock_data(symbol.upper(), period)

    if data:
        indicators = quanttradex.calculate_simple_indicators(data)
        data['indicators'] = indicators
        return jsonify({'success': True, 'data': data})
    else:
        return jsonify({'success': False, 'error': '无法获取股票数据'})

@app.route('/api/watchlist')
def get_watchlist():
    """获取关注列表"""
    # 默认关注列表
    watchlist = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN']
    results = []

    for symbol in watchlist:
        data = quanttradex.get_stock_data(symbol, '1d')
        if data:
            indicators = quanttradex.calculate_simple_indicators(data)
            results.append({
                'symbol': symbol,
                'price': indicators.get('current_price', 0),
                'change': indicators.get('price_change', 0),
                'change_pct': indicators.get('price_change_pct', 0)
            })

    return jsonify({'success': True, 'data': results})

@app.route('/api/system/status')
def system_status():
    """系统状态检查"""
    status = {
        'redis': False,
        'database': False,
        'timestamp': datetime.now().isoformat()
    }

    try:
        if quanttradex.redis_client:
            quanttradex.redis_client.ping()
            status['redis'] = True
    except:
        pass

    try:
        if quanttradex.db_connection:
            with quanttradex.db_connection.cursor() as cursor:
                cursor.execute('SELECT 1')
            status['database'] = True
    except:
        pass

    return jsonify(status)

@app.route('/api/market/summary')
def market_summary():
    """市场概览"""
    try:
        # 获取主要指数数据
        indices = {
            '^GSPC': 'S&P 500',
            '^DJI': 'Dow Jones',
            '^IXIC': 'NASDAQ',
            '^VIX': 'VIX'
        }

        results = {}
        for symbol, name in indices.items():
            data = quanttradex.get_stock_data(symbol, '1d')
            if data:
                indicators = quanttradex.calculate_simple_indicators(data)
                results[symbol] = {
                    'name': name,
                    'price': indicators.get('current_price', 0),
                    'change': indicators.get('price_change', 0),
                    'change_pct': indicators.get('price_change_pct', 0)
                }

        return jsonify({'success': True, 'data': results})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)

    print("🚀 启动 QuantTradeX 简化版应用")
    print("📊 访问地址: http://www.qclb.com:5000")
    print("🔧 功能: 股票数据查询、技术指标计算、实时图表")
    print("⚡ 特点: 快速启动，无需TA-Lib依赖")

    app.run(host='0.0.0.0', port=5000, debug=True)
