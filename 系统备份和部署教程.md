# QuantTradeX 系统备份和部署教程

## 📋 目录
1. [系统备份](#系统备份)
2. [新服务器部署](#新服务器部署)
3. [环境配置](#环境配置)
4. [数据迁移](#数据迁移)
5. [日常维护](#日常维护)
6. [故障恢复](#故障恢复)

## 🗂️ 系统备份

### 1. 完整备份脚本

创建自动备份脚本：

```bash
#!/bin/bash
# backup_quanttradex.sh

# 配置变量
BACKUP_DIR="/backup/quanttradex"
PROJECT_DIR="/www/wwwroot/qclb.com"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="quanttradex_backup_${DATE}"

# 创建备份目录
mkdir -p ${BACKUP_DIR}/${BACKUP_NAME}

echo "🚀 开始备份 QuantTradeX 系统..."

# 1. 备份项目文件
echo "📁 备份项目文件..."
cp -r ${PROJECT_DIR} ${BACKUP_DIR}/${BACKUP_NAME}/project/

# 2. 备份数据库（如果有）
echo "🗄️ 备份数据库..."
# mysqldump -u username -p password quanttradex > ${BACKUP_DIR}/${BACKUP_NAME}/database.sql

# 3. 备份配置文件
echo "⚙️ 备份配置文件..."
mkdir -p ${BACKUP_DIR}/${BACKUP_NAME}/config/
cp /etc/nginx/sites-available/qclb.com ${BACKUP_DIR}/${BACKUP_NAME}/config/ 2>/dev/null || true
cp /etc/systemd/system/quanttradex.service ${BACKUP_DIR}/${BACKUP_NAME}/config/ 2>/dev/null || true

# 4. 备份环境信息
echo "🔧 备份环境信息..."
python3 --version > ${BACKUP_DIR}/${BACKUP_NAME}/environment.txt
pip3 list >> ${BACKUP_DIR}/${BACKUP_NAME}/environment.txt
nginx -v >> ${BACKUP_DIR}/${BACKUP_NAME}/environment.txt 2>&1

# 5. 创建压缩包
echo "📦 创建压缩包..."
cd ${BACKUP_DIR}
tar -czf ${BACKUP_NAME}.tar.gz ${BACKUP_NAME}/
rm -rf ${BACKUP_NAME}/

# 6. 清理旧备份（保留最近7天）
echo "🧹 清理旧备份..."
find ${BACKUP_DIR} -name "quanttradex_backup_*.tar.gz" -mtime +7 -delete

echo "✅ 备份完成: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
```

### 2. 设置定时备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点自动备份
0 2 * * * /backup/scripts/backup_quanttradex.sh >> /var/log/quanttradex_backup.log 2>&1

# 每周日凌晨3点备份到远程
0 3 * * 0 rsync -av /backup/quanttradex/ user@remote-server:/remote/backup/quanttradex/
```

### 3. 手动备份命令

```bash
# 快速备份当前状态
cd /www/wwwroot
tar -czf qclb_backup_$(date +%Y%m%d).tar.gz qclb.com/

# 备份到指定位置
cp -r qclb.com /backup/manual_backup_$(date +%Y%m%d)/
```

## 🚀 新服务器部署

### 1. 服务器环境要求

**最低配置**:
- CPU: 2核
- 内存: 4GB
- 存储: 50GB SSD
- 操作系统: Ubuntu 20.04+ / CentOS 7+

**推荐配置**:
- CPU: 4核
- 内存: 8GB
- 存储: 100GB SSD
- 带宽: 10Mbps+

### 2. 基础环境安装

```bash
# Ubuntu/Debian 系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y python3 python3-pip python3-venv nginx git curl wget

# CentOS/RHEL 系统
sudo yum update -y
sudo yum install -y python3 python3-pip nginx git curl wget

# 安装 Node.js (可选，用于前端构建)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

### 3. 创建项目目录

```bash
# 创建项目目录
sudo mkdir -p /www/wwwroot
sudo chown -R $USER:$USER /www/wwwroot
cd /www/wwwroot

# 创建备份目录
sudo mkdir -p /backup/{scripts,quanttradex}
sudo chown -R $USER:$USER /backup
```

## 📦 部署步骤

### 1. 从备份恢复

```bash
# 上传备份文件到新服务器
scp quanttradex_backup_20250127.tar.gz user@new-server:/tmp/

# 在新服务器上解压
cd /tmp
tar -xzf quanttradex_backup_20250127.tar.gz

# 复制项目文件
cp -r quanttradex_backup_20250127/project/qclb.com /www/wwwroot/

# 设置权限
sudo chown -R www-data:www-data /www/wwwroot/qclb.com
sudo chmod -R 755 /www/wwwroot/qclb.com
```

### 2. Python环境配置

```bash
cd /www/wwwroot/qclb.com

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install flask gunicorn

# 如果有requirements.txt
pip install -r requirements.txt
```

### 3. Nginx配置

```bash
# 创建Nginx配置文件
sudo nano /etc/nginx/sites-available/qclb.com
```

```nginx
server {
    listen 80;
    server_name qclb.com www.qclb.com;

    # 项目根目录
    root /www/wwwroot/qclb.com;
    index index.html;

    # 静态文件处理
    location /static/ {
        alias /www/wwwroot/qclb.com/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # API请求转发到Flask
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 认证请求转发
    location /auth/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 其他路由转发到Flask
    location ~ ^/(dashboard|strategies|backtest|forum|strategy-editor)$ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 默认处理静态文件
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 安全设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # 日志
    access_log /var/log/nginx/qclb.com.access.log;
    error_log /var/log/nginx/qclb.com.error.log;
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/qclb.com /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 4. 创建系统服务

```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/quanttradex.service
```

```ini
[Unit]
Description=QuantTradeX Flask Application
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/www/wwwroot/qclb.com
Environment=PATH=/www/wwwroot/qclb.com/venv/bin
ExecStart=/www/wwwroot/qclb.com/venv/bin/gunicorn --bind 127.0.0.1:5000 --workers 4 app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start quanttradex
sudo systemctl enable quanttradex

# 检查状态
sudo systemctl status quanttradex
```

## 🔧 环境配置

### 1. 防火墙设置

```bash
# Ubuntu UFW
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL证书配置（可选）

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d qclb.com -d www.qclb.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 性能优化

```bash
# 调整系统参数
echo 'net.core.somaxconn = 65535' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 调整文件描述符限制
echo '* soft nofile 65535' | sudo tee -a /etc/security/limits.conf
echo '* hard nofile 65535' | sudo tee -a /etc/security/limits.conf
```

## 📊 监控和日志

### 1. 日志配置

```bash
# 创建日志目录
sudo mkdir -p /var/log/quanttradex
sudo chown www-data:www-data /var/log/quanttradex

# 配置日志轮转
sudo nano /etc/logrotate.d/quanttradex
```

```
/var/log/quanttradex/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload quanttradex
    endscript
}
```

### 2. 监控脚本

```bash
# 创建监控脚本
nano /backup/scripts/monitor_quanttradex.sh
```

```bash
#!/bin/bash
# monitor_quanttradex.sh

# 检查服务状态
if ! systemctl is-active --quiet quanttradex; then
    echo "$(date): QuantTradeX service is down, restarting..." >> /var/log/quanttradex/monitor.log
    systemctl restart quanttradex
fi

# 检查Nginx状态
if ! systemctl is-active --quiet nginx; then
    echo "$(date): Nginx is down, restarting..." >> /var/log/quanttradex/monitor.log
    systemctl restart nginx
fi

# 检查磁盘空间
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is ${DISK_USAGE}%, cleaning up..." >> /var/log/quanttradex/monitor.log
    # 清理临时文件
    find /tmp -type f -mtime +7 -delete
fi

# 检查内存使用
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEM_USAGE -gt 90 ]; then
    echo "$(date): Memory usage is ${MEM_USAGE}%, consider optimization" >> /var/log/quanttradex/monitor.log
fi
```

```bash
# 设置定时监控
crontab -e
# 每5分钟检查一次
*/5 * * * * /backup/scripts/monitor_quanttradex.sh
```

## 🔄 日常维护

### 1. 定期维护任务

```bash
# 创建维护脚本
nano /backup/scripts/maintenance.sh
```

```bash
#!/bin/bash
# maintenance.sh

echo "$(date): Starting maintenance tasks..."

# 1. 更新系统包
apt update && apt upgrade -y

# 2. 清理日志文件
journalctl --vacuum-time=30d

# 3. 清理临时文件
find /tmp -type f -mtime +7 -delete
find /var/tmp -type f -mtime +7 -delete

# 4. 检查磁盘空间
df -h

# 5. 检查服务状态
systemctl status quanttradex nginx

# 6. 备份重要配置
cp /etc/nginx/sites-available/qclb.com /backup/config/nginx_$(date +%Y%m%d).conf
cp /etc/systemd/system/quanttradex.service /backup/config/service_$(date +%Y%m%d).conf

echo "$(date): Maintenance tasks completed."
```

### 2. 性能监控

```bash
# 创建性能监控脚本
nano /backup/scripts/performance_check.sh
```

```bash
#!/bin/bash
# performance_check.sh

LOG_FILE="/var/log/quanttradex/performance.log"

echo "$(date): Performance Check" >> $LOG_FILE
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)" >> $LOG_FILE
echo "Memory Usage: $(free | awk 'NR==2{printf "%.2f%%", $3*100/$2}')" >> $LOG_FILE
echo "Disk Usage: $(df / | awk 'NR==2 {print $5}')" >> $LOG_FILE
echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')" >> $LOG_FILE
echo "Active Connections: $(netstat -an | grep :80 | wc -l)" >> $LOG_FILE
echo "---" >> $LOG_FILE
```

### 3. 安全检查

```bash
# 创建安全检查脚本
nano /backup/scripts/security_check.sh
```

```bash
#!/bin/bash
# security_check.sh

SECURITY_LOG="/var/log/quanttradex/security.log"

echo "$(date): Security Check" >> $SECURITY_LOG

# 检查失败的登录尝试
echo "Failed login attempts:" >> $SECURITY_LOG
grep "Failed password" /var/log/auth.log | tail -10 >> $SECURITY_LOG

# 检查异常网络连接
echo "Unusual network connections:" >> $SECURITY_LOG
netstat -tuln | grep -E ":(22|80|443|5000)" >> $SECURITY_LOG

# 检查文件权限
echo "File permissions check:" >> $SECURITY_LOG
find /www/wwwroot/qclb.com -type f -perm 777 >> $SECURITY_LOG

echo "---" >> $SECURITY_LOG
```

## 🆘 故障恢复

### 1. 常见问题排查

```bash
# 服务无法启动
sudo systemctl status quanttradex
sudo journalctl -u quanttradex -f

# Nginx配置错误
sudo nginx -t
sudo tail -f /var/log/nginx/error.log

# 端口占用检查
sudo netstat -tulpn | grep :5000
sudo lsof -i :5000

# 磁盘空间不足
df -h
du -sh /var/log/*
```

### 2. 快速恢复步骤

```bash
# 1. 停止服务
sudo systemctl stop quanttradex nginx

# 2. 从备份恢复
cd /backup/quanttradex
tar -xzf latest_backup.tar.gz
cp -r backup_files/* /www/wwwroot/qclb.com/

# 3. 修复权限
sudo chown -R www-data:www-data /www/wwwroot/qclb.com
sudo chmod -R 755 /www/wwwroot/qclb.com

# 4. 重启服务
sudo systemctl start quanttradex nginx
```

### 3. 数据恢复

```bash
# 恢复数据库（如果使用）
mysql -u username -p quanttradex < backup_database.sql

# 恢复配置文件
sudo cp backup_config/nginx.conf /etc/nginx/sites-available/qclb.com
sudo cp backup_config/quanttradex.service /etc/systemd/system/

# 重载配置
sudo systemctl daemon-reload
sudo nginx -s reload
```

## 📋 部署检查清单

### 部署前检查
- [ ] 服务器环境满足要求
- [ ] 备份文件完整性验证
- [ ] 网络连接正常
- [ ] 域名DNS解析配置

### 部署过程检查
- [ ] 项目文件复制完成
- [ ] Python环境配置正确
- [ ] Nginx配置无误
- [ ] 系统服务创建成功
- [ ] 防火墙规则配置

### 部署后验证
- [ ] 网站可以正常访问
- [ ] API接口响应正常
- [ ] 用户登录功能正常
- [ ] 静态文件加载正常
- [ ] 日志记录正常

### 监控配置
- [ ] 自动备份脚本配置
- [ ] 监控脚本运行正常
- [ ] 日志轮转配置
- [ ] 性能监控启用
- [ ] 安全检查配置

## 🚀 快速部署指南

### 一键部署命令

```bash
# 1. 下载部署脚本
wget https://raw.githubusercontent.com/quanttradex/deploy/main/deploy_quanttradex.sh

# 2. 赋予执行权限
chmod +x deploy_quanttradex.sh

# 3. 执行部署（新安装）
sudo ./deploy_quanttradex.sh

# 4. 从备份恢复部署
sudo ./deploy_quanttradex.sh /path/to/backup.tar.gz
```

### 快速备份命令

```bash
# 1. 执行备份脚本
sudo /backup/scripts/backup_quanttradex.sh

# 2. 手动快速备份
cd /www/wwwroot && tar -czf qclb_backup_$(date +%Y%m%d).tar.gz qclb.com/
```

### 服务管理命令

```bash
# 启动服务
sudo systemctl start quanttradex nginx

# 停止服务
sudo systemctl stop quanttradex nginx

# 重启服务
sudo systemctl restart quanttradex nginx

# 查看状态
sudo systemctl status quanttradex nginx

# 查看日志
sudo journalctl -u quanttradex -f
sudo tail -f /var/log/nginx/qclb.com.access.log
```

## 📋 完整文件清单

### 核心文件
```
qclb.com/
├── app.py                          # Flask主应用
├── templates/                      # HTML模板
│   ├── index.html                 # 主页
│   ├── strategies.html            # 策略市场
│   ├── backtest.html             # 回测系统
│   ├── forum.html                # 社区论坛
│   └── strategy-editor.html      # 策略编辑器
├── static/                        # 静态资源
├── venv/                          # Python虚拟环境
├── forum_api_data.json           # 论坛数据
├── forum_data_generator.py       # 数据生成器
└── 项目开发历史记录.md            # 开发历史
```

### 备份脚本
```
backup_scripts/
├── backup_quanttradex.sh          # 自动备份脚本
├── monitor_quanttradex.sh         # 系统监控脚本
└── deploy_quanttradex.sh          # 一键部署脚本
```

### 配置文件
```
/etc/nginx/sites-available/qclb.com     # Nginx配置
/etc/systemd/system/quanttradex.service # 系统服务
```

### 日志文件
```
/var/log/quanttradex/
├── backup.log                     # 备份日志
├── monitor.log                    # 监控日志
├── alerts.log                     # 告警日志
└── performance.log                # 性能日志
```

## 🔧 常用维护命令

### 系统监控
```bash
# 检查系统资源
htop
df -h
free -h
iostat -x 1

# 检查网络连接
netstat -tuln | grep :80
netstat -tuln | grep :5000
ss -tuln

# 检查进程
ps aux | grep python
ps aux | grep nginx
```

### 日志分析
```bash
# 查看访问日志
tail -f /var/log/nginx/qclb.com.access.log

# 查看错误日志
tail -f /var/log/nginx/qclb.com.error.log

# 查看应用日志
journalctl -u quanttradex -f --since "1 hour ago"

# 分析访问统计
awk '{print $1}' /var/log/nginx/qclb.com.access.log | sort | uniq -c | sort -nr | head -10
```

### 性能优化
```bash
# 调整系统参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
sysctl -p

# 调整Nginx配置
# 在nginx.conf中添加：
# worker_processes auto;
# worker_connections 1024;
```

## 🆘 故障排查手册

### 常见问题及解决方案

**1. 网站无法访问**
```bash
# 检查服务状态
systemctl status nginx quanttradex

# 检查端口监听
netstat -tuln | grep :80
netstat -tuln | grep :5000

# 检查防火墙
ufw status
firewall-cmd --list-all

# 重启服务
systemctl restart nginx quanttradex
```

**2. 500内部服务器错误**
```bash
# 查看错误日志
tail -f /var/log/nginx/qclb.com.error.log
journalctl -u quanttradex -f

# 检查Python环境
cd /www/wwwroot/qclb.com
source venv/bin/activate
python app.py

# 检查文件权限
chown -R www-data:www-data /www/wwwroot/qclb.com
chmod -R 755 /www/wwwroot/qclb.com
```

**3. 静态文件无法加载**
```bash
# 检查Nginx配置
nginx -t

# 检查文件权限
ls -la /www/wwwroot/qclb.com/static/

# 重新加载Nginx配置
nginx -s reload
```

**4. 数据库连接失败**
```bash
# 检查数据库服务
systemctl status mysql
systemctl status postgresql

# 检查连接配置
mysql -u username -p -h localhost

# 重启数据库服务
systemctl restart mysql
```

## 📊 监控告警设置

### 设置邮件告警
```bash
# 安装邮件工具
apt install mailutils

# 配置SMTP
echo "set smtp=smtp://smtp.gmail.com:587" >> /etc/mail.rc
echo "set smtp-auth=login" >> /etc/mail.rc
echo "set smtp-auth-user=<EMAIL>" >> /etc/mail.rc
echo "set smtp-auth-password=your-password" >> /etc/mail.rc

# 测试邮件发送
echo "Test message" | mail -s "Test Subject" <EMAIL>
```

### 设置Webhook告警
```bash
# 在监控脚本中配置Webhook
WEBHOOK_ENABLED=true
WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

# 测试Webhook
curl -X POST $WEBHOOK_URL \
     -H "Content-Type: application/json" \
     -d '{"text":"QuantTradeX monitoring test"}'
```

## 🔐 安全加固建议

### 1. SSH安全
```bash
# 修改SSH端口
sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config

# 禁用root登录
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# 重启SSH服务
systemctl restart sshd
```

### 2. 防火墙配置
```bash
# 配置UFW
ufw default deny incoming
ufw default allow outgoing
ufw allow 2222/tcp  # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw enable
```

### 3. SSL证书
```bash
# 安装Certbot
apt install certbot python3-certbot-nginx

# 获取证书
certbot --nginx -d qclb.com -d www.qclb.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### 4. 文件权限
```bash
# 设置正确的文件权限
find /www/wwwroot/qclb.com -type f -exec chmod 644 {} \;
find /www/wwwroot/qclb.com -type d -exec chmod 755 {} \;
chmod +x /www/wwwroot/qclb.com/venv/bin/*
```

## 🎯 总结

这套完整的备份和部署方案包括：

✅ **自动化备份**: 定时备份，保留历史版本
✅ **一键部署**: 全自动部署脚本，支持从备份恢复
✅ **标准化部署**: 详细的部署步骤和配置
✅ **环境配置**: 完整的服务器环境设置
✅ **监控维护**: 自动监控和定期维护
✅ **故障恢复**: 快速恢复和问题排查
✅ **安全加固**: 防火墙和SSL配置
✅ **运维手册**: 详细的维护和故障排查指南

### 🚀 立即开始

1. **新服务器部署**：
   ```bash
   wget -O deploy.sh https://raw.githubusercontent.com/quanttradex/deploy/main/deploy_quanttradex.sh
   chmod +x deploy.sh
   sudo ./deploy.sh
   ```

2. **从备份恢复**：
   ```bash
   sudo ./deploy.sh /path/to/backup.tar.gz
   ```

3. **设置定时备份**：
   ```bash
   sudo crontab -e
   # 添加: 0 2 * * * /backup/scripts/backup_quanttradex.sh
   ```

4. **启用监控**：
   ```bash
   sudo crontab -e
   # 添加: */5 * * * * /backup/scripts/monitor_quanttradex.sh
   ```

按照这个教程，您可以轻松地将QuantTradeX系统迁移到任何新服务器，并建立完善的运维体系。
