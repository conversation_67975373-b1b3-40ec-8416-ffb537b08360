#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
创建用户表和其他必要的表结构，实现真正的数据持久化
"""

import psycopg2
import hashlib
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'quanttradex',
    'user': 'quanttradex_user',
    'password': 'quanttradex123'
}

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_database_tables():
    """创建数据库表"""
    try:
        # 连接数据库
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 检查并添加缺失的字段到现有users表
        missing_columns = [
            ('phone', 'VARCHAR(20)'),
            ('region', 'VARCHAR(10)'),
            ('risk_preference', 'VARCHAR(20)'),
            ('experience', 'VARCHAR(20)'),
            ('updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
            ('is_premium', 'BOOLEAN DEFAULT FALSE'),
            ('premium_expires', 'TIMESTAMP'),
            ('total_strategies', 'INTEGER DEFAULT 0'),
            ('total_backtests', 'INTEGER DEFAULT 0'),
            ('total_profit', 'DECIMAL(15,2) DEFAULT 0.00'),
            ('win_rate', 'DECIMAL(5,2) DEFAULT 0.00'),
            ('followers', 'INTEGER DEFAULT 0'),
            ('following', 'INTEGER DEFAULT 0'),
            ('backup_codes', 'TEXT')
        ]

        for column_name, column_type in missing_columns:
            try:
                cursor.execute(f"""
                    ALTER TABLE users ADD COLUMN IF NOT EXISTS {column_name} {column_type}
                """)
                logger.info(f"添加字段: {column_name}")
            except Exception as e:
                logger.warning(f"添加字段 {column_name} 失败: {e}")

        # 创建用户表（如果不存在）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(64) NOT NULL,
                full_name VARCHAR(100),
                role VARCHAR(20) DEFAULT 'user',
                avatar_url VARCHAR(255),
                bio TEXT,
                phone VARCHAR(20),
                region VARCHAR(10),
                risk_preference VARCHAR(20),
                experience VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_premium BOOLEAN DEFAULT FALSE,
                premium_expires TIMESTAMP,
                total_strategies INTEGER DEFAULT 0,
                total_backtests INTEGER DEFAULT 0,
                total_profit DECIMAL(15,2) DEFAULT 0.00,
                win_rate DECIMAL(5,2) DEFAULT 0.00,
                followers INTEGER DEFAULT 0,
                following INTEGER DEFAULT 0,
                two_factor_enabled BOOLEAN DEFAULT FALSE,
                two_factor_secret VARCHAR(32),
                backup_codes TEXT
            )
        """)

        # 创建用户关注列表表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_watchlists (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                symbol VARCHAR(20) NOT NULL,
                asset_type VARCHAR(20) NOT NULL,
                name VARCHAR(100),
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, symbol, asset_type)
            )
        """)

        # 创建支付订单表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payment_orders (
                id SERIAL PRIMARY KEY,
                order_id VARCHAR(50) UNIQUE NOT NULL,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                plan VARCHAR(20) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(10) DEFAULT 'CNY',
                payment_method VARCHAR(20) NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP,
                payment_data TEXT
            )
        """)

        # 创建策略表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategies (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                code TEXT NOT NULL,
                language VARCHAR(20) DEFAULT 'python',
                category VARCHAR(50),
                tags TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                price DECIMAL(10,2) DEFAULT 0.00,
                downloads INTEGER DEFAULT 0,
                rating DECIMAL(3,2) DEFAULT 0.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 创建回测结果表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS backtest_results (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                strategy_id INTEGER REFERENCES strategies(id) ON DELETE CASCADE,
                symbol VARCHAR(20) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                initial_capital DECIMAL(15,2) NOT NULL,
                final_value DECIMAL(15,2) NOT NULL,
                total_return DECIMAL(10,4) NOT NULL,
                sharpe_ratio DECIMAL(10,4),
                max_drawdown DECIMAL(10,4),
                win_rate DECIMAL(5,2),
                total_trades INTEGER,
                results_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 创建论坛帖子表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS forum_posts (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                title VARCHAR(200) NOT NULL,
                content TEXT NOT NULL,
                category VARCHAR(50),
                tags TEXT,
                views INTEGER DEFAULT 0,
                likes INTEGER DEFAULT 0,
                replies INTEGER DEFAULT 0,
                is_pinned BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 创建论坛回复表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS forum_replies (
                id SERIAL PRIMARY KEY,
                post_id INTEGER REFERENCES forum_posts(id) ON DELETE CASCADE,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                content TEXT NOT NULL,
                likes INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 提交事务
        conn.commit()
        logger.info("数据库表创建成功")

        return conn, cursor

    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        return None, None

def insert_default_users(cursor, conn):
    """插入默认用户数据"""
    try:
        # 默认用户数据
        default_users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'full_name': '系统管理员',
                'role': 'admin',
                'avatar_url': '/static/img/avatar-admin.png',
                'bio': 'QuantTradeX平台管理员，负责平台运营和技术支持',
                'phone': '+86 138****0001',
                'region': 'cn',
                'risk_preference': 'moderate',
                'experience': 'expert',
                'is_premium': True,
                'premium_expires': '2026-01-01 00:00:00',
                'total_strategies': 5,
                'total_backtests': 25,
                'total_profit': 156780.50,
                'win_rate': 78.5,
                'followers': 1250,
                'following': 45
            },
            {
                'username': 'trader1',
                'email': '<EMAIL>',
                'password': 'password123',
                'full_name': '专业交易员',
                'role': 'user',
                'avatar_url': '/static/img/avatar-trader.png',
                'bio': '专业量化交易员，擅长高频交易和套利策略',
                'phone': '+86 138****0003',
                'region': 'cn',
                'risk_preference': 'aggressive',
                'experience': 'expert',
                'is_premium': True,
                'premium_expires': '2025-06-01 00:00:00',
                'total_strategies': 12,
                'total_backtests': 89,
                'total_profit': 89234.67,
                'win_rate': 75.2,
                'followers': 567,
                'following': 89
            },
            {
                'username': 'vip_user',
                'email': '<EMAIL>',
                'password': 'password123',
                'full_name': 'VIP用户',
                'role': 'premium',
                'avatar_url': '/static/img/avatar-vip.png',
                'bio': 'VIP会员，享受平台所有高级功能',
                'phone': '+86 138****0005',
                'region': 'cn',
                'risk_preference': 'moderate',
                'experience': 'advanced',
                'is_premium': True,
                'premium_expires': '2025-12-31 00:00:00',
                'total_strategies': 8,
                'total_backtests': 45,
                'total_profit': 45678.90,
                'win_rate': 68.9,
                'followers': 234,
                'following': 123
            },
            {
                'username': 'quant_expert',
                'email': '<EMAIL>',
                'password': 'password123',
                'full_name': '量化专家',
                'role': 'premium',
                'avatar_url': '/static/img/avatar-expert.png',
                'bio': '金融工程博士，专注机器学习在量化交易中的应用',
                'phone': '+86 138****0004',
                'region': 'us',
                'risk_preference': 'moderate',
                'experience': 'expert',
                'is_premium': True,
                'premium_expires': '2025-12-15 09:20:00',
                'total_strategies': 18,
                'total_backtests': 234,
                'total_profit': 234567.89,
                'win_rate': 82.7,
                'followers': 1567,
                'following': 234
            },
            {
                'username': 'crypto_trader',
                'email': '<EMAIL>',
                'password': 'password123',
                'full_name': '加密货币交易员',
                'role': 'user',
                'avatar_url': '/static/img/avatar-crypto.png',
                'bio': '专注加密货币市场，熟悉DeFi和NFT交易',
                'phone': '+86 138****0006',
                'region': 'cn',
                'risk_preference': 'aggressive',
                'experience': 'advanced',
                'is_premium': False,
                'total_strategies': 6,
                'total_backtests': 34,
                'total_profit': 12345.67,
                'win_rate': 65.4,
                'followers': 123,
                'following': 456
            }
        ]

        for user_data in default_users:
            # 检查用户是否已存在
            cursor.execute("SELECT id FROM users WHERE username = %s", (user_data['username'],))
            if cursor.fetchone():
                logger.info(f"用户 {user_data['username']} 已存在，跳过")
                continue

            # 插入用户
            cursor.execute("""
                INSERT INTO users (
                    username, email, password_hash, full_name, role, avatar_url, bio,
                    phone, region, risk_preference, experience, is_premium, premium_expires,
                    total_strategies, total_backtests, total_profit, win_rate, followers, following
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                user_data['username'],
                user_data['email'],
                hash_password(user_data['password']),
                user_data['full_name'],
                user_data['role'],
                user_data['avatar_url'],
                user_data['bio'],
                user_data['phone'],
                user_data['region'],
                user_data['risk_preference'],
                user_data['experience'],
                user_data['is_premium'],
                user_data.get('premium_expires'),
                user_data['total_strategies'],
                user_data['total_backtests'],
                user_data['total_profit'],
                user_data['win_rate'],
                user_data['followers'],
                user_data['following']
            ))

            logger.info(f"插入用户: {user_data['username']}")

        conn.commit()
        logger.info("默认用户数据插入成功")

    except Exception as e:
        logger.error(f"插入默认用户失败: {e}")
        conn.rollback()

def main():
    """主函数"""
    logger.info("开始初始化数据库...")

    # 创建表
    conn, cursor = create_database_tables()
    if not conn:
        logger.error("数据库初始化失败")
        return

    # 插入默认用户
    insert_default_users(cursor, conn)

    # 关闭连接
    cursor.close()
    conn.close()

    logger.info("数据库初始化完成！")
    logger.info("现在用户数据将永久保存在PostgreSQL数据库中")

if __name__ == "__main__":
    main()
