# QuantTradeX 项目移植指导文档

## 📋 项目概述
- **源服务器**: qclb.com (当前运行环境)
- **目标服务器**: www.gdpp.com (Ubuntu 22.04 + 宝塔面板)
- **项目类型**: Flask量化交易平台
- **数据库**: PostgreSQL + Redis
- **Web服务器**: Nginx

## 🎯 移植目标
将完整的QuantTradeX量化交易平台从当前服务器迁移到新的大空间服务器，确保：
- 所有功能正常运行
- 用户数据完整迁移
- 域名更换为www.gdpp.com
- 性能和稳定性提升

## 📦 第一步：项目打包和备份

### 1.1 创建完整项目备份
```bash
# 在当前服务器执行
cd /www/wwwroot/qclb.com

# 创建项目完整备份
tar -czf quanttradex_migration_$(date +%Y%m%d_%H%M%S).tar.gz \
    --exclude='venv' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='.git' \
    .

# 检查备份文件
ls -lh quanttradex_migration_*.tar.gz
```

### 1.2 导出数据库数据
```bash
# PostgreSQL数据导出
pg_dump -h localhost -U quanttradex_user -d quanttradex > quanttradex_db_backup.sql

# Redis数据导出（如果有重要数据）
redis-cli --rdb quanttradex_redis_backup.rdb
```

### 1.3 备份配置文件
```bash
# 备份Nginx配置
cp /www/server/panel/vhost/nginx/qclb.com.conf nginx_config_backup.conf

# 备份环境变量和配置
cp -r /www/wwwroot/qclb.com/config config_backup/
```

## 🚀 第二步：新服务器环境准备

### 2.1 宝塔面板基础配置
1. **登录宝塔面板**
   ```
   访问: http://your-server-ip:8888
   ```

2. **安装必要软件**
   - Python 3.8+
   - PostgreSQL 13+
   - Redis 6+
   - Nginx 1.18+
   - PM2 (进程管理)

### 2.2 创建网站和数据库
1. **在宝塔面板创建网站**
   - 域名: www.gdpp.com
   - 根目录: /www/wwwroot/www.gdpp.com
   - PHP版本: 不选择（Python项目）

2. **创建数据库**
   - 数据库名: quanttradex
   - 用户名: quanttradex_user
   - 密码: 生成强密码并记录

3. **Redis配置**
   - 启动Redis服务
   - 设置密码（可选）

### 2.3 系统依赖安装
```bash
# SSH连接到新服务器
ssh root@your-new-server-ip

# 更新系统
apt update && apt upgrade -y

# 安装Python开发环境
apt install python3-pip python3-venv python3-dev build-essential -y

# 安装PostgreSQL开发库
apt install libpq-dev -y

# 安装其他依赖
apt install git curl wget unzip -y
```

## 📁 第三步：项目文件传输

### 3.1 上传项目文件
```bash
# 方法1: 使用scp传输
scp quanttradex_migration_*.tar.gz root@new-server-ip:/www/wwwroot/

# 方法2: 使用宝塔面板文件管理器上传
# 在宝塔面板 -> 文件 -> 上传文件

# 方法3: 使用wget从当前服务器下载
cd /www/wwwroot/
wget http://qclb.com/backup/quanttradex_migration_*.tar.gz
```

### 3.2 解压和部署
```bash
# 进入网站目录
cd /www/wwwroot/www.gdpp.com

# 解压项目文件
tar -xzf ../quanttradex_migration_*.tar.gz

# 设置正确的权限
chown -R www:www /www/wwwroot/www.gdpp.com
chmod -R 755 /www/wwwroot/www.gdpp.com
```

## 🐍 第四步：Python环境配置

### 4.1 创建虚拟环境
```bash
cd /www/wwwroot/www.gdpp.com

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 4.2 安装项目依赖
```bash
# 安装基础依赖
pip install flask flask-socketio

# 安装数据库依赖
pip install psycopg2-binary redis

# 安装数据分析依赖
pip install pandas numpy yfinance

# 安装其他依赖
pip install requests pyotp qrcode[pil] cryptography

# 如果有requirements.txt
pip install -r requirements.txt
```

## 🗄️ 第五步：数据库配置

### 5.1 PostgreSQL配置
```bash
# 切换到postgres用户
sudo -u postgres psql

# 创建数据库和用户
CREATE DATABASE quanttradex;
CREATE USER quanttradex_user WITH PASSWORD 'your_strong_password';
GRANT ALL PRIVILEGES ON DATABASE quanttradex TO quanttradex_user;
\q
```

### 5.2 导入数据库数据
```bash
# 导入数据库备份
psql -h localhost -U quanttradex_user -d quanttradex < quanttradex_db_backup.sql

# 或者运行初始化脚本
cd /www/wwwroot/www.gdpp.com
python database_init.py
```

### 5.3 Redis配置
```bash
# 启动Redis
systemctl start redis
systemctl enable redis

# 测试Redis连接
redis-cli ping
```

## ⚙️ 第六步：应用配置修改

### 6.1 更新配置文件
```python
# 编辑 app.py 或 config/settings.py
# 更新数据库连接信息
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'quanttradex',
    'user': 'quanttradex_user',
    'password': 'your_new_password'
}

# 更新Redis配置
REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0
}

# 更新域名相关配置
DOMAIN = 'www.gdpp.com'
```

### 6.2 更新模板中的域名引用
```bash
# 批量替换域名
find /www/wwwroot/www.gdpp.com/templates -name "*.html" -exec sed -i 's/qclb\.com/gdpp.com/g' {} \;
find /www/wwwroot/www.gdpp.com/static -name "*.js" -exec sed -i 's/qclb\.com/gdpp.com/g' {} \;
```

## 🌐 第七步：Nginx配置

### 7.1 配置Nginx反向代理
在宝塔面板中配置网站：

1. **网站设置 -> 配置文件**
```nginx
server {
    listen 80;
    server_name www.gdpp.com gdpp.com;

    # 重定向到HTTPS（可选）
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.gdpp.com gdpp.com;

    # SSL证书配置（宝塔面板自动配置）

    # 静态文件处理
    location /static/ {
        alias /www/wwwroot/www.gdpp.com/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # 反向代理到Flask应用
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### 7.2 SSL证书配置
在宝塔面板中：
1. 网站设置 -> SSL
2. 选择Let's Encrypt免费证书
3. 申请并部署证书

## 🔄 第八步：应用启动和进程管理

### 8.1 测试应用启动
```bash
cd /www/wwwroot/www.gdpp.com
source venv/bin/activate

# 测试启动
python app.py

# 检查是否正常运行
curl http://localhost:5000
```

### 8.2 使用PM2进程管理
```bash
# 安装PM2
npm install -g pm2

# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'quanttradex',
    script: '/www/wwwroot/www.gdpp.com/venv/bin/python',
    args: 'app.py',
    cwd: '/www/wwwroot/www.gdpp.com',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
EOF

# 启动应用
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

## 🧪 第九步：功能测试验证

### 9.1 基础功能测试
```bash
# 测试主页
curl -I https://www.gdpp.com

# 测试API接口
curl https://www.gdpp.com/api/user/status

# 测试数据库连接
curl https://www.gdpp.com/api/system/status
```

### 9.2 完整功能测试清单
- [ ] 主页加载正常
- [ ] 用户注册功能
- [ ] 用户登录功能
- [ ] 策略市场页面
- [ ] 我的策略页面
- [ ] 策略开发工具
- [ ] 回测系统
- [ ] 实时数据中心
- [ ] 社区论坛
- [ ] 个人资料管理

## 🔧 第十步：性能优化和监控

### 10.1 性能优化配置
```bash
# 配置Gunicorn（生产环境推荐）
pip install gunicorn

# 创建Gunicorn配置
cat > gunicorn.conf.py << EOF
bind = "127.0.0.1:5000"
workers = 4
worker_class = "eventlet"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
EOF

# 使用Gunicorn启动
gunicorn -c gunicorn.conf.py app:app
```

### 10.2 监控配置
```bash
# 安装监控工具
pip install psutil

# 配置日志
mkdir -p /www/wwwroot/www.gdpp.com/logs

# 在宝塔面板配置日志轮转
```

## 📋 移植检查清单

### 环境检查
- [ ] Ubuntu 22.04系统正常
- [ ] 宝塔面板安装完成
- [ ] Python 3.8+环境
- [ ] PostgreSQL数据库
- [ ] Redis缓存服务
- [ ] Nginx Web服务器

### 项目部署检查
- [ ] 项目文件完整上传
- [ ] 虚拟环境创建成功
- [ ] 依赖包安装完成
- [ ] 数据库连接正常
- [ ] 配置文件更新完成

### 功能验证检查
- [ ] 网站可正常访问
- [ ] 用户认证功能正常
- [ ] 数据库操作正常
- [ ] 静态文件加载正常
- [ ] WebSocket连接正常
- [ ] API接口响应正常

### 安全和性能检查
- [ ] SSL证书配置完成
- [ ] 防火墙规则配置
- [ ] 进程管理配置
- [ ] 日志记录配置
- [ ] 备份策略制定

## 🚨 常见问题和解决方案

### 问题1: 数据库连接失败
```bash
# 检查PostgreSQL服务状态
systemctl status postgresql

# 检查数据库用户权限
sudo -u postgres psql -c "\du"

# 重置用户密码
sudo -u postgres psql -c "ALTER USER quanttradex_user PASSWORD 'new_password';"
```

### 问题2: 端口占用
```bash
# 检查端口占用
netstat -tlnp | grep :5000

# 杀死占用进程
kill -9 PID
```

### 问题3: 权限问题
```bash
# 设置正确权限
chown -R www:www /www/wwwroot/www.gdpp.com
chmod -R 755 /www/wwwroot/www.gdpp.com
```

## 📞 技术支持

如果在移植过程中遇到问题，可以：
1. 检查日志文件：`/www/wwwroot/www.gdpp.com/logs/`
2. 查看宝塔面板错误日志
3. 参考项目开发历史记录文档
4. 联系技术支持

## 🎯 移植后优化建议

### 11.1 数据库优化
```sql
-- PostgreSQL性能优化
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 重启PostgreSQL使配置生效
SELECT pg_reload_conf();
```

### 11.2 Redis优化配置
```bash
# 编辑Redis配置文件
vim /etc/redis/redis.conf

# 添加以下优化配置
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000

# 重启Redis
systemctl restart redis
```

### 11.3 系统级优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

## 📊 移植验证脚本

创建自动化验证脚本：

```bash
#!/bin/bash
# 文件名: verify_migration.sh

echo "🔍 QuantTradeX移植验证开始..."

# 检查基础服务
echo "1. 检查基础服务状态..."
systemctl is-active nginx && echo "✅ Nginx运行正常" || echo "❌ Nginx未运行"
systemctl is-active postgresql && echo "✅ PostgreSQL运行正常" || echo "❌ PostgreSQL未运行"
systemctl is-active redis && echo "✅ Redis运行正常" || echo "❌ Redis未运行"

# 检查端口监听
echo "2. 检查端口监听..."
netstat -tlnp | grep :80 && echo "✅ 端口80监听正常" || echo "❌ 端口80未监听"
netstat -tlnp | grep :443 && echo "✅ 端口443监听正常" || echo "❌ 端口443未监听"
netstat -tlnp | grep :5000 && echo "✅ 端口5000监听正常" || echo "❌ 端口5000未监听"

# 检查网站响应
echo "3. 检查网站响应..."
curl -s -o /dev/null -w "%{http_code}" https://www.gdpp.com | grep 200 && echo "✅ 网站响应正常" || echo "❌ 网站响应异常"

# 检查API接口
echo "4. 检查API接口..."
curl -s https://www.gdpp.com/api/user/status | grep -q "success" && echo "✅ API接口正常" || echo "❌ API接口异常"

# 检查数据库连接
echo "5. 检查数据库连接..."
cd /www/wwwroot/www.gdpp.com
source venv/bin/activate
python -c "
import psycopg2
try:
    conn = psycopg2.connect(
        host='localhost',
        database='quanttradex',
        user='quanttradex_user',
        password='your_password'
    )
    print('✅ 数据库连接正常')
    conn.close()
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
"

echo "🎉 移植验证完成！"
```

## 🔄 数据迁移详细步骤

### 用户数据迁移
```bash
# 1. 从源服务器导出用户数据
cd /www/wwwroot/qclb.com
python -c "
import json
from user_manager import UserManager
um = UserManager()
users = um.get_all_users()
with open('users_export.json', 'w', encoding='utf-8') as f:
    json.dump(users, f, ensure_ascii=False, indent=2)
print('用户数据导出完成')
"

# 2. 在新服务器导入用户数据
cd /www/wwwroot/www.gdpp.com
python -c "
import json
from user_manager import UserManager
with open('users_export.json', 'r', encoding='utf-8') as f:
    users = json.load(f)
um = UserManager()
for user in users:
    um.create_user(user)
print('用户数据导入完成')
"
```

### 策略数据迁移
```bash
# 复制策略相关JSON文件
scp qclb.com:/www/wwwroot/qclb.com/strategy_codes_mapping.json /www/wwwroot/www.gdpp.com/
scp qclb.com:/www/wwwroot/qclb.com/forum_api_data.json /www/wwwroot/www.gdpp.com/
```

## 🛡️ 安全加固配置

### 防火墙配置
```bash
# 使用ufw配置防火墙
ufw enable
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 8888/tcp  # 宝塔面板
ufw deny 5000/tcp   # 禁止直接访问Flask端口
```

### SSL安全配置
在Nginx配置中添加安全头：
```nginx
# 在server块中添加
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header Referrer-Policy "strict-origin-when-cross-origin";
```

## 📈 监控和日志配置

### 应用日志配置
```python
# 在app.py中添加日志配置
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler(
        'logs/quanttradex.log',
        maxBytes=10240000,
        backupCount=10
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('QuantTradeX startup')
```

### 系统监控脚本
```bash
#!/bin/bash
# 文件名: monitor_system.sh

# 创建监控脚本
cat > /www/wwwroot/www.gdpp.com/monitor.py << 'EOF'
import psutil
import requests
import json
import time
from datetime import datetime

def check_system_health():
    """检查系统健康状态"""
    health = {
        'timestamp': datetime.now().isoformat(),
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_percent': psutil.disk_usage('/').percent,
        'load_average': psutil.getloadavg()[0],
    }

    # 检查网站响应
    try:
        response = requests.get('https://www.gdpp.com', timeout=10)
        health['website_status'] = response.status_code
        health['response_time'] = response.elapsed.total_seconds()
    except Exception as e:
        health['website_status'] = 'ERROR'
        health['error'] = str(e)

    return health

if __name__ == '__main__':
    health = check_system_health()
    print(json.dumps(health, indent=2))

    # 如果有问题，可以发送告警
    if health['cpu_percent'] > 80 or health['memory_percent'] > 80:
        print("⚠️ 系统资源使用率过高！")
EOF

# 设置定时监控
echo "*/5 * * * * cd /www/wwwroot/www.gdpp.com && python monitor.py >> logs/monitor.log 2>&1" | crontab -
```

## 🔧 故障排除指南

### 常见错误及解决方案

#### 1. 模块导入错误
```bash
# 错误: ModuleNotFoundError
# 解决方案:
cd /www/wwwroot/www.gdpp.com
source venv/bin/activate
pip install -r requirements.txt

# 如果没有requirements.txt，手动安装
pip install flask flask-socketio psycopg2-binary redis pandas numpy yfinance
```

#### 2. 数据库连接错误
```bash
# 错误: could not connect to server
# 解决方案:
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 检查数据库配置
sudo -u postgres psql -c "\l"
```

#### 3. 权限错误
```bash
# 错误: Permission denied
# 解决方案:
chown -R www:www /www/wwwroot/www.gdpp.com
chmod -R 755 /www/wwwroot/www.gdpp.com
chmod +x /www/wwwroot/www.gdpp.com/app.py
```

#### 4. 端口冲突
```bash
# 错误: Address already in use
# 解决方案:
netstat -tlnp | grep :5000
kill -9 <PID>

# 或者修改应用端口
sed -i 's/port=5000/port=5001/g' app.py
```

## 📋 移植完成检查表

### 基础环境 ✅
- [ ] Ubuntu 22.04系统
- [ ] 宝塔面板安装
- [ ] Python 3.8+环境
- [ ] PostgreSQL数据库
- [ ] Redis缓存
- [ ] Nginx Web服务器

### 项目部署 ✅
- [ ] 项目文件上传
- [ ] 虚拟环境创建
- [ ] 依赖包安装
- [ ] 配置文件更新
- [ ] 数据库初始化

### 功能测试 ✅
- [ ] 主页访问正常
- [ ] 用户注册登录
- [ ] 策略市场功能
- [ ] 我的策略管理
- [ ] 策略开发工具
- [ ] 回测系统
- [ ] 实时数据中心
- [ ] 社区论坛

### 性能优化 ✅
- [ ] Nginx配置优化
- [ ] 数据库性能调优
- [ ] Redis缓存配置
- [ ] 进程管理配置
- [ ] SSL证书部署

### 安全配置 ✅
- [ ] 防火墙规则
- [ ] SSL安全头
- [ ] 数据库安全
- [ ] 文件权限设置
- [ ] 敏感信息保护

### 监控运维 ✅
- [ ] 日志配置
- [ ] 监控脚本
- [ ] 备份策略
- [ ] 告警机制
- [ ] 性能监控

---

## 🎉 移植成功！

**恭喜！您已经成功将QuantTradeX项目移植到www.gdpp.com！**

现在您拥有：
- 🚀 **更大的服务器空间**
- 🔒 **更安全的运行环境**
- 📈 **更好的性能表现**
- 🛡️ **完善的监控体系**

**访问您的新平台**: https://www.gdpp.com

如有任何问题，请参考故障排除指南或联系技术支持。
