server {
    listen 80;
    server_name gdpp.com www.gdpp.com;

    # 网站根目录
    root /www/wwwroot/gdpp.com;
    index index.html index.htm app.py;

    # 日志文件
    access_log /www/wwwlogs/gdpp.com.log;
    error_log /www/wwwlogs/gdpp.com.error.log;

    # 静态文件处理
    location /static/ {
        alias /www/wwwroot/gdpp.com/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Flask应用API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Flask应用页面代理
    location /dashboard {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # QuantTradeX应用代理
    location /quanttradex {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 主页处理 - 优先显示静态文件，然后代理到Flask
    location / {
        try_files $uri $uri/ @flask;
    }

    # Flask应用后备
    location @flask {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }

    location ~ \.py$ {
        deny all;
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}
