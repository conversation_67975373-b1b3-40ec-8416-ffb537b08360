<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉框样式测试 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            color: white;
            padding: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: white;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: white;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
            color: white;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        /* 修复下拉框选项的字体颜色 */
        .form-select option {
            background: #1e293b;
            color: white;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background: #334155;
        }

        .form-select option:checked {
            background: var(--primary);
            color: white;
        }

        .test-section {
            margin-bottom: 2rem;
        }

        .comparison-box {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .before-fix {
            background: rgba(255, 0, 0, 0.1);
            border-color: rgba(255, 0, 0, 0.3);
        }

        .after-fix {
            background: rgba(0, 255, 0, 0.1);
            border-color: rgba(0, 255, 0, 0.3);
        }
    
        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-4">
                    <h1 class="display-5 mb-3">
                        <i class="fas fa-bug me-3"></i>
                        下拉框样式修复测试
                    </h1>
                    <p class="lead">验证策略编辑器中下拉框的字体颜色修复效果</p>
                </div>

                <!-- 修复后的下拉框 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            修复后的下拉框样式
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">策略类型</label>
                                    <select class="form-select" id="strategyType">
                                        <option value="">请选择策略类型</option>
                                        <option value="trend">趋势跟踪</option>
                                        <option value="mean_reversion">均值回归</option>
                                        <option value="arbitrage">套利策略</option>
                                        <option value="momentum">动量策略</option>
                                        <option value="ml">机器学习</option>
                                        <option value="grid">网格交易</option>
                                        <option value="scalping">高频交易</option>
                                        <option value="swing">波段交易</option>
                                        <option value="pairs">配对交易</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">资产类别</label>
                                    <select class="form-select" id="assetClass">
                                        <option value="">请选择资产类别</option>
                                        <option value="stocks">股票</option>
                                        <option value="futures">期货</option>
                                        <option value="crypto">数字货币</option>
                                        <option value="gold">黄金</option>
                                        <option value="forex">外汇</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">时间周期</label>
                                    <select class="form-select">
                                        <option value="">请选择时间周期</option>
                                        <option value="1m">1分钟</option>
                                        <option value="5m">5分钟</option>
                                        <option value="15m">15分钟</option>
                                        <option value="1h">1小时</option>
                                        <option value="4h">4小时</option>
                                        <option value="1d">1天</option>
                                        <option value="1w">1周</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">风险等级</label>
                                    <select class="form-select">
                                        <option value="">请选择风险等级</option>
                                        <option value="low">低风险</option>
                                        <option value="medium">中等风险</option>
                                        <option value="high">高风险</option>
                                        <option value="extreme">极高风险</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>修复说明：</strong>现在下拉框选项使用深色背景和白色文字，确保在深色主题下清晰可见。
                        </div>
                    </div>
                </div>

                <!-- 测试说明 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>
                            测试说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="comparison-box before-fix">
                                    <h6 class="text-danger">
                                        <i class="fas fa-times-circle me-2"></i>修复前问题
                                    </h6>
                                    <ul class="mb-0">
                                        <li>下拉框选项文字为白色</li>
                                        <li>背景也是白色或透明</li>
                                        <li>导致文字不可见</li>
                                        <li>用户无法看到选项内容</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="comparison-box after-fix">
                                    <h6 class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>修复后效果
                                    </h6>
                                    <ul class="mb-0">
                                        <li>选项背景设为深色 (#1e293b)</li>
                                        <li>文字保持白色，形成对比</li>
                                        <li>悬停时背景变为更深色</li>
                                        <li>选中时使用主题色背景</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CSS修复代码 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-code me-2"></i>
                            CSS修复代码
                        </h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-dark p-3 rounded"><code style="color: #e2e8f0;">/* 修复下拉框选项的字体颜色 */
.form-select option {
    background: #1e293b;
    color: white;
    padding: 8px 12px;
}

.form-select option:hover {
    background: #334155;
}

.form-select option:checked {
    background: var(--primary);
    color: white;
}</code></pre>
                    </div>
                </div>

                <!-- 新增资产类别说明 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            新增资产类别
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">原有资产类别：</h6>
                                <ul>
                                    <li>股票 (stocks)</li>
                                    <li>数字货币 (crypto)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">新增资产类别：</h6>
                                <ul>
                                    <li>期货 (futures)</li>
                                    <li>黄金 (gold)</li>
                                    <li>外汇 (forex)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6 class="text-info">对应策略模板：</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="small">
                                        <li><strong>数字货币网格</strong> - 适用于数字货币的网格交易</li>
                                        <li><strong>期货动量策略</strong> - 期货市场动量追踪策略</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="small">
                                        <li><strong>黄金对冲策略</strong> - 黄金避险和套利策略</li>
                                        <li><strong>外汇套息策略</strong> - 外汇利差交易策略</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 返回按钮 -->
                <div class="text-center mt-4">
                    <a href="/strategy-editor" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        返回策略编辑器
                    </a>
                    <a href="/" class="btn btn-outline-light btn-lg ms-3">
                        <i class="fas fa-home me-2"></i>
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试下拉框功能
        document.addEventListener('DOMContentLoaded', function() {
            const selects = document.querySelectorAll('.form-select');
            
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    console.log(`选择了: ${this.value}`);
                    
                    // 显示选择结果
                    if (this.value) {
                        const selectedText = this.options[this.selectedIndex].text;
                        showNotification(`已选择: ${selectedText}`, 'success');
                    }
                });
            });
        });

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
