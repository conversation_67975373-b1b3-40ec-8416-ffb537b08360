#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
前端功能和WebSocket测试脚本
"""

import requests
import json
import time
import socketio
from datetime import datetime

class FrontendTester:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_static_files(self):
        """测试静态文件加载"""
        print("📁 测试静态文件加载")
        print("-" * 30)
        
        static_files = [
            "/static/css/style.css",
            "/static/js/main.js",
            "/static/js/auth.js",
            "/static/js/dashboard.js",
            "/static/js/realtime.js"
        ]
        
        for file_path in static_files:
            try:
                response = self.session.get(f"{self.base_url}{file_path}")
                if response.status_code == 200:
                    print(f"✅ {file_path}: 加载成功")
                else:
                    print(f"❌ {file_path}: 状态码 {response.status_code}")
            except Exception as e:
                print(f"❌ {file_path}: 异常 {str(e)}")
    
    def test_page_content(self):
        """测试页面内容完整性"""
        print("\n📄 测试页面内容完整性")
        print("-" * 30)
        
        pages_content = {
            "/": ["QuantTradeX", "量化交易", "策略开发"],
            "/strategies": ["策略市场", "策略列表", "搜索"],
            "/backtest": ["回测系统", "历史数据", "策略测试"],
            "/forum": ["社区论坛", "讨论", "帖子"],
            "/datacenter": ["数据中心", "市场数据", "实时行情"],
            "/realtime": ["实时数据", "WebSocket", "订阅"]
        }
        
        for path, expected_content in pages_content.items():
            try:
                response = self.session.get(f"{self.base_url}{path}")
                if response.status_code == 200:
                    content = response.text
                    missing_content = []
                    for expected in expected_content:
                        if expected not in content:
                            missing_content.append(expected)
                    
                    if missing_content:
                        print(f"⚠️ {path}: 缺少内容 {missing_content}")
                    else:
                        print(f"✅ {path}: 内容完整")
                else:
                    print(f"❌ {path}: 状态码 {response.status_code}")
            except Exception as e:
                print(f"❌ {path}: 异常 {str(e)}")
    
    def test_api_data_quality(self):
        """测试API数据质量"""
        print("\n📊 测试API数据质量")
        print("-" * 30)
        
        # 测试论坛帖子数据
        try:
            response = self.session.get(f"{self.base_url}/api/forum/posts")
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('posts'):
                    posts = data['posts']
                    print(f"✅ 论坛帖子: {len(posts)}个帖子")
                    
                    # 检查帖子数据结构
                    if posts:
                        first_post = posts[0]
                        required_fields = ['id', 'title', 'content', 'author', 'created_at']
                        missing_fields = [field for field in required_fields if field not in first_post]
                        if missing_fields:
                            print(f"⚠️ 帖子数据缺少字段: {missing_fields}")
                        else:
                            print("✅ 帖子数据结构完整")
                else:
                    print("❌ 论坛帖子: 数据为空")
            else:
                print(f"❌ 论坛帖子: 状态码 {response.status_code}")
        except Exception as e:
            print(f"❌ 论坛帖子: 异常 {str(e)}")
        
        # 测试策略数据
        try:
            response = self.session.get(f"{self.base_url}/api/strategies/1")
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('strategy'):
                    strategy = data['strategy']
                    required_fields = ['id', 'name', 'description', 'code']
                    missing_fields = [field for field in required_fields if field not in strategy]
                    if missing_fields:
                        print(f"⚠️ 策略数据缺少字段: {missing_fields}")
                    else:
                        print("✅ 策略数据结构完整")
                else:
                    print("❌ 策略数据: 数据为空")
            else:
                print(f"❌ 策略数据: 状态码 {response.status_code}")
        except Exception as e:
            print(f"❌ 策略数据: 异常 {str(e)}")
        
        # 测试股票数据
        try:
            response = self.session.get(f"{self.base_url}/api/stock/AAPL")
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    stock_data = data['data']
                    required_fields = ['symbol', 'dates', 'close', 'volume']
                    missing_fields = [field for field in required_fields if field not in stock_data]
                    if missing_fields:
                        print(f"⚠️ 股票数据缺少字段: {missing_fields}")
                    else:
                        print("✅ 股票数据结构完整")
                        print(f"   数据点数量: {len(stock_data.get('dates', []))}")
                else:
                    print("❌ 股票数据: 数据为空")
            else:
                print(f"❌ 股票数据: 状态码 {response.status_code}")
        except Exception as e:
            print(f"❌ 股票数据: 异常 {str(e)}")
    
    def test_websocket_connection(self):
        """测试WebSocket连接"""
        print("\n🔌 测试WebSocket连接")
        print("-" * 30)
        
        try:
            # 创建SocketIO客户端
            sio = socketio.SimpleClient()
            
            # 连接到服务器
            sio.connect(self.base_url)
            print("✅ WebSocket连接成功")
            
            # 测试ping
            sio.emit('ping')
            event = sio.receive(timeout=5)
            if event[0] == 'pong':
                print("✅ WebSocket ping/pong正常")
            else:
                print("⚠️ WebSocket ping/pong异常")
            
            # 测试订阅
            sio.emit('subscribe', {'symbol': 'AAPL', 'type': 'stock'})
            
            # 等待订阅确认
            try:
                event = sio.receive(timeout=5)
                if event[0] == 'subscribed':
                    print("✅ WebSocket订阅功能正常")
                else:
                    print(f"⚠️ WebSocket订阅响应异常: {event}")
            except Exception:
                print("⚠️ WebSocket订阅超时")
            
            # 断开连接
            sio.disconnect()
            print("✅ WebSocket断开连接成功")
            
        except Exception as e:
            print(f"❌ WebSocket连接失败: {str(e)}")
    
    def test_user_workflow(self):
        """测试完整用户工作流"""
        print("\n👤 测试用户工作流")
        print("-" * 30)
        
        # 创建测试用户
        test_username = f"test_user_{int(time.time())}"
        test_email = f"test_{int(time.time())}@example.com"
        
        # 1. 注册
        register_data = {
            "username": test_username,
            "email": test_email,
            "password": "test123456",
            "full_name": "测试用户"
        }
        
        try:
            response = self.session.post(f"{self.base_url}/auth/register", json=register_data)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 用户注册成功")
                else:
                    print(f"❌ 用户注册失败: {data.get('error')}")
                    return
            else:
                print(f"❌ 用户注册失败: 状态码 {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 用户注册异常: {str(e)}")
            return
        
        # 2. 登录
        login_data = {
            "username": test_username,
            "password": "test123456"
        }
        
        try:
            response = self.session.post(f"{self.base_url}/auth/login", json=login_data)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 用户登录成功")
                else:
                    print(f"❌ 用户登录失败: {data.get('error')}")
                    return
            else:
                print(f"❌ 用户登录失败: 状态码 {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 用户登录异常: {str(e)}")
            return
        
        # 3. 检查登录状态
        try:
            response = self.session.get(f"{self.base_url}/auth/check")
            if response.status_code == 200:
                data = response.json()
                if data.get('authenticated'):
                    print("✅ 登录状态验证成功")
                else:
                    print("❌ 登录状态验证失败")
            else:
                print(f"❌ 登录状态检查失败: 状态码 {response.status_code}")
        except Exception as e:
            print(f"❌ 登录状态检查异常: {str(e)}")
        
        # 4. 登出
        try:
            response = self.session.post(f"{self.base_url}/auth/logout")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 用户登出成功")
                else:
                    print(f"❌ 用户登出失败: {data.get('error')}")
            else:
                print(f"❌ 用户登出失败: 状态码 {response.status_code}")
        except Exception as e:
            print(f"❌ 用户登出异常: {str(e)}")
    
    def run_frontend_tests(self):
        """运行前端测试"""
        print("🧪 开始前端功能测试")
        print("=" * 50)
        
        self.test_static_files()
        self.test_page_content()
        self.test_api_data_quality()
        self.test_websocket_connection()
        self.test_user_workflow()
        
        print("\n" + "=" * 50)
        print("🎉 前端功能测试完成")

if __name__ == "__main__":
    tester = FrontendTester()
    tester.run_frontend_tests()
