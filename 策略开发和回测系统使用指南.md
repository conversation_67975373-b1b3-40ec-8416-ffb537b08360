# QuantTradeX 策略开发和回测系统使用指南

## 📋 功能访问概览

### 🔓 公开访问功能
- **首页** (`/`) - 平台介绍和功能展示
- **策略市场** (`/strategies`) - 浏览和下载策略
- **社区论坛** (`/forum`) - 查看讨论和帖子

### 🔐 需要登录的功能
- **策略开发** (`/strategy-editor`) - 在线代码编辑器
- **回测系统** (`/backtest`) - 策略回测和分析
- **交易仪表板** (`/dashboard`) - 个人交易面板

## 🚪 策略开发入口位置

### 1. 主导航栏（登录后显示）
```
首页 | 仪表板 | 策略市场 | [策略开发] | 回测系统 | 社区论坛
```
- 位置：主导航栏第4个位置
- 显示条件：用户登录后自动显示
- 图标：`<i class="fas fa-code"></i>`

### 2. 用户下拉菜单
```
用户名 ▼
├── 仪表板
├── [策略开发]  ← 这里
├── 个人资料
├── 我的策略
├── 升级VIP (非VIP用户)
└── 退出登录
```

### 3. 首页快速入口（登录后显示）
```
快速体验区:
[交易仪表板] [策略市场] [策略开发] [回测分析]
```

### 4. 策略市场页面
- **创建策略按钮** - 右上角和筛选区域
- **用户菜单** - 策略开发选项

## 🔑 测试账号

### 普通用户
- **用户名**: `demo_user`
- **密码**: `password123`
- **权限**: 基础功能访问

### VIP用户
- **用户名**: `premium_user`
- **密码**: `premium123`
- **权限**: 全功能访问

## 📝 使用步骤

### 第一步：登录系统
1. 访问任意页面
2. 点击右上角"登录"按钮
3. 输入测试账号信息
4. 登录成功后导航栏会更新

### 第二步：访问策略开发
**方式一：主导航栏**
```
点击导航栏中的 "策略开发" 链接
```

**方式二：用户菜单**
```
点击用户名 → 选择 "策略开发"
```

**方式三：直接访问**
```
浏览器地址栏输入: /strategy-editor
```

### 第三步：使用策略编辑器
1. 在线代码编辑器界面
2. 选择策略模板或从头编写
3. 实时语法检查和提示
4. 保存和测试策略

### 第四步：回测策略
**方式一：策略编辑器内**
```
编写完策略 → 点击 "回测" 按钮
```

**方式二：回测系统页面**
```
导航栏 "回测系统" → 选择策略 → 设置参数 → 运行回测
```

## 🎯 功能特色

### 策略开发器特点
- **在线编辑器**: Monaco Editor (VS Code内核)
- **语法高亮**: Python/JavaScript支持
- **实时检查**: 语法错误提示
- **模板库**: 预置策略模板
- **调试工具**: 断点和变量查看

### 回测系统特点
- **历史数据**: 多年历史行情数据
- **性能分析**: 收益率、夏普比率、最大回撤
- **可视化**: 收益曲线、持仓分析
- **参数优化**: 自动寻找最优参数
- **风险评估**: VaR、压力测试

## 🔧 技术架构

### 前端技术
- **编辑器**: Monaco Editor
- **图表**: Chart.js / ECharts
- **UI框架**: Bootstrap 5
- **响应式**: 支持移动端

### 后端技术
- **框架**: Flask (Python)
- **数据**: 模拟数据 + API集成
- **认证**: Session管理
- **API**: RESTful接口

## 📊 数据支持

### 支持的资产类型
- **股票**: A股、美股、港股
- **期货**: 商品期货、金融期货
- **外汇**: 主要货币对
- **数字货币**: BTC、ETH等

### 数据提供商
- **Alpha Vantage**: 股票数据
- **Yahoo Finance**: 免费行情
- **Quandl**: 期货数据
- **CoinGecko**: 数字货币

## 🚀 快速开始

### 1. 打开演示页面
```
访问: demo_login.html
```

### 2. 快速登录
```
点击测试用户卡片即可一键登录
```

### 3. 体验功能
```
登录后所有受限功能将自动解锁
```

## 📞 技术支持

### 问题反馈
- **论坛**: 社区论坛发帖
- **邮箱**: <EMAIL>
- **文档**: 查看API文档

### 常见问题
1. **Q**: 为什么看不到策略开发入口？
   **A**: 需要先登录，登录后导航栏会自动显示

2. **Q**: 测试账号无法登录？
   **A**: 确保使用正确的用户名和密码，检查网络连接

3. **Q**: 策略编辑器无法加载？
   **A**: 检查浏览器兼容性，建议使用Chrome或Firefox

4. **Q**: 回测结果不准确？
   **A**: 当前使用模拟数据，实际部署需要接入真实数据源

## 🎉 总结

QuantTradeX提供了完整的量化交易开发环境：

✅ **用户友好**: 直观的界面设计  
✅ **功能完整**: 从策略开发到回测分析  
✅ **权限管理**: 基于登录状态的功能控制  
✅ **响应式**: 支持各种设备访问  
✅ **可扩展**: 模块化架构便于功能扩展  

**立即体验**: 使用测试账号登录，探索专业的量化交易平台！
