#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终验证测试脚本
验证所有修复是否成功
"""

import requests
import time

def verify_fixes():
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 QuantTradeX 最终验证测试")
    print("=" * 50)
    
    results = {
        'passed': 0,
        'failed': 0,
        'total': 0
    }
    
    def test_item(name, test_func):
        results['total'] += 1
        try:
            if test_func():
                print(f"✅ {name}")
                results['passed'] += 1
                return True
            else:
                print(f"❌ {name}")
                results['failed'] += 1
                return False
        except Exception as e:
            print(f"❌ {name} - 异常: {e}")
            results['failed'] += 1
            return False
    
    # 1. 页面访问测试
    print("\n📄 页面访问测试")
    print("-" * 30)
    
    pages = [
        ("/", "主页"),
        ("/strategies", "策略市场"),
        ("/backtest", "回测系统"),
        ("/forum", "论坛"),
        ("/datacenter", "数据中心"),
        ("/realtime", "实时数据"),
        ("/strategy-editor", "策略编辑器"),
        ("/security", "安全设置"),
        ("/advanced_backtest", "高级回测")
    ]
    
    for path, name in pages:
        test_item(f"{name}页面", lambda p=path: requests.get(f"{base_url}{p}", timeout=5).status_code == 200)
    
    # 2. API接口测试
    print("\n🔌 API接口测试")
    print("-" * 30)
    
    def test_auth_check():
        response = requests.get(f"{base_url}/auth/check", timeout=5)
        return response.status_code == 200 and response.json().get('success')
    
    def test_user_status():
        response = requests.get(f"{base_url}/auth/status", timeout=5)
        return response.status_code == 200 and response.json().get('success') is not None
    
    def test_forum_posts():
        response = requests.get(f"{base_url}/api/forum/posts", timeout=5)
        data = response.json()
        return response.status_code == 200 and data.get('success') and len(data.get('posts', [])) > 0
    
    def test_strategy_detail():
        response = requests.get(f"{base_url}/api/strategies/1", timeout=5)
        data = response.json()
        return response.status_code == 200 and data.get('success') and data.get('strategy')
    
    def test_stock_data():
        response = requests.get(f"{base_url}/api/stock/AAPL", timeout=10)
        data = response.json()
        return (response.status_code == 200 and 
                data.get('success') and 
                data.get('data') and 
                len(data['data'].get('dates', [])) > 0)
    
    def test_crypto_data():
        response = requests.get(f"{base_url}/api/realtime/crypto/BTC", timeout=5)
        data = response.json()
        return response.status_code == 200 and data.get('success') and data.get('data')
    
    def test_watchlist():
        response = requests.get(f"{base_url}/api/watchlist", timeout=5)
        data = response.json()
        return response.status_code == 200 and data.get('success') and data.get('watchlist')
    
    test_item("认证状态检查", test_auth_check)
    test_item("用户状态API", test_user_status)
    test_item("论坛帖子API", test_forum_posts)
    test_item("策略详情API", test_strategy_detail)
    test_item("股票数据API", test_stock_data)
    test_item("加密货币API", test_crypto_data)
    test_item("关注列表API", test_watchlist)
    
    # 3. 用户功能测试
    print("\n👤 用户功能测试")
    print("-" * 30)
    
    def test_user_registration():
        test_user = {
            "username": f"test_{int(time.time())}",
            "email": f"test_{int(time.time())}@example.com",
            "password": "test123456",
            "full_name": "测试用户"
        }
        response = requests.post(f"{base_url}/auth/register", json=test_user, timeout=5)
        data = response.json()
        return response.status_code == 200 and data.get('success')
    
    test_item("用户注册功能", test_user_registration)
    
    # 4. 静态文件测试
    print("\n📁 静态文件测试")
    print("-" * 30)
    
    def test_css_file():
        response = requests.get(f"{base_url}/static/css/style.css", timeout=5)
        return response.status_code == 200 and 'QuantTradeX' in response.text
    
    def test_js_file():
        response = requests.get(f"{base_url}/static/js/main.js", timeout=5)
        return response.status_code == 200 and 'QuantTradeX' in response.text
    
    test_item("CSS样式文件", test_css_file)
    test_item("JavaScript文件", test_js_file)
    
    # 生成报告
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    success_rate = (results['passed'] / results['total']) * 100 if results['total'] > 0 else 0
    
    print(f"总测试项目: {results['total']}")
    print(f"✅ 通过: {results['passed']}")
    print(f"❌ 失败: {results['failed']}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 优秀！网站功能基本完整，可以正常使用")
    elif success_rate >= 80:
        print("\n👍 良好！大部分功能正常，少数问题需要关注")
    elif success_rate >= 70:
        print("\n⚠️ 一般！部分功能存在问题，需要进一步修复")
    else:
        print("\n❌ 需要改进！存在较多问题，建议全面检查")
    
    return success_rate

if __name__ == "__main__":
    verify_fixes()
