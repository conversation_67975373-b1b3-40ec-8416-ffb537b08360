#!/bin/bash

# QuantTradeX Web应用启动脚本

cd /www/wwwroot/qclb.com

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 启动 QuantTradeX Web应用"
echo "=================================="

# 1. 检查Python环境
log_info "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    log_error "Python3 未安装"
    exit 1
fi

# 2. 创建虚拟环境
if [ ! -d "venv" ]; then
    log_info "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 3. 激活虚拟环境
log_info "激活虚拟环境..."
source venv/bin/activate

# 4. 安装依赖
log_info "安装Python依赖..."
pip install --upgrade pip
pip install -r requirements.txt

# 5. 检查数据库连接
log_info "检查数据库连接..."
python3 -c "
import psycopg2
try:
    conn = psycopg2.connect(
        host='localhost',
        port=5432,
        database='quanttradex',
        user='quanttradex_user',
        password='quanttradex123'
    )
    conn.close()
    print('✅ PostgreSQL连接成功')
except Exception as e:
    print(f'❌ PostgreSQL连接失败: {e}')
"

# 6. 检查Redis连接
log_info "检查Redis连接..."
python3 -c "
import redis
try:
    r = redis.Redis(host='localhost', port=6379, db=0)
    r.ping()
    print('✅ Redis连接成功')
except Exception as e:
    print(f'❌ Redis连接失败: {e}')
"

# 7. 设置权限
log_info "设置文件权限..."
chown -R www:www /www/wwwroot/qclb.com
chmod +x app.py

# 8. 启动应用
log_info "启动Flask应用..."
echo "应用将在 http://www.qclb.com:5000 运行"
echo "按 Ctrl+C 停止应用"
echo "=================================="

# 使用gunicorn启动（生产环境）
if [ "$1" = "production" ]; then
    log_info "生产模式启动..."
    gunicorn -w 4 -b 0.0.0.0:5000 app:app
else
    log_info "开发模式启动..."
    python3 app.py
fi
