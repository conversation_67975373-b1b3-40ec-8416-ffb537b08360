<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框样式测试 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            padding: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
        }

        /* 模态框样式修复 */
        .modal-content {
            background: var(--dark-surface) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: 16px !important;
            color: var(--text-primary) !important;
            backdrop-filter: blur(20px) !important;
        }

        .modal-header {
            border-bottom: 1px solid var(--glass-border);
            background: rgba(99, 102, 241, 0.1);
        }

        .modal-title {
            color: var(--text-primary) !important;
            font-weight: 600;
        }

        .modal-body {
            color: var(--text-primary) !important;
        }

        .modal-body h6 {
            color: var(--primary);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .modal-body p, .modal-body li {
            color: var(--text-secondary);
        }

        .modal-body strong {
            color: var(--text-primary);
        }

        .modal-footer {
            border-top: 1px solid var(--glass-border);
            background: rgba(0, 0, 0, 0.1);
        }

        /* 代码块样式 */
        .modal-body pre {
            background: var(--dark) !important;
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            padding: 1rem;
            color: var(--text-primary) !important;
            max-height: 300px;
            overflow-y: auto;
        }

        .modal-body code {
            color: var(--text-primary) !important;
            background: transparent !important;
        }

        /* 表单元素在模态框中的样式 */
        .modal-body .form-control,
        .modal-body .form-select {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            color: var(--text-primary);
        }

        .modal-body .form-control:focus,
        .modal-body .form-select:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .modal-body .form-label {
            color: var(--text-primary);
            font-weight: 500;
        }

        .modal-body .form-check-label {
            color: var(--text-secondary);
        }

        /* 关闭按钮样式 */
        .btn-close {
            filter: invert(1);
            opacity: 0.8;
        }

        .btn-close:hover {
            opacity: 1;
        }

        /* 修复模态框中的下拉框选项颜色 */
        .modal-body .form-select option {
            background: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background: var(--primary) !important;
            color: white !important;
        }

        /* 徽章样式在模态框中 */
        .modal-body .badge {
            background: var(--primary) !important;
            color: white;
        }

        .modal-body .badge.bg-secondary {
            background: var(--dark-card) !important;
            color: var(--text-secondary);
        }

        .modal-body .badge.bg-success {
            background: var(--success) !important;
            color: white;
        }

        /* 列表样式 */
        .modal-body .list-unstyled li {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .comparison-box {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .before-fix {
            background: rgba(255, 0, 0, 0.1);
            border-color: rgba(255, 0, 0, 0.3);
        }

        .after-fix {
            background: rgba(0, 255, 0, 0.1);
            border-color: rgba(0, 255, 0, 0.3);
        }
    
        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-4">
                    <h1 class="display-5 mb-3">
                        <i class="fas fa-window-restore me-3"></i>
                        模态框样式修复测试
                    </h1>
                    <p class="lead">验证策略市场中模态框的背景和文字对比度修复效果</p>
                </div>

                <!-- 测试按钮 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-play-circle text-success me-2"></i>
                            测试模态框
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <button class="btn btn-primary btn-lg me-3" data-bs-toggle="modal" data-bs-target="#strategyModal">
                            <i class="fas fa-eye me-2"></i>策略详情模态框
                        </button>
                        <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#createStrategyModal">
                            <i class="fas fa-plus me-2"></i>创建策略模态框
                        </button>
                    </div>
                </div>

                <!-- 修复说明 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            修复说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="comparison-box before-fix">
                                    <h6 class="text-danger">
                                        <i class="fas fa-times-circle me-2"></i>修复前问题
                                    </h6>
                                    <ul class="mb-0">
                                        <li>模态框背景为白色半透明</li>
                                        <li>文字颜色对比度不够</li>
                                        <li>代码块难以阅读</li>
                                        <li>表单元素不清晰</li>
                                        <li>下拉框选项不可见</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="comparison-box after-fix">
                                    <h6 class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>修复后效果
                                    </h6>
                                    <ul class="mb-0">
                                        <li>使用深色背景主题</li>
                                        <li>高对比度文字颜色</li>
                                        <li>深色代码块样式</li>
                                        <li>清晰的表单元素</li>
                                        <li>可见的下拉框选项</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 返回按钮 -->
                <div class="text-center">
                    <a href="/strategies" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        返回策略市场
                    </a>
                    <a href="/" class="btn btn-outline-light btn-lg ms-3">
                        <i class="fas fa-home me-2"></i>
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 策略详情模态框 -->
    <div class="modal fade" id="strategyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">双均线交叉策略</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>策略描述</h6>
                            <p>基于20日和50日移动平均线的经典交叉策略，当短期均线上穿长期均线时买入，下穿时卖出。</p>

                            <h6>策略代码</h6>
                            <pre><code class="language-python">def strategy(data):
    # 计算移动平均线
    sma_20 = data['close'].rolling(20).mean()
    sma_50 = data['close'].rolling(50).mean()
    
    # 生成交易信号
    signals = []
    for i in range(len(data)):
        if sma_20.iloc[i] > sma_50.iloc[i]:
            signals.append('buy')
        else:
            signals.append('sell')
    
    return signals</code></pre>
                        </div>
                        <div class="col-md-4">
                            <h6>策略信息</h6>
                            <ul class="list-unstyled">
                                <li><strong>分类:</strong> 趋势跟踪</li>
                                <li><strong>作者:</strong> AlgoTrader</li>
                                <li><strong>评分:</strong> ⭐⭐⭐⭐⭐ (4.5)</li>
                                <li><strong>下载量:</strong> 1,234</li>
                                <li><strong>价格:</strong> 免费</li>
                                <li><strong>创建时间:</strong> 2025/1/15</li>
                            </ul>

                            <h6>标签</h6>
                            <div>
                                <span class="badge bg-secondary me-1">股票</span>
                                <span class="badge bg-secondary me-1">技术分析</span>
                                <span class="badge bg-secondary me-1">趋势</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary">下载策略</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建策略模态框 -->
    <div class="modal fade" id="createStrategyModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新策略</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">策略名称</label>
                                    <input type="text" class="form-control" placeholder="输入策略名称">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">分类</label>
                                    <select class="form-select">
                                        <option value="">选择分类</option>
                                        <option value="trend">趋势跟踪</option>
                                        <option value="mean_reversion">均值回归</option>
                                        <option value="arbitrage">套利策略</option>
                                        <option value="momentum">动量策略</option>
                                        <option value="volatility">波动率策略</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">策略描述</label>
                            <textarea class="form-control" rows="3" placeholder="描述您的策略..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">策略代码</label>
                            <textarea class="form-control" rows="8" placeholder="# 请输入Python策略代码
def strategy(data):
    # 您的策略逻辑
    return signals"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">标签 (用逗号分隔)</label>
                                    <input type="text" class="form-control" placeholder="例如: 股票, 短线, 技术分析">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isPublic">
                                        <label class="form-check-label" for="isPublic">
                                            公开策略
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isPremium">
                                        <label class="form-check-label" for="isPremium">
                                            付费策略
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary">创建策略</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // 高亮代码
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>
