# QuantTradeX 项目依赖

# Web框架
Flask==2.3.3
Flask-SQLAlchemy>=3.0.5
Flask-Migrate>=4.0.4
Flask-Login>=0.6.2
Flask-WTF>=1.1.1
Flask-SocketIO>=5.3.4
Flask-CORS>=4.0.0
Werkzeug>=2.3.7

# HTTP请求和网络
requests==2.31.0
urllib3>=2.0.7

# 数据处理
pandas==2.0.3
numpy==1.24.3

# 金融数据和技术分析
yfinance==0.2.18
TA-Lib==0.4.25
pandas-ta>=0.3.14b0

# 缓存和数据库
redis==4.6.0
psycopg2-binary==2.9.7

# 环境变量和配置
python-dotenv==1.0.0

# 部署和服务器
gunicorn==21.2.0
eventlet>=0.33.3
waitress>=2.1.2

# 表单处理
WTForms>=3.0.1

# 安全和认证
bcrypt>=4.1.2
cryptography>=41.0.7
pyotp>=2.8.0
qrcode[pil]>=7.4.2

# 实时数据流
flask-socketio>=5.3.6
python-socketio>=5.8.0
eventlet>=0.33.3
websocket-client>=1.6.1

# 数学和科学计算
scipy>=1.11.4

# 可视化 (可选)
matplotlib>=3.8.2
plotly>=5.17.0

# 开发工具 (可选)
pytest>=7.4.3
black>=23.11.0
flake8>=6.1.0

# API文档 (可选)
flasgger>=*******

# 实盘交易API (高级功能)
ib-insync>=0.9.86

# 机器学习 (可选)
scikit-learn>=1.3.2

# 邮件发送 (可选)
Flask-Mail>=0.9.1

# 任务队列 (可选)
celery>=5.3.4

# 时区处理
pytz>=2023.3.post1

# 文件处理
openpyxl>=3.1.2

# 系统监控
psutil>=5.9.6

# API限流
Flask-Limiter>=3.5.0

# 压缩
Flask-Compress>=1.14

# 缓存增强
Flask-Caching>=2.1.0

# 表单验证
email-validator>=2.1.0.post1
