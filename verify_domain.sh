#!/bin/bash

# QuantTradeX 域名配置验证脚本
# 验证域名绑定和反向代理配置

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印带颜色的消息
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "✅ ${GREEN}$message${NC}"
    elif [ "$status" = "WARN" ]; then
        echo -e "⚠️  ${YELLOW}$message${NC}"
    elif [ "$status" = "ERROR" ]; then
        echo -e "❌ ${RED}$message${NC}"
    else
        echo -e "ℹ️  ${BLUE}$message${NC}"
    fi
}

echo "🌐 QuantTradeX 域名配置验证"
echo "=================================="

# 1. 检查nginx配置文件
if [ -f "/www/server/panel/vhost/nginx/www.gdpp.com.conf" ]; then
    print_status "OK" "nginx配置文件存在"
    
    # 检查配置内容
    if grep -q "proxy_pass http://127.0.0.1:5000" /www/server/panel/vhost/nginx/www.gdpp.com.conf; then
        print_status "OK" "反向代理配置正确"
    else
        print_status "ERROR" "反向代理配置缺失"
    fi
    
    if grep -q "server_name www.gdpp.com gdpp.com" /www/server/panel/vhost/nginx/www.gdpp.com.conf; then
        print_status "OK" "域名配置正确"
    else
        print_status "ERROR" "域名配置错误"
    fi
else
    print_status "ERROR" "nginx配置文件不存在"
fi

# 2. 检查nginx语法
echo ""
print_status "INFO" "检查nginx配置语法..."
if nginx -t 2>/dev/null; then
    print_status "OK" "nginx配置语法正确"
else
    print_status "ERROR" "nginx配置语法错误"
fi

# 3. 检查nginx服务状态
nginx_status=$(systemctl is-active nginx 2>/dev/null)
if [ "$nginx_status" = "active" ]; then
    print_status "OK" "nginx服务运行正常"
else
    print_status "ERROR" "nginx服务未运行 (状态: $nginx_status)"
fi

# 4. 检查Flask应用状态
flask_status=$(systemctl is-active quanttradex.service 2>/dev/null)
if [ "$flask_status" = "active" ]; then
    print_status "OK" "Flask应用运行正常"
else
    print_status "ERROR" "Flask应用未运行 (状态: $flask_status)"
fi

# 5. 检查端口监听
echo ""
print_status "INFO" "检查端口监听状态..."

# 检查80端口
port_80=$(netstat -tlnp 2>/dev/null | grep :80)
if [ -n "$port_80" ]; then
    print_status "OK" "端口80正在监听 (nginx)"
else
    print_status "ERROR" "端口80未监听"
fi

# 检查5000端口
port_5000=$(netstat -tlnp 2>/dev/null | grep :5000)
if [ -n "$port_5000" ]; then
    print_status "OK" "端口5000正在监听 (Flask)"
else
    print_status "ERROR" "端口5000未监听"
fi

# 6. 测试域名访问
echo ""
print_status "INFO" "测试域名访问..."

# 测试 www.gdpp.com
www_response=$(curl -s -o /dev/null -w "%{http_code}" http://www.gdpp.com 2>/dev/null)
if [ "$www_response" = "200" ]; then
    print_status "OK" "www.gdpp.com 访问正常 (HTTP 200)"
else
    print_status "ERROR" "www.gdpp.com 访问失败 (HTTP $www_response)"
fi

# 测试 gdpp.com
root_response=$(curl -s -o /dev/null -w "%{http_code}" http://gdpp.com 2>/dev/null)
if [ "$root_response" = "200" ]; then
    print_status "OK" "gdpp.com 访问正常 (HTTP 200)"
else
    print_status "ERROR" "gdpp.com 访问失败 (HTTP $root_response)"
fi

# 7. 检查响应头
echo ""
print_status "INFO" "检查响应头信息..."
response_headers=$(curl -s -I http://www.gdpp.com 2>/dev/null)
if echo "$response_headers" | grep -q "Server: nginx"; then
    print_status "OK" "nginx反向代理工作正常"
else
    print_status "WARN" "未检测到nginx服务器标识"
fi

# 8. 检查静态文件访问
echo ""
print_status "INFO" "测试静态文件访问..."
static_response=$(curl -s -o /dev/null -w "%{http_code}" http://www.gdpp.com/static/css/style.css 2>/dev/null)
if [ "$static_response" = "200" ] || [ "$static_response" = "304" ]; then
    print_status "OK" "静态文件访问正常"
else
    print_status "WARN" "静态文件访问可能有问题 (HTTP $static_response)"
fi

# 9. 检查WebSocket支持
echo ""
print_status "INFO" "检查WebSocket配置..."
if grep -q "proxy_set_header Upgrade" /www/server/panel/vhost/nginx/www.gdpp.com.conf; then
    print_status "OK" "WebSocket支持已配置"
else
    print_status "WARN" "WebSocket支持未配置"
fi

# 10. 检查日志文件
echo ""
print_status "INFO" "检查日志文件..."
if [ -f "/www/wwwlogs/www.gdpp.com.log" ]; then
    print_status "OK" "访问日志文件存在"
    log_size=$(du -h /www/wwwlogs/www.gdpp.com.log | cut -f1)
    print_status "INFO" "访问日志大小: $log_size"
else
    print_status "WARN" "访问日志文件不存在"
fi

if [ -f "/www/wwwlogs/www.gdpp.com.error.log" ]; then
    print_status "OK" "错误日志文件存在"
    error_log_size=$(du -h /www/wwwlogs/www.gdpp.com.error.log | cut -f1)
    print_status "INFO" "错误日志大小: $error_log_size"
else
    print_status "WARN" "错误日志文件不存在"
fi

# 总结
echo ""
echo "=================================="
print_status "INFO" "域名配置验证完成！"

echo ""
echo "🌐 访问地址:"
echo "   主域名: http://www.gdpp.com"
echo "   备用域名: http://gdpp.com"
echo ""
echo "📊 服务状态:"
echo "   nginx: $nginx_status"
echo "   Flask应用: $flask_status"
echo ""
echo "🔧 管理命令:"
echo "   重启nginx: sudo nginx -s reload"
echo "   重启应用: ./manage_service.sh restart"
echo "   查看日志: tail -f /www/wwwlogs/www.gdpp.com.log"
echo "   查看错误: tail -f /www/wwwlogs/www.gdpp.com.error.log"

echo ""
