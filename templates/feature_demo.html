<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新功能演示 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px);
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
        }

        .feature-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .text-white {
            color: white !important;
        }

        .text-muted {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        .btn-demo {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
        }

        .btn-demo:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
        }

        .api-status {
            padding: 10px;
            border-radius: 8px;
            margin: 5px 0;
        }

        .api-active {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
        }

        .api-inactive {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.5);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1 class="text-white mb-3">
                    <i class="fas fa-rocket me-2"></i>第一优先级功能演示
                </h1>
                <p class="text-muted">展示最新开发的核心功能</p>
            </div>
        </div>

        <!-- 功能概览 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card feature-card position-relative" onclick="showFeature('watchlist')">
                    <span class="badge bg-success status-badge">✅ 已完成</span>
                    <div class="card-body text-center">
                        <i class="fas fa-star fa-3x text-warning mb-3"></i>
                        <h5 class="text-white">关注列表功能</h5>
                        <p class="text-muted">完善的数据中心模块，支持多种资产类型的关注和管理</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card feature-card position-relative" onclick="showFeature('api')">
                    <span class="badge bg-success status-badge">✅ 已完成</span>
                    <div class="card-body text-center">
                        <i class="fas fa-plug fa-3x text-info mb-3"></i>
                        <h5 class="text-white">API数据源集成</h5>
                        <p class="text-muted">统一的API管理系统，支持多个数据提供商</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card feature-card position-relative" onclick="showFeature('payment')">
                    <span class="badge bg-success status-badge">✅ 已完成</span>
                    <div class="card-body text-center">
                        <i class="fas fa-credit-card fa-3x text-success mb-3"></i>
                        <h5 class="text-white">支付系统基础版</h5>
                        <p class="text-muted">VIP升级支付功能，支持多种支付方式</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能演示区域 -->
        <div id="demoArea" class="demo-section" style="display: none;">
            <div id="demoContent"></div>
        </div>

        <!-- 关注列表演示 -->
        <div id="watchlistDemo" style="display: none;">
            <h4 class="text-white mb-3">
                <i class="fas fa-star me-2"></i>关注列表功能演示
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-white">功能特性：</h6>
                    <ul class="text-muted">
                        <li>支持股票、加密货币、外汇、期货、黄金等多种资产类型</li>
                        <li>实时价格更新和涨跌幅显示</li>
                        <li>个性化关注列表管理</li>
                        <li>统计信息和数据分析</li>
                        <li>一键添加/移除关注品种</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-white">演示操作：</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-demo" onclick="window.open('/datacenter', '_blank')">
                            <i class="fas fa-external-link-alt me-2"></i>打开数据中心
                        </button>
                        <button class="btn btn-outline-light" onclick="testWatchlistAPI()">
                            <i class="fas fa-flask me-2"></i>测试API接口
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- API集成演示 -->
        <div id="apiDemo" style="display: none;">
            <h4 class="text-white mb-3">
                <i class="fas fa-plug me-2"></i>API数据源集成演示
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-white">支持的数据提供商：</h6>
                    <div id="apiProviders">
                        <div class="api-status api-inactive">
                            <i class="fas fa-spinner fa-spin me-2"></i>正在加载提供商状态...
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="text-white">演示操作：</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-demo" onclick="loadAPIStatus()">
                            <i class="fas fa-sync me-2"></i>刷新API状态
                        </button>
                        <button class="btn btn-outline-light" onclick="testAPIConnection()">
                            <i class="fas fa-network-wired me-2"></i>测试连接
                        </button>
                        <button class="btn btn-outline-info" onclick="window.open('/static/API配置指南.md', '_blank')">
                            <i class="fas fa-book me-2"></i>配置指南
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支付系统演示 -->
        <div id="paymentDemo" style="display: none;">
            <h4 class="text-white mb-3">
                <i class="fas fa-credit-card me-2"></i>支付系统演示
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-white">支持的支付方式：</h6>
                    <div id="paymentMethods">
                        <div class="api-status api-inactive">
                            <i class="fas fa-spinner fa-spin me-2"></i>正在加载支付方式...
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="text-white">演示操作：</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-demo" onclick="loadPaymentMethods()">
                            <i class="fas fa-sync me-2"></i>刷新支付方式
                        </button>
                        <button class="btn btn-outline-light" onclick="testPayment()">
                            <i class="fas fa-coins me-2"></i>模拟支付测试
                        </button>
                        <button class="btn btn-outline-warning" onclick="showUpgradeModal()">
                            <i class="fas fa-crown me-2"></i>VIP升级演示
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="text-white mb-3">
                    <i class="fas fa-check-circle me-2"></i>开发完成总结
                </h5>
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-success">✅ 关注列表功能</h6>
                        <ul class="text-muted small">
                            <li>完整的数据中心页面</li>
                            <li>多资产类型支持</li>
                            <li>实时数据更新</li>
                            <li>用户个性化管理</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-success">✅ API数据源集成</h6>
                        <ul class="text-muted small">
                            <li>统一API管理框架</li>
                            <li>多提供商支持</li>
                            <li>自动故障转移</li>
                            <li>频率限制管理</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-success">✅ 支付系统基础版</h6>
                        <ul class="text-muted small">
                            <li>VIP升级支付流程</li>
                            <li>多支付方式支持</li>
                            <li>订单管理系统</li>
                            <li>安全支付处理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 显示功能演示
        function showFeature(feature) {
            // 隐藏所有演示
            document.querySelectorAll('[id$="Demo"]').forEach(el => el.style.display = 'none');
            
            // 显示选中的演示
            document.getElementById(feature + 'Demo').style.display = 'block';
            document.getElementById('demoArea').style.display = 'block';
            document.getElementById('demoContent').innerHTML = '';
            document.getElementById('demoContent').appendChild(document.getElementById(feature + 'Demo'));
            
            // 滚动到演示区域
            document.getElementById('demoArea').scrollIntoView({ behavior: 'smooth' });
            
            // 根据功能类型加载相应数据
            if (feature === 'api') {
                loadAPIStatus();
            } else if (feature === 'payment') {
                loadPaymentMethods();
            }
        }

        // 加载API状态
        async function loadAPIStatus() {
            try {
                const response = await fetch('/api/data-providers');
                const result = await response.json();
                
                const container = document.getElementById('apiProviders');
                if (result.success) {
                    container.innerHTML = '';
                    Object.entries(result.providers).forEach(([id, provider]) => {
                        const statusClass = provider.status === 'active' ? 'api-active' : 'api-inactive';
                        const icon = provider.status === 'active' ? 'fa-check-circle' : 'fa-times-circle';
                        const statusText = provider.status === 'active' ? '已配置' : '未配置';
                        
                        container.innerHTML += `
                            <div class="api-status ${statusClass}">
                                <i class="fas ${icon} me-2"></i>
                                <strong>${provider.name}</strong> - ${statusText}
                                <br><small>${provider.description}</small>
                                <br><small>支持: ${provider.supported_types.join(', ')}</small>
                            </div>
                        `;
                    });
                } else {
                    container.innerHTML = '<div class="api-status api-inactive">加载失败</div>';
                }
            } catch (error) {
                console.error('加载API状态失败:', error);
            }
        }

        // 加载支付方式
        async function loadPaymentMethods() {
            try {
                const response = await fetch('/api/payment/methods');
                const result = await response.json();
                
                const container = document.getElementById('paymentMethods');
                if (result.success) {
                    container.innerHTML = '';
                    result.methods.forEach(method => {
                        const statusClass = method.status === 'active' ? 'api-active' : 'api-inactive';
                        const icon = method.status === 'active' ? 'fa-check-circle' : 'fa-times-circle';
                        const statusText = method.status === 'active' ? '已配置' : '未配置';
                        
                        container.innerHTML += `
                            <div class="api-status ${statusClass}">
                                <i class="fas ${icon} me-2"></i>
                                <strong>${method.name}</strong> - ${statusText}
                            </div>
                        `;
                    });
                } else {
                    container.innerHTML = '<div class="api-status api-inactive">加载失败</div>';
                }
            } catch (error) {
                console.error('加载支付方式失败:', error);
            }
        }

        // 测试关注列表API
        async function testWatchlistAPI() {
            try {
                const response = await fetch('/api/watchlist');
                const result = await response.json();
                
                if (result.success) {
                    alert(`关注列表API测试成功！\n共有 ${result.data.length} 个关注品种`);
                } else {
                    alert(`API测试失败: ${result.error}`);
                }
            } catch (error) {
                alert('API测试失败: 网络错误');
            }
        }

        // 测试API连接
        async function testAPIConnection() {
            alert('正在测试API连接...\n这可能需要几秒钟时间');
            
            try {
                const response = await fetch('/api/data-providers/test/yahoo_finance');
                const result = await response.json();
                
                if (result.success) {
                    alert('API连接测试成功！\n所有测试项目都通过了');
                } else {
                    alert(`API连接测试失败: ${result.error}`);
                }
            } catch (error) {
                alert('API连接测试失败: 网络错误');
            }
        }

        // 测试支付
        function testPayment() {
            alert('支付测试功能\n\n这将创建一个测试订单并跳转到模拟支付页面');
            
            // 模拟创建订单
            fetch('/auth/upgrade', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    plan: 'monthly',
                    payment_method: 'mock'
                })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    window.open(result.payment_url, '_blank');
                } else {
                    alert(`创建测试订单失败: ${result.error}`);
                }
            })
            .catch(error => {
                alert('测试失败: 网络错误');
            });
        }

        // 显示升级模态框
        function showUpgradeModal() {
            alert('VIP升级演示\n\n这将打开主页的VIP升级功能');
            window.open('/', '_blank');
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里添加初始化代码
        });
    </script>
</body>
</html>
