<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级回测系统 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--primary) !important;
        }

        .container {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background: rgba(99, 102, 241, 0.1);
            border-bottom: 1px solid var(--glass-border);
            border-radius: 16px 16px 0 0 !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-select option {
            background: var(--dark-card);
            color: var(--text-primary);
        }

        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .metric-card {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            text-align: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
        }

        .metric-number {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .strategy-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .strategy-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .strategy-card.selected {
            border-color: var(--primary);
            background: rgba(99, 102, 241, 0.1);
        }

        .progress {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            height: 8px;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary), var(--accent));
            border-radius: 10px;
        }

        .positive {
            color: var(--success);
        }

        .negative {
            color: var(--danger);
        }

        .neutral {
            color: var(--warning);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 50%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .parameter-input {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .parameter-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.02);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .trade-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: var(--success);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: var(--danger);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            color: var(--warning);
        }

        .badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .badge-success {
            background: var(--success);
            color: white;
        }

        .badge-danger {
            background: var(--danger);
            color: white;
        }

        .badge-warning {
            background: var(--warning);
            color: white;
        }

        .badge-primary {
            background: var(--primary);
            color: white;
        }
    
        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-chart-bar me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/realtime">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>账户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                            <li><a class="dropdown-item" href="/security">安全设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- 页面标题 -->
        <div class="text-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-rocket me-3"></i>
                高级回测系统
            </h1>
            <p class="page-subtitle">专业级策略回测，多维度性能分析，精准风险评估</p>
        </div>

        <div class="row">
            <!-- 左侧：策略配置 -->
            <div class="col-lg-4">
                <!-- 基本配置 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-cog me-2"></i>基本配置
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="backtestForm">
                            <div class="mb-3">
                                <label class="form-label">回测名称</label>
                                <input type="text" class="form-control" id="backtestName"
                                       placeholder="输入回测名称" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">交易品种</label>
                                <input type="text" class="form-control" id="symbol"
                                       placeholder="例如: AAPL, GOOGL, TSLA" required>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">开始日期</label>
                                        <input type="date" class="form-control" id="startDate" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="endDate" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">初始资金</label>
                                <input type="number" class="form-control" id="initialCapital"
                                       value="100000" min="1000" step="1000" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">数据频率</label>
                                <select class="form-select" id="interval">
                                    <option value="1d">日线</option>
                                    <option value="1h">小时线</option>
                                    <option value="5m">5分钟线</option>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 策略选择 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-brain me-2"></i>策略选择
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="strategyCards">
                            <div class="strategy-card" data-strategy="moving_average_crossover">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">移动平均交叉</h6>
                                        <small class="text-muted">双均线金叉死叉策略</small>
                                    </div>
                                    <i class="fas fa-chart-line text-primary"></i>
                                </div>
                            </div>

                            <div class="strategy-card" data-strategy="rsi_mean_reversion">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">RSI均值回归</h6>
                                        <small class="text-muted">超买超卖反转策略</small>
                                    </div>
                                    <i class="fas fa-wave-square text-warning"></i>
                                </div>
                            </div>

                            <div class="strategy-card" data-strategy="bollinger_bands">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">布林带策略</h6>
                                        <small class="text-muted">价格通道突破策略</small>
                                    </div>
                                    <i class="fas fa-arrows-alt-h text-success"></i>
                                </div>
                            </div>

                            <div class="strategy-card" data-strategy="buy_and_hold">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">买入持有</h6>
                                        <small class="text-muted">基准对比策略</small>
                                    </div>
                                    <i class="fas fa-hand-holding-usd text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 策略参数 -->
                <div class="card" id="parametersCard" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i>策略参数
                        </h6>
                    </div>
                    <div class="card-body" id="parametersContainer">
                        <!-- 参数将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 开始回测按钮 -->
                <div class="card">
                    <div class="card-body text-center">
                        <button class="btn btn-primary btn-lg w-100" onclick="startBacktest()">
                            <i class="fas fa-rocket me-2"></i>开始高级回测
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧：回测结果 -->
            <div class="col-lg-8">
                <!-- 回测进度 -->
                <div class="card" id="progressCard" style="display: none;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="loading-spinner me-3"></div>
                            <div>
                                <h6 class="mb-1">高级回测进行中...</h6>
                                <div class="progress" style="width: 300px;">
                                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted" id="progressText">正在初始化...</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 回测结果 -->
                <div class="card" id="resultsCard" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>回测结果
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 关键指标 -->
                        <div class="row mb-4" id="metricsRow">
                            <!-- 指标卡片将通过JavaScript生成 -->
                        </div>

                        <!-- 图表区域 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <h6>收益曲线</h6>
                                    <canvas id="equityChart" height="200"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <h6>回撤曲线</h6>
                                    <canvas id="drawdownChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 详细分析 -->
                        <div class="row">
                            <div class="col-md-4">
                                <h6>性能指标</h6>
                                <div id="performanceMetrics">
                                    <!-- 性能指标将通过JavaScript生成 -->
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6>风险指标</h6>
                                <div id="riskMetrics">
                                    <!-- 风险指标将通过JavaScript生成 -->
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6>交易分析</h6>
                                <div id="tradeAnalysis">
                                    <!-- 交易分析将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 交易记录 -->
                        <div class="mt-4">
                            <h6>交易记录</h6>
                            <div id="tradesList" style="max-height: 300px; overflow-y: auto;">
                                <!-- 交易记录将通过JavaScript生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 默认显示 -->
                <div class="card" id="defaultCard">
                    <div class="card-body text-center">
                        <i class="fas fa-rocket fa-4x text-primary mb-3"></i>
                        <h4>专业级回测系统</h4>
                        <p class="text-muted">选择策略，配置参数，开始您的高级回测分析</p>

                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <i class="fas fa-brain fa-2x mb-2"></i>
                                    <div class="metric-label">内置策略</div>
                                    <div class="metric-number">4+</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <i class="fas fa-chart-area fa-2x mb-2"></i>
                                    <div class="metric-label">技术指标</div>
                                    <div class="metric-number">15+</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <i class="fas fa-calculator fa-2x mb-2"></i>
                                    <div class="metric-label">风险指标</div>
                                    <div class="metric-number">10+</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                                    <div class="metric-label">回测速度</div>
                                    <div class="metric-number">秒级</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedStrategy = null;
        let equityChart = null;
        let drawdownChart = null;

        // 策略参数配置
        const strategyParameters = {
            'moving_average_crossover': [
                { name: 'fast_period', label: '快速均线周期', type: 'number', default: 10, min: 5, max: 50 },
                { name: 'slow_period', label: '慢速均线周期', type: 'number', default: 20, min: 10, max: 200 },
                { name: 'position_size', label: '仓位比例', type: 'number', default: 0.95, min: 0.1, max: 1.0, step: 0.05 }
            ],
            'rsi_mean_reversion': [
                { name: 'rsi_oversold', label: 'RSI超卖阈值', type: 'number', default: 30, min: 10, max: 40 },
                { name: 'rsi_overbought', label: 'RSI超买阈值', type: 'number', default: 70, min: 60, max: 90 },
                { name: 'position_size', label: '仓位比例', type: 'number', default: 0.95, min: 0.1, max: 1.0, step: 0.05 }
            ],
            'bollinger_bands': [
                { name: 'bb_period', label: '布林带周期', type: 'number', default: 20, min: 10, max: 50 },
                { name: 'bb_std', label: '标准差倍数', type: 'number', default: 2, min: 1, max: 3, step: 0.1 },
                { name: 'position_size', label: '仓位比例', type: 'number', default: 0.95, min: 0.1, max: 1.0, step: 0.05 }
            ],
            'buy_and_hold': []
        };

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期
            const endDate = new Date();
            const startDate = new Date();
            startDate.setFullYear(endDate.getFullYear() - 1);

            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

            // 绑定策略选择事件
            document.querySelectorAll('.strategy-card').forEach(card => {
                card.addEventListener('click', function() {
                    selectStrategy(this.dataset.strategy);
                });
            });

            // 默认选择第一个策略
            selectStrategy('moving_average_crossover');
        });

        // 选择策略
        function selectStrategy(strategyType) {
            selectedStrategy = strategyType;

            // 更新UI
            document.querySelectorAll('.strategy-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-strategy="${strategyType}"]`).classList.add('selected');

            // 显示参数配置
            showParameters(strategyType);
        }

        // 显示策略参数
        function showParameters(strategyType) {
            const parametersCard = document.getElementById('parametersCard');
            const parametersContainer = document.getElementById('parametersContainer');
            const parameters = strategyParameters[strategyType];

            if (parameters.length === 0) {
                parametersCard.style.display = 'none';
                return;
            }

            parametersCard.style.display = 'block';
            parametersContainer.innerHTML = parameters.map(param => `
                <div class="parameter-input">
                    <div class="parameter-label">${param.label}</div>
                    <input type="${param.type}"
                           id="param_${param.name}"
                           class="form-control form-control-sm"
                           value="${param.default}"
                           min="${param.min || ''}"
                           max="${param.max || ''}"
                           step="${param.step || ''}">
                </div>
            `).join('');
        }

        // 获取策略参数
        function getStrategyParameters() {
            const parameters = {};
            const paramInputs = document.querySelectorAll('[id^="param_"]');

            paramInputs.forEach(input => {
                const paramName = input.id.replace('param_', '');
                parameters[paramName] = parseFloat(input.value) || input.value;
            });

            return parameters;
        }

        // 开始回测
        async function startBacktest() {
            try {
                // 验证输入
                const symbol = document.getElementById('symbol').value.trim();
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const initialCapital = parseFloat(document.getElementById('initialCapital').value);
                const backtestName = document.getElementById('backtestName').value.trim();

                if (!symbol || !startDate || !endDate || !selectedStrategy) {
                    showAlert('请填写所有必需字段并选择策略', 'warning');
                    return;
                }

                if (new Date(startDate) >= new Date(endDate)) {
                    showAlert('结束日期必须晚于开始日期', 'warning');
                    return;
                }

                // 显示进度
                showProgress();

                // 构建请求数据
                const requestData = {
                    name: backtestName || `${symbol} 高级回测`,
                    symbol: symbol.toUpperCase(),
                    start_date: startDate,
                    end_date: endDate,
                    initial_capital: initialCapital,
                    strategy_type: selectedStrategy,
                    parameters: getStrategyParameters(),
                    interval: document.getElementById('interval').value
                };

                // 发送回测请求
                const response = await fetch('/api/backtest/advanced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (result.success) {
                    displayResults(result.report);
                    showAlert('回测完成！', 'success');
                } else {
                    hideProgress();
                    showAlert(`回测失败: ${result.error}`, 'danger');
                }

            } catch (error) {
                hideProgress();
                showAlert(`回测失败: ${error.message}`, 'danger');
                console.error('回测错误:', error);
            }
        }

        // 显示进度
        function showProgress() {
            document.getElementById('defaultCard').style.display = 'none';
            document.getElementById('resultsCard').style.display = 'none';
            document.getElementById('progressCard').style.display = 'block';

            // 模拟进度
            let progress = 0;
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                progressBar.style.width = progress + '%';

                if (progress < 30) {
                    progressText.textContent = '正在获取历史数据...';
                } else if (progress < 60) {
                    progressText.textContent = '正在计算技术指标...';
                } else if (progress < 90) {
                    progressText.textContent = '正在执行策略...';
                } else {
                    progressText.textContent = '正在分析结果...';
                }
            }, 200);

            // 保存interval ID以便后续清理
            window.progressInterval = interval;
        }

        // 隐藏进度
        function hideProgress() {
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
            }
            document.getElementById('progressCard').style.display = 'none';
            document.getElementById('defaultCard').style.display = 'block';
        }

        // 显示结果
        function displayResults(report) {
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
            }

            // 完成进度条
            document.getElementById('progressBar').style.width = '100%';
            document.getElementById('progressText').textContent = '回测完成！';

            setTimeout(() => {
                document.getElementById('progressCard').style.display = 'none';
                document.getElementById('resultsCard').style.display = 'block';

                // 显示关键指标
                displayKeyMetrics(report);

                // 绘制图表
                drawCharts(report);

                // 显示详细分析
                displayDetailedAnalysis(report);

                // 显示交易记录
                displayTrades(report.trades);

            }, 1000);
        }

        // 显示关键指标
        function displayKeyMetrics(report) {
            const metricsRow = document.getElementById('metricsRow');
            const performance = report.performance;

            metricsRow.innerHTML = `
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-number ${performance.total_return >= 0 ? 'positive' : 'negative'}">
                            ${performance.total_return.toFixed(2)}%
                        </div>
                        <div class="metric-label">总收益率</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-number ${performance.annual_return >= 0 ? 'positive' : 'negative'}">
                            ${performance.annual_return.toFixed(2)}%
                        </div>
                        <div class="metric-label">年化收益率</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-number ${performance.sharpe_ratio >= 1 ? 'positive' : performance.sharpe_ratio >= 0 ? 'neutral' : 'negative'}">
                            ${performance.sharpe_ratio.toFixed(2)}
                        </div>
                        <div class="metric-label">夏普比率</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-number negative">
                            ${Math.abs(performance.max_drawdown).toFixed(2)}%
                        </div>
                        <div class="metric-label">最大回撤</div>
                    </div>
                </div>
            `;
        }

        // 绘制图表
        function drawCharts(report) {
            // 收益曲线
            drawEquityChart(report.equity_curve);

            // 回撤曲线
            drawDrawdownChart(report.drawdown_curve);
        }

        // 绘制收益曲线
        function drawEquityChart(equityCurve) {
            const ctx = document.getElementById('equityChart').getContext('2d');

            if (equityChart) {
                equityChart.destroy();
            }

            const labels = equityCurve.map((_, index) => index);

            equityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '投资组合价值',
                        data: equityCurve,
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#cbd5e1'
                            }
                        }
                    }
                }
            });
        }

        // 绘制回撤曲线
        function drawDrawdownChart(drawdownCurve) {
            const ctx = document.getElementById('drawdownChart').getContext('2d');

            if (drawdownChart) {
                drawdownChart.destroy();
            }

            const labels = drawdownCurve.map((_, index) => index);
            const drawdownData = drawdownCurve.map(dd => dd * 100);

            drawdownChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '回撤 (%)',
                        data: drawdownData,
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#cbd5e1'
                            }
                        }
                    }
                }
            });
        }

        // 显示详细分析
        function displayDetailedAnalysis(report) {
            const performance = report.performance;
            const riskMetrics = report.risk_metrics;
            const tradeAnalysis = report.trade_analysis;

            // 性能指标
            document.getElementById('performanceMetrics').innerHTML = `
                <div class="mb-2">
                    <small class="text-muted">总收益率</small>
                    <div class="${performance.total_return >= 0 ? 'positive' : 'negative'}">${performance.total_return.toFixed(2)}%</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">年化收益率</small>
                    <div class="${performance.annual_return >= 0 ? 'positive' : 'negative'}">${performance.annual_return.toFixed(2)}%</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">夏普比率</small>
                    <div>${performance.sharpe_ratio.toFixed(3)}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">索提诺比率</small>
                    <div>${performance.sortino_ratio.toFixed(3)}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">盈利因子</small>
                    <div>${performance.profit_factor.toFixed(2)}</div>
                </div>
            `;

            // 风险指标
            document.getElementById('riskMetrics').innerHTML = `
                <div class="mb-2">
                    <small class="text-muted">最大回撤</small>
                    <div class="negative">${Math.abs(performance.max_drawdown).toFixed(2)}%</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">年化波动率</small>
                    <div>${riskMetrics.volatility ? riskMetrics.volatility.toFixed(2) + '%' : 'N/A'}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">VaR (95%)</small>
                    <div>${riskMetrics.var_95 ? riskMetrics.var_95.toFixed(2) + '%' : 'N/A'}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">卡尔马比率</small>
                    <div>${riskMetrics.calmar_ratio ? riskMetrics.calmar_ratio.toFixed(2) : 'N/A'}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">偏度</small>
                    <div>${riskMetrics.skewness ? riskMetrics.skewness.toFixed(3) : 'N/A'}</div>
                </div>
            `;

            // 交易分析
            document.getElementById('tradeAnalysis').innerHTML = `
                <div class="mb-2">
                    <small class="text-muted">总交易次数</small>
                    <div>${tradeAnalysis.total_trades || 0}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">胜率</small>
                    <div class="${(tradeAnalysis.win_rate || 0) >= 50 ? 'positive' : 'negative'}">${(tradeAnalysis.win_rate || 0).toFixed(1)}%</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">平均持仓天数</small>
                    <div>${tradeAnalysis.avg_holding_days ? tradeAnalysis.avg_holding_days.toFixed(1) : 'N/A'}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">最大单笔盈利</small>
                    <div class="positive">${tradeAnalysis.largest_win_pct ? tradeAnalysis.largest_win_pct.toFixed(2) + '%' : 'N/A'}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">最大单笔亏损</small>
                    <div class="negative">${tradeAnalysis.largest_loss_pct ? tradeAnalysis.largest_loss_pct.toFixed(2) + '%' : 'N/A'}</div>
                </div>
            `;
        }

        // 显示交易记录
        function displayTrades(trades) {
            const tradesList = document.getElementById('tradesList');

            if (!trades || trades.length === 0) {
                tradesList.innerHTML = '<p class="text-muted">暂无交易记录</p>';
                return;
            }

            tradesList.innerHTML = trades.map(trade => `
                <div class="trade-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge badge-${trade.type === 'buy' ? 'success' : 'danger'}">${trade.type === 'buy' ? '买入' : '卖出'}</span>
                            <span class="ms-2">${trade.date}</span>
                        </div>
                        <div class="text-end">
                            <div>$${trade.price.toFixed(2)} × ${trade.shares}</div>
                            <small class="text-muted">${trade.signal}</small>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST'
                });

                if (response.ok) {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        }
    </script>
</body>
</html>
