<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantTradeX - 量化交易系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px);
        }
        
        .navbar-brand, .nav-link {
            color: white !important;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }
        
        .hero-section {
            padding: 100px 0;
            text-align: center;
            color: white;
        }
        
        .feature-card {
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: #4ade80;
            box-shadow: 0 0 10px #4ade80;
        }
        
        .status-offline {
            background-color: #ef4444;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">交易面板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="systemStatus">
                            <span class="status-indicator" id="statusDot"></span>
                            <span id="statusText">检查中...</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="hero-section">
        <div class="container">
            <h1 class="display-4 mb-4">
                <i class="fas fa-rocket me-3"></i>
                欢迎使用 QuantTradeX
            </h1>
            <p class="lead mb-5">专业的量化交易系统，助您在金融市场中获得优势</p>
            <a href="/dashboard" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-chart-bar me-2"></i>开始交易
            </a>
            <button class="btn btn-outline-light btn-lg" onclick="checkSystemStatus()">
                <i class="fas fa-cog me-2"></i>系统状态
            </button>
        </div>
    </div>

    <!-- 功能特性 -->
    <div class="container my-5">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-3x mb-3 text-primary"></i>
                        <h5 class="card-title">实时数据</h5>
                        <p class="card-text">获取实时股票价格、技术指标和市场数据，支持多种金融工具。</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-robot fa-3x mb-3 text-success"></i>
                        <h5 class="card-title">智能策略</h5>
                        <p class="card-text">基于机器学习的交易策略，自动化执行交易决策。</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-3x mb-3 text-warning"></i>
                        <h5 class="card-title">风险管理</h5>
                        <p class="card-text">完善的风险控制系统，保护您的投资安全。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速股票查询 -->
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>快速股票查询
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="stockSymbol" 
                                   placeholder="输入股票代码 (如: AAPL, GOOGL, TSLA)" 
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                            <button class="btn btn-primary" onclick="searchStock()">
                                <i class="fas fa-search"></i> 查询
                            </button>
                        </div>
                        <div id="stockResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 关注列表 -->
    <div class="container my-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>热门股票
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="watchlistContainer">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('/api/system/status');
                const status = await response.json();
                
                const statusDot = document.getElementById('statusDot');
                const statusText = document.getElementById('statusText');
                
                if (status.redis && status.database) {
                    statusDot.className = 'status-indicator status-online';
                    statusText.textContent = '系统正常';
                } else {
                    statusDot.className = 'status-indicator status-offline';
                    statusText.textContent = '系统异常';
                }
            } catch (error) {
                console.error('检查系统状态失败:', error);
                document.getElementById('statusDot').className = 'status-indicator status-offline';
                document.getElementById('statusText').textContent = '连接失败';
            }
        }

        // 搜索股票
        async function searchStock() {
            const symbol = document.getElementById('stockSymbol').value.trim().toUpperCase();
            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            const resultDiv = document.getElementById('stockResult');
            resultDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 查询中...</div>';

            try {
                const response = await fetch(`/api/stock/${symbol}`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    const indicators = data.indicators;
                    
                    resultDiv.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>${data.symbol}</h6>
                                <p class="mb-1">当前价格: <strong>$${indicators.current_price?.toFixed(2) || 'N/A'}</strong></p>
                                <p class="mb-1">涨跌: <span class="${indicators.price_change >= 0 ? 'text-success' : 'text-danger'}">
                                    ${indicators.price_change >= 0 ? '+' : ''}${indicators.price_change?.toFixed(2) || 'N/A'} 
                                    (${indicators.price_change_pct?.toFixed(2) || 'N/A'}%)
                                </span></p>
                                <p class="mb-0">更新时间: ${new Date(data.last_update).toLocaleString()}</p>
                            </div>
                            <div class="col-md-6">
                                <canvas id="stockChart" width="300" height="150"></canvas>
                            </div>
                        </div>
                    `;

                    // 绘制简单图表
                    const ctx = document.getElementById('stockChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: data.dates.slice(-30),
                            datasets: [{
                                label: '收盘价',
                                data: data.close.slice(-30),
                                borderColor: 'rgb(75, 192, 192)',
                                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                                tension: 0.1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    labels: {
                                        color: 'white'
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    ticks: {
                                        color: 'white'
                                    }
                                },
                                y: {
                                    ticks: {
                                        color: 'white'
                                    }
                                }
                            }
                        }
                    });
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">${result.error}</div>`;
                }
            } catch (error) {
                console.error('查询股票失败:', error);
                resultDiv.innerHTML = '<div class="alert alert-danger">查询失败，请稍后重试</div>';
            }
        }

        // 加载关注列表
        async function loadWatchlist() {
            try {
                const response = await fetch('/api/watchlist');
                const result = await response.json();

                if (result.success) {
                    const container = document.getElementById('watchlistContainer');
                    container.innerHTML = result.data.map(stock => `
                        <div class="row border-bottom py-2">
                            <div class="col-3">
                                <strong>${stock.symbol}</strong>
                            </div>
                            <div class="col-3">
                                $${stock.price.toFixed(2)}
                            </div>
                            <div class="col-3">
                                <span class="${stock.change >= 0 ? 'text-success' : 'text-danger'}">
                                    ${stock.change >= 0 ? '+' : ''}${stock.change.toFixed(2)}
                                </span>
                            </div>
                            <div class="col-3">
                                <span class="${stock.change_pct >= 0 ? 'text-success' : 'text-danger'}">
                                    ${stock.change_pct >= 0 ? '+' : ''}${stock.change_pct.toFixed(2)}%
                                </span>
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('加载关注列表失败:', error);
                document.getElementById('watchlistContainer').innerHTML = 
                    '<div class="alert alert-warning">加载失败</div>';
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            loadWatchlist();
            
            // 股票输入框回车事件
            document.getElementById('stockSymbol').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchStock();
                }
            });
        });
    </script>
</body>
</html>
