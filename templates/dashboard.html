<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易面板 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px);
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            margin-bottom: 20px;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }

        .btn-success {
            background: rgba(40, 167, 69, 0.8);
            border: 1px solid rgba(40, 167, 69, 0.9);
        }

        .btn-danger {
            background: rgba(220, 53, 69, 0.8);
            border: 1px solid rgba(220, 53, 69, 0.9);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .chart-container {
            position: relative;
            height: 400px;
        }

        .metric-card {
            text-align: center;
            padding: 20px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link active" href="/dashboard">仪表板</a>
                <a class="nav-link" href="/strategies">策略市场</a>
                <a class="nav-link" href="/backtest">基础回测</a>
                <a class="nav-link" href="/advanced_backtest">高级回测</a>
                <a class="nav-link" href="/realtime">实时数据</a>
                <a class="nav-link" href="/forum">社区论坛</a>
            </div>
            <div class="navbar-nav ms-auto" id="navbarUser">
                <a class="nav-link" href="#" onclick="showLogin()">
                    <i class="fas fa-sign-in-alt me-1"></i>登录
                </a>
                <a class="nav-link" href="#" onclick="showRegister()">
                    <i class="fas fa-user-plus me-1"></i>注册
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 左侧面板 -->
            <div class="col-md-3">
                <!-- 股票搜索 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-search me-2"></i>股票查询</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="searchSymbol"
                                   placeholder="输入股票代码">
                        </div>
                        <div class="mb-3">
                            <select class="form-select" id="timePeriod">
                                <option value="1d">1天</option>
                                <option value="5d">5天</option>
                                <option value="1mo" selected>1个月</option>
                                <option value="3mo">3个月</option>
                                <option value="6mo">6个月</option>
                                <option value="1y">1年</option>
                            </select>
                        </div>
                        <button class="btn btn-primary w-100" onclick="loadStockData()">
                            <i class="fas fa-chart-bar me-1"></i>加载数据
                        </button>
                    </div>
                </div>

                <!-- 交易操作 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>模拟交易</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">股票代码</label>
                            <input type="text" class="form-control" id="tradeSymbol"
                                   placeholder="如: AAPL">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">数量</label>
                            <input type="number" class="form-control" id="tradeQuantity"
                                   placeholder="100" value="100">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">价格</label>
                            <input type="number" class="form-control" id="tradePrice"
                                   placeholder="市价" step="0.01">
                        </div>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="simulateTrade('buy')">
                                <i class="fas fa-arrow-up me-1"></i>买入
                            </button>
                            <button class="btn btn-danger" onclick="simulateTrade('sell')">
                                <i class="fas fa-arrow-down me-1"></i>卖出
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 投资组合 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>投资组合</h6>
                    </div>
                    <div class="card-body">
                        <div id="portfolioContainer">
                            <p class="text-center text-muted">暂无持仓</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间主要内容 -->
            <div class="col-md-6">
                <!-- 股票信息 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" id="stockTitle">
                            <i class="fas fa-chart-line me-2"></i>选择股票查看数据
                        </h6>
                        <button class="btn btn-sm btn-outline-light" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="stockInfo" class="mb-3">
                            <div class="text-center text-muted">
                                <i class="fas fa-chart-line fa-3x mb-3"></i>
                                <p>请在左侧搜索框输入股票代码并点击"加载数据"</p>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="priceChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 技术指标 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>技术指标</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="indicatorChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="col-md-3">
                <!-- 市场概览 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-globe me-2"></i>市场概览</h6>
                    </div>
                    <div class="card-body">
                        <div id="marketOverview">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 交易历史 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-history me-2"></i>交易记录</h6>
                    </div>
                    <div class="card-body">
                        <div id="tradeHistory">
                            <p class="text-center text-muted">暂无交易记录</p>
                        </div>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-server me-2"></i>系统状态</h6>
                    </div>
                    <div class="card-body">
                        <div class="metric-card">
                            <div class="metric-value text-success" id="systemStatus">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-label">系统运行正常</div>
                        </div>
                        <hr>
                        <small class="text-muted">
                            <div>数据库: <span id="dbStatus" class="text-success">正常</span></div>
                            <div>缓存: <span id="redisStatus" class="text-success">正常</span></div>
                            <div>最后更新: <span id="lastUpdate">--</span></div>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/user-auth.js"></script>
    <script>
        let currentChart = null;
        let indicatorChart = null;
        let portfolio = JSON.parse(localStorage.getItem('portfolio') || '{}');
        let tradeHistory = JSON.parse(localStorage.getItem('tradeHistory') || '[]');

        // 加载股票数据
        async function loadStockData() {
            const symbol = document.getElementById('searchSymbol').value.trim().toUpperCase();
            const period = document.getElementById('timePeriod').value;

            if (!symbol) {
                alert('请输入股票代码');
                return;
            }

            document.getElementById('stockTitle').innerHTML =
                '<i class="fas fa-spinner fa-spin me-2"></i>加载中...';

            try {
                const response = await fetch(`/api/stock/${symbol}?period=${period}`);
                const result = await response.json();

                if (result.success) {
                    displayStockData(result.data);
                    document.getElementById('tradeSymbol').value = symbol;
                    document.getElementById('tradePrice').value =
                        result.data.indicators.current_price?.toFixed(2) || '';
                } else {
                    alert(result.error);
                    document.getElementById('stockTitle').innerHTML =
                        '<i class="fas fa-exclamation-triangle me-2"></i>加载失败';
                }
            } catch (error) {
                console.error('加载股票数据失败:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 显示股票数据
        function displayStockData(data) {
            const indicators = data.indicators;

            // 更新标题
            document.getElementById('stockTitle').innerHTML =
                `<i class="fas fa-chart-line me-2"></i>${data.symbol}`;

            // 更新股票信息
            const changeClass = indicators.price_change >= 0 ? 'text-success' : 'text-danger';
            const changeIcon = indicators.price_change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

            document.getElementById('stockInfo').innerHTML = `
                <div class="row text-center">
                    <div class="col-4">
                        <div class="metric-value">$${indicators.current_price?.toFixed(2) || 'N/A'}</div>
                        <div class="metric-label">当前价格</div>
                    </div>
                    <div class="col-4">
                        <div class="metric-value ${changeClass}">
                            <i class="fas ${changeIcon} me-1"></i>
                            ${indicators.price_change?.toFixed(2) || 'N/A'}
                        </div>
                        <div class="metric-label">涨跌额</div>
                    </div>
                    <div class="col-4">
                        <div class="metric-value ${changeClass}">
                            ${indicators.price_change_pct?.toFixed(2) || 'N/A'}%
                        </div>
                        <div class="metric-label">涨跌幅</div>
                    </div>
                </div>
            `;

            // 绘制价格图表
            drawPriceChart(data);
            drawIndicatorChart(data);
        }

        // 绘制价格图表
        function drawPriceChart(data) {
            const ctx = document.getElementById('priceChart').getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.dates,
                    datasets: [{
                        label: '收盘价',
                        data: data.close,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: { ticks: { color: 'white' } },
                        y: { ticks: { color: 'white' } }
                    }
                }
            });
        }

        // 绘制技术指标图表
        function drawIndicatorChart(data) {
            const ctx = document.getElementById('indicatorChart').getContext('2d');

            if (indicatorChart) {
                indicatorChart.destroy();
            }

            const indicators = data.indicators;
            const sma20Data = new Array(data.dates.length - indicators.sma_20.length).fill(null).concat(indicators.sma_20);
            const rsiData = new Array(data.dates.length - indicators.rsi.length).fill(null).concat(indicators.rsi);

            indicatorChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.dates,
                    datasets: [{
                        label: 'SMA 20',
                        data: sma20Data,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        yAxisID: 'y',
                        tension: 0.1
                    }, {
                        label: 'RSI',
                        data: rsiData,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: { ticks: { color: 'white' } },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            ticks: { color: 'white' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            min: 0,
                            max: 100,
                            ticks: { color: 'white' },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        // 模拟交易
        function simulateTrade(action) {
            const symbol = document.getElementById('tradeSymbol').value.trim().toUpperCase();
            const quantity = parseInt(document.getElementById('tradeQuantity').value);
            const price = parseFloat(document.getElementById('tradePrice').value);

            if (!symbol || !quantity || !price) {
                alert('请填写完整的交易信息');
                return;
            }

            const trade = {
                id: Date.now(),
                symbol,
                action,
                quantity,
                price,
                total: quantity * price,
                timestamp: new Date().toISOString()
            };

            // 更新投资组合
            if (!portfolio[symbol]) {
                portfolio[symbol] = { quantity: 0, avgPrice: 0 };
            }

            if (action === 'buy') {
                const oldTotal = portfolio[symbol].quantity * portfolio[symbol].avgPrice;
                const newTotal = oldTotal + trade.total;
                portfolio[symbol].quantity += quantity;
                portfolio[symbol].avgPrice = newTotal / portfolio[symbol].quantity;
            } else {
                portfolio[symbol].quantity -= quantity;
                if (portfolio[symbol].quantity <= 0) {
                    delete portfolio[symbol];
                }
            }

            // 保存数据
            tradeHistory.unshift(trade);
            localStorage.setItem('portfolio', JSON.stringify(portfolio));
            localStorage.setItem('tradeHistory', JSON.stringify(tradeHistory));

            // 更新显示
            updatePortfolioDisplay();
            updateTradeHistoryDisplay();

            alert(`${action === 'buy' ? '买入' : '卖出'}成功！`);
        }

        // 更新投资组合显示
        function updatePortfolioDisplay() {
            const container = document.getElementById('portfolioContainer');

            if (Object.keys(portfolio).length === 0) {
                container.innerHTML = '<p class="text-center text-muted">暂无持仓</p>';
                return;
            }

            container.innerHTML = Object.entries(portfolio).map(([symbol, data]) => `
                <div class="border-bottom py-2">
                    <div class="d-flex justify-content-between">
                        <strong>${symbol}</strong>
                        <span>${data.quantity}股</span>
                    </div>
                    <small class="text-muted">均价: $${data.avgPrice.toFixed(2)}</small>
                </div>
            `).join('');
        }

        // 更新交易历史显示
        function updateTradeHistoryDisplay() {
            const container = document.getElementById('tradeHistory');

            if (tradeHistory.length === 0) {
                container.innerHTML = '<p class="text-center text-muted">暂无交易记录</p>';
                return;
            }

            container.innerHTML = tradeHistory.slice(0, 10).map(trade => `
                <div class="border-bottom py-2">
                    <div class="d-flex justify-content-between">
                        <span class="${trade.action === 'buy' ? 'text-success' : 'text-danger'}">
                            ${trade.action === 'buy' ? '买入' : '卖出'} ${trade.symbol}
                        </span>
                        <span>${trade.quantity}股</span>
                    </div>
                    <small class="text-muted">
                        $${trade.price.toFixed(2)} - ${new Date(trade.timestamp).toLocaleString()}
                    </small>
                </div>
            `).join('');
        }

        // 加载市场概览
        async function loadMarketOverview() {
            try {
                const response = await fetch('/api/watchlist');
                const result = await response.json();

                if (result.success) {
                    const container = document.getElementById('marketOverview');
                    container.innerHTML = result.watchlist.map(stock => `
                        <div class="border-bottom py-2">
                            <div class="d-flex justify-content-between">
                                <strong>${stock.symbol}</strong>
                                <span>$${stock.price.toFixed(2)}</span>
                            </div>
                            <small class="${stock.change >= 0 ? 'text-success' : 'text-danger'}">
                                ${stock.change >= 0 ? '+' : ''}${stock.change.toFixed(2)}
                            </small>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('加载市场概览失败:', error);
            }
        }

        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('/api/system/status');
                const status = await response.json();

                document.getElementById('dbStatus').textContent = status.database ? '正常' : '异常';
                document.getElementById('dbStatus').className = status.database ? 'text-success' : 'text-danger';

                document.getElementById('redisStatus').textContent = status.redis ? '正常' : '异常';
                document.getElementById('redisStatus').className = status.redis ? 'text-success' : 'text-danger';

                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            } catch (error) {
                console.error('检查系统状态失败:', error);
            }
        }

        // 刷新数据
        function refreshData() {
            loadStockData();
            loadMarketOverview();
            checkSystemStatus();
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 显示登录模态框
        function showLogin() {
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            loginModal.show();
        }

        // 显示注册模态框
        function showRegister() {
            const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
            registerModal.show();
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            updatePortfolioDisplay();
            updateTradeHistoryDisplay();
            loadMarketOverview();
            checkSystemStatus();
            checkLoginStatus();

            // 搜索框回车事件
            document.getElementById('searchSymbol').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loadStockData();
                }
            });

            // 定时刷新
            setInterval(() => {
                loadMarketOverview();
                checkSystemStatus();
            }, 30000);
        });
    </script>

    <!-- 页脚 -->
    <footer class="text-center py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-secondary mb-3">&copy; 2025 QuantTradeX. 户部尚书专业量化交易策略平台</p>
                    <div class="d-flex justify-content-center gap-4 flex-wrap">
                        <a href="/api/system/status" class="text-secondary text-decoration-none">
                            <i class="fas fa-server me-1"></i>系统状态
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-book me-1"></i>API文档
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a>
                        <a href="#" class="text-secondary text-decoration-none">
                            <i class="fas fa-shield-alt me-1"></i>隐私政策
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: rgba(30, 41, 59, 0.4); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">用户登录</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm" onsubmit="event.preventDefault(); performLogin();">
                        <div class="mb-3">
                            <label class="form-label text-white">邮箱</label>
                            <input type="email" class="form-control" id="loginEmail" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">登录</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: rgba(30, 41, 59, 0.4); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">用户注册</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm" onsubmit="event.preventDefault(); performRegister();">
                        <div class="mb-3">
                            <label class="form-label text-white">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">全名</label>
                            <input type="text" class="form-control" id="registerFullName"
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">注册</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
