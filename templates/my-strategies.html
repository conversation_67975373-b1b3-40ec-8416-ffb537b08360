<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的策略 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* 导航栏 */
        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand i {
            color: var(--primary);
            font-size: 1.8rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--text-primary) !important;
            background: rgba(99, 102, 241, 0.1);
        }

        /* 卡片样式 */
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .strategy-card {
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .strategy-card:hover .strategy-title {
            color: var(--primary) !important;
        }

        .strategy-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .strategy-card:hover::before {
            transform: scaleX(1);
        }

        .strategy-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: rgba(99, 102, 241, 0.3);
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .btn-outline-light {
            border: 2px solid var(--glass-border);
            border-radius: 10px;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--primary);
            color: var(--primary);
            transform: translateY(-2px);
        }

        /* 页面标题 */
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 50%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 5rem;
            margin-bottom: 2rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .empty-state p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* 状态徽章 */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-draft {
            background: rgba(156, 163, 175, 0.2);
            color: #9ca3af;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }

        .status-published {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-private {
            background: rgba(239, 68, 68, 0.2);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        /* 操作按钮 */
        .action-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .action-btn.edit:hover {
            color: var(--primary);
        }

        .action-btn.delete:hover {
            color: var(--danger);
        }

        .action-btn.share:hover {
            color: var(--success);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">仪表板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">策略市场</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/my-strategies">我的策略</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategy-editor">
                            <i class="fas fa-code me-1"></i>策略开发
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">基础回测</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">高级回测</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/realtime">实时数据</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">社区论坛</a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showRegister()">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5 pt-4">
        <!-- 页面标题 -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="page-title">
                    <i class="fas fa-code me-3"></i>我的策略
                </h1>
                <p class="page-subtitle">管理和编辑您创建的量化交易策略</p>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <button class="btn btn-primary me-2" onclick="createNewStrategy()">
                            <i class="fas fa-plus me-1"></i>创建新策略
                        </button>
                        <button class="btn btn-outline-light me-2" onclick="importStrategy()">
                            <i class="fas fa-upload me-1"></i>导入策略
                        </button>
                    </div>
                    <div>
                        <select class="form-select" id="statusFilter" onchange="filterStrategies()">
                            <option value="">所有状态</option>
                            <option value="draft">草稿</option>
                            <option value="published">已发布</option>
                            <option value="private">私有</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 策略列表 -->
        <div class="row" id="strategiesContainer">
            <!-- 策略卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">用户登录</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm" onsubmit="event.preventDefault(); performLogin();">
                        <div class="mb-3">
                            <label class="form-label text-white">邮箱</label>
                            <input type="email" class="form-control" id="loginEmail" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label text-white" for="rememberMe">记住我</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">登录</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">用户注册</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm" onsubmit="event.preventDefault(); performRegister();">
                        <div class="mb-3">
                            <label class="form-label text-white">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">全名</label>
                            <input type="text" class="form-control" id="registerFullName"
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white;">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">注册</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/user-auth.js"></script>
    <script>
        let myStrategies = [];

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            loadMyStrategies();
        });

        // 加载我的策略
        async function loadMyStrategies() {
            try {
                // 模拟数据，实际应该从API获取
                myStrategies = [
                    {
                        id: 1,
                        name: '我的双均线策略',
                        description: '基于20日和50日移动平均线的趋势跟踪策略',
                        status: 'published',
                        created_at: '2025-01-20T10:30:00',
                        updated_at: '2025-01-25T15:20:00',
                        performance: {
                            total_return: 15.6,
                            sharpe_ratio: 1.2,
                            max_drawdown: -8.5
                        }
                    },
                    {
                        id: 2,
                        name: 'RSI反转策略',
                        description: '基于RSI指标的均值回归策略',
                        status: 'draft',
                        created_at: '2025-01-22T14:15:00',
                        updated_at: '2025-01-22T16:45:00',
                        performance: null
                    }
                ];

                displayStrategies(myStrategies);
            } catch (error) {
                console.error('加载策略失败:', error);
                showEmptyState('加载失败', '无法加载您的策略，请稍后重试');
            }
        }

        // 显示策略列表
        function displayStrategies(strategies) {
            const container = document.getElementById('strategiesContainer');

            if (strategies.length === 0) {
                showEmptyState();
                return;
            }

            container.innerHTML = strategies.map(strategy => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card strategy-card h-100" onclick="viewStrategy(${strategy.id})">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 strategy-title" style="cursor: pointer;">${strategy.name}</h6>
                            <span class="status-badge status-${strategy.status}">
                                ${getStatusText(strategy.status)}
                            </span>
                        </div>
                        <div class="card-body">
                            <p class="card-text small text-secondary">${strategy.description}</p>

                            ${strategy.performance ? `
                                <div class="mb-3">
                                    <small class="text-muted">策略表现:</small>
                                    <div class="row text-center mt-2">
                                        <div class="col-4">
                                            <div class="text-success fw-bold">${strategy.performance.total_return}%</div>
                                            <small class="text-muted">总收益</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-info fw-bold">${strategy.performance.sharpe_ratio}</div>
                                            <small class="text-muted">夏普比率</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-warning fw-bold">${strategy.performance.max_drawdown}%</div>
                                            <small class="text-muted">最大回撤</small>
                                        </div>
                                    </div>
                                </div>
                            ` : `
                                <div class="mb-3 text-center">
                                    <small class="text-muted">尚未进行回测</small>
                                </div>
                            `}

                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    更新: ${new Date(strategy.updated_at).toLocaleDateString()}
                                </small>
                                <div>
                                    <button class="action-btn edit" onclick="event.stopPropagation(); editStrategy(${strategy.id})" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn share" onclick="event.stopPropagation(); shareStrategy(${strategy.id})" title="分享">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                    <button class="action-btn delete" onclick="event.stopPropagation(); deleteStrategy(${strategy.id})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 显示空状态
        function showEmptyState(title = '暂无策略', message = '您还没有创建任何策略，点击下方按钮开始创建您的第一个策略吧！') {
            const container = document.getElementById('strategiesContainer');
            container.innerHTML = `
                <div class="col-12">
                    <div class="empty-state">
                        <i class="fas fa-code"></i>
                        <h3>${title}</h3>
                        <p>${message}</p>
                        <button class="btn btn-primary" onclick="createNewStrategy()">
                            <i class="fas fa-plus me-1"></i>创建第一个策略
                        </button>
                    </div>
                </div>
            `;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'draft': '草稿',
                'published': '已发布',
                'private': '私有'
            };
            return statusMap[status] || status;
        }

        // 查看策略详情
        function viewStrategy(strategyId) {
            // 可以跳转到策略详情页面或者显示详情模态框
            const strategy = myStrategies.find(s => s.id === strategyId);
            if (strategy) {
                // 这里可以显示策略详情模态框或跳转到详情页面
                showStrategyDetails(strategy);
            }
        }

        // 显示策略详情模态框
        function showStrategyDetails(strategy) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                        <div class="modal-header border-bottom border-secondary">
                            <h5 class="modal-title text-white">${strategy.name}</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="text-white mb-3">策略描述</h6>
                                    <p class="text-secondary">${strategy.description}</p>

                                    <h6 class="text-white mb-3 mt-4">策略信息</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">状态:</small>
                                            <div class="status-badge status-${strategy.status} mb-2">
                                                ${getStatusText(strategy.status)}
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">创建时间:</small>
                                            <div class="text-white">${new Date(strategy.created_at).toLocaleDateString()}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    ${strategy.performance ? `
                                        <h6 class="text-white mb-3">策略表现</h6>
                                        <div class="text-center">
                                            <div class="mb-3">
                                                <div class="text-success fw-bold fs-4">${strategy.performance.total_return}%</div>
                                                <small class="text-muted">总收益率</small>
                                            </div>
                                            <div class="mb-3">
                                                <div class="text-info fw-bold fs-5">${strategy.performance.sharpe_ratio}</div>
                                                <small class="text-muted">夏普比率</small>
                                            </div>
                                            <div class="mb-3">
                                                <div class="text-warning fw-bold fs-5">${strategy.performance.max_drawdown}%</div>
                                                <small class="text-muted">最大回撤</small>
                                            </div>
                                        </div>
                                    ` : `
                                        <div class="text-center">
                                            <i class="fas fa-chart-line text-muted" style="font-size: 3rem;"></i>
                                            <p class="text-muted mt-3">尚未进行回测</p>
                                            <button class="btn btn-outline-light btn-sm" onclick="runBacktest(${strategy.id})">
                                                <i class="fas fa-play me-1"></i>开始回测
                                            </button>
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer border-top border-secondary">
                            <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="editStrategy(${strategy.id})">
                                <i class="fas fa-edit me-1"></i>编辑策略
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // 模态框关闭后移除DOM元素
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }

        // 创建新策略
        function createNewStrategy() {
            window.location.href = '/strategy-editor';
        }

        // 编辑策略
        function editStrategy(strategyId) {
            window.location.href = `/strategy-editor?id=${strategyId}`;
        }

        // 运行回测
        function runBacktest(strategyId) {
            window.location.href = `/backtest?strategy_id=${strategyId}`;
        }

        // 分享策略
        function shareStrategy(strategyId) {
            showNotification('分享功能开发中...', 'info');
        }

        // 删除策略
        function deleteStrategy(strategyId) {
            if (confirm('确定要删除这个策略吗？此操作不可撤销。')) {
                // 实际应该调用API删除
                myStrategies = myStrategies.filter(s => s.id !== strategyId);
                displayStrategies(myStrategies);
                showNotification('策略已删除', 'success');
            }
        }

        // 导入策略
        function importStrategy() {
            showNotification('导入功能开发中...', 'info');
        }

        // 筛选策略
        function filterStrategies() {
            const status = document.getElementById('statusFilter').value;
            const filtered = status ? myStrategies.filter(s => s.status === status) : myStrategies;
            displayStrategies(filtered);
        }

        // 显示登录模态框
        function showLogin() {
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            loginModal.show();
        }

        // 显示注册模态框
        function showRegister() {
            const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
            registerModal.show();
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'info' ? 'info' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
    </script>
</body>
</html>
