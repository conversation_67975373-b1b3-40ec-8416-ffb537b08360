<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全设置 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary) !important;
        }

        .container {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
        }

        .card-header {
            background: rgba(99, 102, 241, 0.1);
            border-bottom: 1px solid var(--glass-border);
            border-radius: 16px 16px 0 0 !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger), #dc2626);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .security-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .security-status.enabled {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .security-status.disabled {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .qr-code-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .backup-codes {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .backup-code {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 0.5rem;
            border-radius: 5px;
            margin: 0.25rem;
            display: inline-block;
            min-width: 120px;
            text-align: center;
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .alert-info {
            background: rgba(6, 182, 212, 0.1);
            color: var(--accent);
            border: 1px solid rgba(6, 182, 212, 0.3);
        }

        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .step {
            flex: 1;
            text-align: center;
            position: relative;
        }

        .step::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: var(--glass-border);
            z-index: 1;
        }

        .step:last-child::after {
            display: none;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--glass-border);
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            position: relative;
            z-index: 2;
            font-weight: 600;
        }

        .step.active .step-number {
            background: var(--primary);
            color: white;
        }

        .step.completed .step-number {
            background: var(--success);
            color: white;
        }

        .step-title {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .step.active .step-title {
            color: var(--primary);
            font-weight: 600;
        }

        .step.completed .step-title {
            color: var(--success);
        }

        .modal-content {
            background: var(--dark-surface) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: 16px !important;
            color: var(--text-primary) !important;
            backdrop-filter: blur(20px) !important;
        }

        .modal-header {
            border-bottom: 1px solid var(--glass-border);
            background: rgba(99, 102, 241, 0.1);
        }

        .modal-title {
            color: var(--text-primary) !important;
            font-weight: 600;
        }

        .modal-body {
            color: var(--text-primary) !important;
        }

        .modal-footer {
            border-top: 1px solid var(--glass-border);
            background: rgba(0, 0, 0, 0.1);
        }

        .btn-close {
            filter: invert(1);
            opacity: 0.8;
        }

        .btn-close:hover {
            opacity: 1;
        }
    
        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-chart-bar me-1"></i>回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>账户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                            <li><a class="dropdown-item" href="/security">安全设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- 页面标题 -->
                <div class="text-center mb-4">
                    <h1 class="display-6 mb-3">
                        <i class="fas fa-shield-alt text-primary me-3"></i>
                        安全设置
                    </h1>
                    <p class="lead text-secondary">保护您的账户安全，启用多因素认证</p>
                </div>

                <!-- 2FA状态卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-mobile-alt me-2"></i>
                            双因素认证 (2FA)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="twoFactorStatus" class="security-status disabled">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-times-circle text-danger me-2"></i>
                                    2FA 未启用
                                </h6>
                                <p class="mb-0 text-muted">您的账户未启用双因素认证，建议启用以提高安全性</p>
                            </div>
                            <button class="btn btn-primary" onclick="startSetup2FA()">
                                <i class="fas fa-plus me-1"></i>启用 2FA
                            </button>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>什么是双因素认证？</strong><br>
                            双因素认证为您的账户增加了额外的安全层。除了密码外，您还需要提供手机应用生成的验证码才能登录。
                        </div>
                    </div>
                </div>

                <!-- 其他安全设置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-key me-2"></i>
                            密码安全
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="security-status">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    密码强度良好
                                </h6>
                                <p class="mb-0 text-muted">上次修改时间：2025-01-15</p>
                            </div>
                            <button class="btn btn-outline-light" onclick="changePassword()">
                                <i class="fas fa-edit me-1"></i>修改密码
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 登录历史 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            登录历史
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>IP地址</th>
                                        <th>设备</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2025-01-27 14:30</td>
                                        <td>*************</td>
                                        <td>Chrome on Windows</td>
                                        <td><span class="badge bg-success">成功</span></td>
                                    </tr>
                                    <tr>
                                        <td>2025-01-26 18:45</td>
                                        <td>*************</td>
                                        <td>Chrome on Windows</td>
                                        <td><span class="badge bg-success">成功</span></td>
                                    </tr>
                                    <tr>
                                        <td>2025-01-25 09:20</td>
                                        <td>*************</td>
                                        <td>Chrome on Windows</td>
                                        <td><span class="badge bg-success">成功</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 2FA设置模态框 -->
    <div class="modal fade" id="setup2FAModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-shield-alt me-2"></i>设置双因素认证
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 步骤指示器 -->
                    <div class="step-indicator">
                        <div class="step active" id="step1">
                            <div class="step-number">1</div>
                            <div class="step-title">下载应用</div>
                        </div>
                        <div class="step" id="step2">
                            <div class="step-number">2</div>
                            <div class="step-title">扫描二维码</div>
                        </div>
                        <div class="step" id="step3">
                            <div class="step-number">3</div>
                            <div class="step-title">验证设置</div>
                        </div>
                    </div>

                    <!-- 步骤1：下载应用 -->
                    <div id="setupStep1" class="setup-step">
                        <h6 class="mb-3">第一步：下载认证应用</h6>
                        <p class="text-muted mb-3">请在您的手机上下载以下任一认证应用：</p>
                        <div class="row">
                            <div class="col-md-4 text-center mb-3">
                                <i class="fab fa-google fa-3x text-primary mb-2"></i>
                                <h6>Google Authenticator</h6>
                                <small class="text-muted">iOS / Android</small>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <i class="fas fa-mobile-alt fa-3x text-success mb-2"></i>
                                <h6>Microsoft Authenticator</h6>
                                <small class="text-muted">iOS / Android</small>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <i class="fas fa-key fa-3x text-warning mb-2"></i>
                                <h6>Authy</h6>
                                <small class="text-muted">iOS / Android</small>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤2：扫描二维码 -->
                    <div id="setupStep2" class="setup-step" style="display: none;">
                        <h6 class="mb-3">第二步：扫描二维码</h6>
                        <p class="text-muted mb-3">使用认证应用扫描下方二维码，或手动输入密钥：</p>

                        <div id="qrCodeContainer" class="qr-code-container">
                            <!-- QR码将在这里显示 -->
                        </div>

                        <div class="alert alert-info">
                            <strong>手动输入密钥：</strong><br>
                            <code id="manualKey" class="text-primary"></code>
                        </div>

                        <div id="backupCodesContainer" class="backup-codes" style="display: none;">
                            <h6 class="mb-2">
                                <i class="fas fa-key me-2"></i>备用验证码
                            </h6>
                            <p class="text-muted small mb-3">
                                请将这些备用验证码保存在安全的地方。当您无法使用认证应用时，可以使用这些代码登录。每个代码只能使用一次。
                            </p>
                            <div id="backupCodesList">
                                <!-- 备用验证码将在这里显示 -->
                            </div>
                        </div>
                    </div>

                    <!-- 步骤3：验证设置 -->
                    <div id="setupStep3" class="setup-step" style="display: none;">
                        <h6 class="mb-3">第三步：验证设置</h6>
                        <p class="text-muted mb-3">请输入认证应用显示的6位验证码：</p>

                        <div class="mb-3">
                            <label class="form-label">验证码</label>
                            <input type="text" class="form-control text-center" id="verificationCode"
                                   placeholder="000000" maxlength="6" style="font-size: 1.5rem; letter-spacing: 0.5rem;">
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>重要提醒：</strong>验证成功后，2FA将立即生效。请确保您已保存备用验证码。
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="nextStepBtn" onclick="nextStep()">下一步</button>
                    <button type="button" class="btn btn-success" id="verifyBtn" onclick="verify2FA()" style="display: none;">
                        <i class="fas fa-check me-1"></i>验证并启用
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 禁用2FA模态框 -->
    <div class="modal fade" id="disable2FAModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>禁用双因素认证
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>安全警告：</strong>禁用双因素认证将降低您账户的安全性。
                    </div>

                    <div class="mb-3">
                        <label class="form-label">确认密码</label>
                        <input type="password" class="form-control" id="disablePassword" placeholder="请输入您的密码">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">2FA验证码</label>
                        <input type="text" class="form-control" id="disableCode" placeholder="请输入6位验证码" maxlength="6">
                        <small class="text-muted">您也可以使用备用验证码</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDisable2FA()">
                        <i class="fas fa-times me-1"></i>确认禁用
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        let setupData = {};

        // 页面加载时检查2FA状态
        document.addEventListener('DOMContentLoaded', function() {
            check2FAStatus();
        });

        // 检查2FA状态
        async function check2FAStatus() {
            try {
                const response = await fetch('/auth/2fa/status');
                const result = await response.json();

                if (result.success) {
                    update2FAStatus(result.enabled, result.backup_codes_count);
                }
            } catch (error) {
                console.error('检查2FA状态失败:', error);
            }
        }

        // 更新2FA状态显示
        function update2FAStatus(enabled, backupCodesCount = 0) {
            const statusDiv = document.getElementById('twoFactorStatus');

            if (enabled) {
                statusDiv.className = 'security-status enabled';
                statusDiv.innerHTML = `
                    <div>
                        <h6 class="mb-1">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            2FA 已启用
                        </h6>
                        <p class="mb-0 text-muted">您的账户已启用双因素认证，剩余备用验证码：${backupCodesCount} 个</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-light me-2" onclick="regenerateBackupCodes()">
                            <i class="fas fa-sync me-1"></i>重新生成备用码
                        </button>
                        <button class="btn btn-danger" onclick="startDisable2FA()">
                            <i class="fas fa-times me-1"></i>禁用 2FA
                        </button>
                    </div>
                `;
            } else {
                statusDiv.className = 'security-status disabled';
                statusDiv.innerHTML = `
                    <div>
                        <h6 class="mb-1">
                            <i class="fas fa-times-circle text-danger me-2"></i>
                            2FA 未启用
                        </h6>
                        <p class="mb-0 text-muted">您的账户未启用双因素认证，建议启用以提高安全性</p>
                    </div>
                    <button class="btn btn-primary" onclick="startSetup2FA()">
                        <i class="fas fa-plus me-1"></i>启用 2FA
                    </button>
                `;
            }
        }

        // 开始设置2FA
        function startSetup2FA() {
            currentStep = 1;
            updateStepIndicator();
            new bootstrap.Modal(document.getElementById('setup2FAModal')).show();
        }

        // 下一步
        async function nextStep() {
            if (currentStep === 1) {
                // 从步骤1到步骤2，获取QR码
                try {
                    const response = await fetch('/auth/2fa/setup', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        setupData = result;

                        // 显示QR码
                        document.getElementById('qrCodeContainer').innerHTML =
                            `<img src="${result.qr_code}" alt="QR Code" style="max-width: 200px;">`;

                        // 显示手动输入密钥
                        document.getElementById('manualKey').textContent = result.manual_entry_key;

                        // 显示备用验证码
                        const backupCodesDiv = document.getElementById('backupCodesList');
                        backupCodesDiv.innerHTML = result.backup_codes.map(code =>
                            `<span class="backup-code">${code}</span>`
                        ).join('');
                        document.getElementById('backupCodesContainer').style.display = 'block';

                        currentStep = 2;
                        updateStepIndicator();
                    } else {
                        showNotification(result.error, 'error');
                    }
                } catch (error) {
                    showNotification('设置2FA失败，请稍后重试', 'error');
                }
            } else if (currentStep === 2) {
                // 从步骤2到步骤3
                currentStep = 3;
                updateStepIndicator();
            }
        }

        // 验证2FA
        async function verify2FA() {
            const code = document.getElementById('verificationCode').value;

            if (!code || code.length !== 6) {
                showNotification('请输入6位验证码', 'error');
                return;
            }

            try {
                const response = await fetch('/auth/2fa/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ code: code })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('2FA已成功启用！', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('setup2FAModal')).hide();
                    check2FAStatus(); // 刷新状态
                } else {
                    showNotification(result.error, 'error');
                }
            } catch (error) {
                showNotification('验证失败，请稍后重试', 'error');
            }
        }

        // 更新步骤指示器
        function updateStepIndicator() {
            // 重置所有步骤
            for (let i = 1; i <= 3; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');

                if (i < currentStep) {
                    step.classList.add('completed');
                } else if (i === currentStep) {
                    step.classList.add('active');
                }
            }

            // 显示/隐藏步骤内容
            for (let i = 1; i <= 3; i++) {
                const stepContent = document.getElementById(`setupStep${i}`);
                stepContent.style.display = i === currentStep ? 'block' : 'none';
            }

            // 更新按钮
            const nextBtn = document.getElementById('nextStepBtn');
            const verifyBtn = document.getElementById('verifyBtn');

            if (currentStep === 3) {
                nextBtn.style.display = 'none';
                verifyBtn.style.display = 'inline-block';
            } else {
                nextBtn.style.display = 'inline-block';
                verifyBtn.style.display = 'none';
            }
        }

        // 开始禁用2FA
        function startDisable2FA() {
            new bootstrap.Modal(document.getElementById('disable2FAModal')).show();
        }

        // 确认禁用2FA
        async function confirmDisable2FA() {
            const password = document.getElementById('disablePassword').value;
            const code = document.getElementById('disableCode').value;

            if (!password) {
                showNotification('请输入密码', 'error');
                return;
            }

            if (!code) {
                showNotification('请输入2FA验证码', 'error');
                return;
            }

            try {
                const response = await fetch('/auth/2fa/disable', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ password: password, code: code })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('2FA已禁用', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('disable2FAModal')).hide();
                    check2FAStatus(); // 刷新状态
                } else {
                    showNotification(result.error, 'error');
                }
            } catch (error) {
                showNotification('禁用失败，请稍后重试', 'error');
            }
        }

        // 重新生成备用验证码
        async function regenerateBackupCodes() {
            const password = prompt('请输入您的密码以重新生成备用验证码：');

            if (!password) {
                return;
            }

            try {
                const response = await fetch('/auth/2fa/backup-codes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ password: password })
                });

                const result = await response.json();

                if (result.success) {
                    // 显示新的备用验证码
                    const codes = result.backup_codes.join('\n');
                    alert(`新的备用验证码：\n\n${codes}\n\n请将这些代码保存在安全的地方！`);
                    check2FAStatus(); // 刷新状态
                } else {
                    showNotification(result.error, 'error');
                }
            } catch (error) {
                showNotification('生成失败，请稍后重试', 'error');
            }
        }

        // 修改密码
        function changePassword() {
            showNotification('密码修改功能开发中...', 'info');
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST'
                });

                if (response.ok) {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
