<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时数据流测试 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            padding: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .connection-status {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .status-connected {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: var(--success);
        }

        .status-disconnected {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: var(--danger);
        }

        .status-connecting {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            color: var(--warning);
        }

        .market-data-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .market-data-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .price-positive {
            color: var(--success);
        }

        .price-negative {
            color: var(--danger);
        }

        .price-neutral {
            color: var(--text-secondary);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-select option {
            background: var(--dark-card);
            color: var(--text-primary);
            padding: 0.5rem;
        }

        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }

        .log-info {
            color: var(--accent);
        }

        .log-success {
            color: var(--success);
        }

        .log-error {
            color: var(--danger);
        }

        .log-warning {
            color: var(--warning);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            color: white;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 800;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .subscription-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    
        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="text-center mb-4">
                    <h1 class="display-5 mb-3">
                        <i class="fas fa-broadcast-tower text-primary me-3"></i>
                        实时数据流测试
                    </h1>
                    <p class="lead">WebSocket实时市场数据推送系统演示</p>
                </div>

                <!-- 连接状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-wifi me-2"></i>
                            连接状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="connectionStatus" class="connection-status status-disconnected">
                            <i class="fas fa-times-circle me-2"></i>
                            未连接
                        </div>
                        <div class="text-center">
                            <button class="btn btn-primary me-2" onclick="connectWebSocket()">
                                <i class="fas fa-plug me-1"></i>连接
                            </button>
                            <button class="btn btn-outline-light me-2" onclick="disconnectWebSocket()">
                                <i class="fas fa-unlink me-1"></i>断开
                            </button>
                            <button class="btn btn-outline-success" onclick="getStats()">
                                <i class="fas fa-chart-bar me-1"></i>获取统计
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 订阅管理 -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-rss me-2"></i>
                                    订阅管理
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">交易品种代码</label>
                                    <input type="text" class="form-control" id="symbolInput" placeholder="股票: AAPL, GOOGL | 加密货币: BTC, ETH" value="AAPL">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">数据类型</label>
                                    <select class="form-select" id="dataTypeSelect">
                                        <option value="stock">美股市场</option>
                                        <option value="crypto">加密货币</option>
                                    </select>
                                </div>
                                <div class="text-center">
                                    <button class="btn btn-primary me-2" onclick="subscribe()">
                                        <i class="fas fa-plus me-1"></i>订阅
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="unsubscribe()">
                                        <i class="fas fa-minus me-1"></i>取消订阅
                                    </button>
                                </div>

                                <hr>

                                <h6>当前订阅</h6>
                                <div id="subscriptionsList">
                                    <p class="text-muted">暂无订阅</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    服务统计
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="stats-grid">
                                    <div class="stat-card">
                                        <div class="stat-number" id="totalConnections">0</div>
                                        <div class="stat-label">总连接数</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number" id="totalSubscriptions">0</div>
                                        <div class="stat-label">总订阅数</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number" id="uniqueSymbols">0</div>
                                        <div class="stat-label">订阅品种</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number" id="cacheSize">0</div>
                                        <div class="stat-label">缓存大小</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时数据显示 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            实时市场数据
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="marketDataContainer">
                            <p class="text-muted text-center">暂无数据，请先订阅股票</p>
                        </div>
                    </div>
                </div>

                <!-- 日志 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            连接日志
                        </h5>
                        <button class="btn btn-outline-light btn-sm" onclick="clearLogs()">
                            <i class="fas fa-trash me-1"></i>清空
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="logContainer" class="log-container">
                            <!-- 日志将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 返回按钮 -->
                <div class="text-center">
                    <a href="/dashboard" class="btn btn-primary btn-lg">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        返回仪表板
                    </a>
                    <a href="/" class="btn btn-outline-light btn-lg ms-3">
                        <i class="fas fa-home me-2"></i>
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script>
        let socket = null;
        let subscriptions = new Set();
        let marketData = {};

        // 连接WebSocket
        function connectWebSocket() {
            if (socket && socket.connected) {
                addLog('已经连接', 'warning');
                return;
            }

            updateConnectionStatus('connecting');
            addLog('正在连接WebSocket...', 'info');

            socket = io();

            socket.on('connect', function() {
                updateConnectionStatus('connected');
                addLog('WebSocket连接成功', 'success');
            });

            socket.on('disconnect', function() {
                updateConnectionStatus('disconnected');
                addLog('WebSocket连接断开', 'error');
            });

            socket.on('connected', function(data) {
                addLog(`服务器确认: ${data.message}`, 'success');
            });

            socket.on('subscribed', function(data) {
                subscriptions.add(`${data.type}:${data.symbol}`);
                updateSubscriptionsList();
                addLog(`订阅成功: ${data.symbol} (${data.type})`, 'success');
            });

            socket.on('unsubscribed', function(data) {
                subscriptions.delete(`${data.type}:${data.symbol}`);
                updateSubscriptionsList();
                addLog(`取消订阅: ${data.symbol} (${data.type})`, 'info');
            });

            socket.on('market_data', function(data) {
                updateMarketData(data);
                addLog(`收到数据: ${data.symbol} - $${data.data.price.toFixed(2)}`, 'info');
            });

            socket.on('stats', function(data) {
                updateStats(data.data);
                addLog('统计信息已更新', 'info');
            });

            socket.on('error', function(data) {
                addLog(`错误: ${data.message}`, 'error');
            });

            socket.on('pong', function(data) {
                addLog('心跳响应正常', 'info');
            });
        }

        // 断开WebSocket
        function disconnectWebSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
                subscriptions.clear();
                updateSubscriptionsList();
                updateConnectionStatus('disconnected');
                addLog('已断开连接', 'info');
            }
        }

        // 订阅
        function subscribe() {
            if (!socket || !socket.connected) {
                addLog('请先连接WebSocket', 'error');
                return;
            }

            const symbol = document.getElementById('symbolInput').value.trim().toUpperCase();
            const dataType = document.getElementById('dataTypeSelect').value;

            if (!symbol) {
                addLog('请输入交易品种代码', 'error');
                return;
            }

            socket.emit('subscribe', {
                symbol: symbol,
                type: dataType
            });
        }

        // 取消订阅
        function unsubscribe() {
            if (!socket || !socket.connected) {
                addLog('请先连接WebSocket', 'error');
                return;
            }

            const symbol = document.getElementById('symbolInput').value.trim().toUpperCase();
            const dataType = document.getElementById('dataTypeSelect').value;

            if (!symbol) {
                addLog('请输入交易品种代码', 'error');
                return;
            }

            socket.emit('unsubscribe', {
                symbol: symbol,
                type: dataType
            });
        }

        // 获取统计
        function getStats() {
            if (!socket || !socket.connected) {
                addLog('请先连接WebSocket', 'error');
                return;
            }

            socket.emit('get_stats');
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.className = `connection-status status-${status}`;

            switch (status) {
                case 'connected':
                    statusDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>已连接';
                    break;
                case 'connecting':
                    statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>连接中...';
                    break;
                case 'disconnected':
                    statusDiv.innerHTML = '<i class="fas fa-times-circle me-2"></i>未连接';
                    break;
            }
        }

        // 更新订阅列表
        function updateSubscriptionsList() {
            const container = document.getElementById('subscriptionsList');

            if (subscriptions.size === 0) {
                container.innerHTML = '<p class="text-muted">暂无订阅</p>';
                return;
            }

            container.innerHTML = Array.from(subscriptions).map(sub => {
                const [type, symbol] = sub.split(':');
                return `
                    <div class="subscription-item">
                        <span>${symbol} (${type})</span>
                        <button class="btn btn-outline-danger btn-sm" onclick="unsubscribeSymbol('${symbol}', '${type}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            }).join('');
        }

        // 取消特定订阅
        function unsubscribeSymbol(symbol, type) {
            if (socket && socket.connected) {
                socket.emit('unsubscribe', { symbol: symbol, type: type });
            }
        }

        // 更新市场数据
        function updateMarketData(data) {
            marketData[data.symbol] = data;

            const container = document.getElementById('marketDataContainer');

            if (Object.keys(marketData).length === 0) {
                container.innerHTML = '<p class="text-muted text-center">暂无数据，请先订阅交易品种</p>';
                return;
            }

            container.innerHTML = Object.values(marketData).map(item => {
                const changeClass = item.data.change > 0 ? 'price-positive' :
                                   item.data.change < 0 ? 'price-negative' : 'price-neutral';
                const changeIcon = item.data.change > 0 ? 'fa-arrow-up' :
                                  item.data.change < 0 ? 'fa-arrow-down' : 'fa-minus';

                return `
                    <div class="market-data-card pulse">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1">${item.symbol}</h6>
                                <small class="text-muted">${item.type}</small>
                            </div>
                            <div class="col-md-3">
                                <div class="h5 mb-0 ${changeClass}">$${item.data.price.toFixed(2)}</div>
                            </div>
                            <div class="col-md-3">
                                <div class="${changeClass}">
                                    <i class="fas ${changeIcon} me-1"></i>
                                    ${item.data.change.toFixed(2)} (${item.data.change_percent.toFixed(2)}%)
                                </div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">
                                    成交量: ${item.data.volume.toLocaleString()}<br>
                                    更新: ${new Date(item.timestamp).toLocaleTimeString()}
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 更新统计信息
        function updateStats(stats) {
            document.getElementById('totalConnections').textContent = stats.total_connections;
            document.getElementById('totalSubscriptions').textContent = stats.total_subscriptions;
            document.getElementById('uniqueSymbols').textContent = stats.unique_symbols;
            document.getElementById('cacheSize').textContent = stats.cache_size;
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;

            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 页面加载完成后自动连接
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，准备连接WebSocket', 'info');
            // 可以选择自动连接
            // connectWebSocket();
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (socket) {
                socket.disconnect();
            }
        });
    </script>
</body>
</html>
