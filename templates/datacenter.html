<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中心 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px);
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
        }

        .card-header {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .watchlist-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .watchlist-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .price-positive {
            color: #28a745;
        }

        .price-negative {
            color: #dc3545;
        }

        .price-neutral {
            color: #6c757d;
        }

        .btn-add-watchlist {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
        }

        .btn-add-watchlist:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
        }

        .search-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: #28a745;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .form-select option {
            background: #343a40;
            color: white;
        }

        .stats-card {
            background: linear-gradient(45deg, rgba(40, 167, 69, 0.2), rgba(32, 201, 151, 0.2));
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .loading-spinner {
            display: none;
        }

        .text-white {
            color: white !important;
        }

        .text-muted {
            color: rgba(255, 255, 255, 0.7) !important;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">策略市场</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">回测系统</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/datacenter">数据中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">社区论坛</a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarNav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-white mb-3">
                    <i class="fas fa-database me-2"></i>数据中心
                </h2>
                <p class="text-muted">管理您的关注列表，获取实时市场数据</p>
            </div>
        </div>

        <!-- 添加关注品种 -->
        <div class="search-container">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" class="form-control" id="symbolInput" placeholder="输入交易品种代码 (如: AAPL, BTC-USD)">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="assetType">
                        <option value="stock">股票</option>
                        <option value="crypto">加密货币</option>
                        <option value="forex">外汇</option>
                        <option value="futures">期货</option>
                        <option value="gold">黄金</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" id="nameInput" placeholder="名称 (可选)">
                </div>
                <div class="col-md-2">
                    <button class="btn btn-add-watchlist w-100" onclick="addToWatchlist()">
                        <i class="fas fa-plus me-1"></i>添加关注
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="text-white" id="totalWatchlist">0</h5>
                        <p class="text-muted mb-0">关注品种</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="text-white" id="gainersCount">0</h5>
                        <p class="text-muted mb-0">上涨品种</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="text-white" id="losersCount">0</h5>
                        <p class="text-muted mb-0">下跌品种</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="text-white" id="lastUpdate">--</h5>
                        <p class="text-muted mb-0">最后更新</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关注列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="text-white mb-0">
                    <i class="fas fa-star me-2"></i>我的关注列表
                </h5>
                <div>
                    <button class="btn btn-outline-light btn-sm me-2" onclick="refreshWatchlist()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="clearWatchlist()">
                        <i class="fas fa-trash me-1"></i>清空
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="watchlistContainer">
                    <div class="text-center text-muted">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                            <p>加载中...</p>
                        </div>
                        <div id="emptyMessage">
                            <i class="fas fa-star fa-3x mb-3"></i>
                            <p>暂无关注品种，请添加您感兴趣的交易品种</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let watchlistData = [];
        let currentUser = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            loadWatchlist();
        });

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/auth/status');
                const result = await response.json();

                if (result.success && result.user) {
                    currentUser = result.user;
                    updateNavbar(result.user);
                } else {
                    updateNavbar(null);
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                updateNavbar(null);
            }
        }

        // 更新导航栏
        function updateNavbar(user) {
            const navbarNav = document.getElementById('navbarNav');

            if (user) {
                navbarNav.innerHTML = `
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>${user.username}
                            ${user.is_premium ? '<span class="badge bg-warning ms-1">VIP</span>' : ''}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>仪表板</a></li>
                            <li><a class="dropdown-item" href="/strategy-editor"><i class="fas fa-code me-2"></i>策略开发</a></li>
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                `;
            } else {
                navbarNav.innerHTML = `
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                `;
            }
        }

        // 加载关注列表
        async function loadWatchlist() {
            if (!currentUser) {
                document.getElementById('watchlistContainer').innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-sign-in-alt fa-3x mb-3"></i>
                        <p>请先登录以查看您的关注列表</p>
                        <button class="btn btn-outline-light" onclick="showLogin()">立即登录</button>
                    </div>
                `;
                return;
            }

            showLoading(true);

            try {
                const response = await fetch('/api/watchlist');
                const result = await response.json();

                if (result.success) {
                    watchlistData = result.data;
                    displayWatchlist(watchlistData);
                    updateStats(watchlistData);
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
                } else {
                    showError(result.error || '加载关注列表失败');
                }
            } catch (error) {
                console.error('加载关注列表失败:', error);
                showError('网络错误，请稍后重试');
            } finally {
                showLoading(false);
            }
        }

        // 显示关注列表
        function displayWatchlist(data) {
            const container = document.getElementById('watchlistContainer');

            if (data.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-star fa-3x mb-3"></i>
                        <p>暂无关注品种，请添加您感兴趣的交易品种</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = data.map(item => {
                const changeClass = item.change > 0 ? 'price-positive' :
                                  item.change < 0 ? 'price-negative' : 'price-neutral';
                const changeIcon = item.change > 0 ? 'fa-arrow-up' :
                                 item.change < 0 ? 'fa-arrow-down' : 'fa-minus';

                return `
                    <div class="watchlist-item">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <div>
                                        <h6 class="text-white mb-1">${item.symbol}</h6>
                                        <small class="text-muted">${item.name} • ${item.type}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-white">
                                    <strong>$${item.price.toFixed(2)}</strong>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="${changeClass}">
                                    <i class="fas ${changeIcon} me-1"></i>
                                    ${item.change.toFixed(2)}
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="${changeClass}">
                                    ${item.change_pct.toFixed(2)}%
                                </div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">
                                    ${new Date(item.added_at).toLocaleDateString()}
                                </small>
                            </div>
                            <div class="col-md-1">
                                <button class="btn btn-outline-danger btn-sm"
                                        onclick="removeFromWatchlist('${item.symbol}', '${item.type}')"
                                        title="移除关注">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        ${item.error ? `<div class="mt-2"><small class="text-warning">${item.error}</small></div>` : ''}
                        ${item.note ? `<div class="mt-2"><small class="text-info">${item.note}</small></div>` : ''}
                    </div>
                `;
            }).join('');
        }

        // 更新统计信息
        function updateStats(data) {
            const total = data.length;
            const gainers = data.filter(item => item.change > 0).length;
            const losers = data.filter(item => item.change < 0).length;

            document.getElementById('totalWatchlist').textContent = total;
            document.getElementById('gainersCount').textContent = gainers;
            document.getElementById('losersCount').textContent = losers;
        }

        // 添加到关注列表
        async function addToWatchlist() {
            if (!currentUser) {
                showNotification('请先登录', 'warning');
                return;
            }

            const symbol = document.getElementById('symbolInput').value.trim().toUpperCase();
            const type = document.getElementById('assetType').value;
            const name = document.getElementById('nameInput').value.trim() || symbol;

            if (!symbol) {
                showNotification('请输入交易品种代码', 'error');
                return;
            }

            try {
                const response = await fetch('/api/watchlist', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ symbol, type, name })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(result.message, 'success');

                    // 清空输入框
                    document.getElementById('symbolInput').value = '';
                    document.getElementById('nameInput').value = '';

                    // 重新加载关注列表
                    loadWatchlist();
                } else {
                    showNotification(result.error || '添加失败', 'error');
                }
            } catch (error) {
                console.error('添加关注失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 从关注列表移除
        async function removeFromWatchlist(symbol, type) {
            if (!currentUser) {
                showNotification('请先登录', 'warning');
                return;
            }

            if (!confirm(`确定要移除 ${symbol} 吗？`)) {
                return;
            }

            try {
                const response = await fetch('/api/watchlist', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ symbol, type })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(result.message, 'success');
                    loadWatchlist();
                } else {
                    showNotification(result.error || '移除失败', 'error');
                }
            } catch (error) {
                console.error('移除关注失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 刷新关注列表
        function refreshWatchlist() {
            loadWatchlist();
        }

        // 清空关注列表
        async function clearWatchlist() {
            if (!currentUser) {
                showNotification('请先登录', 'warning');
                return;
            }

            if (!confirm('确定要清空所有关注品种吗？此操作不可撤销。')) {
                return;
            }

            try {
                // 逐个删除所有关注品种
                for (const item of watchlistData) {
                    await fetch('/api/watchlist', {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ symbol: item.symbol, type: item.type })
                    });
                }

                showNotification('已清空关注列表', 'success');
                loadWatchlist();
            } catch (error) {
                console.error('清空关注列表失败:', error);
                showNotification('清空失败，请稍后重试', 'error');
            }
        }

        // 显示加载状态
        function showLoading(show) {
            const spinner = document.querySelector('.loading-spinner');
            const emptyMessage = document.getElementById('emptyMessage');

            if (show) {
                spinner.style.display = 'block';
                emptyMessage.style.display = 'none';
            } else {
                spinner.style.display = 'none';
                emptyMessage.style.display = 'block';
            }
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('watchlistContainer').innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
                    <p>${message}</p>
                    <button class="btn btn-outline-light" onclick="loadWatchlist()">重试</button>
                </div>
            `;
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // 显示登录模态框
        function showLogin() {
            window.location.href = '/';
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST'
                });

                if (response.ok) {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        }

        // 键盘事件处理
        document.getElementById('symbolInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addToWatchlist();
            }
        });

        document.getElementById('nameInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addToWatchlist();
            }
        });
    </script>
</body>
</html>
