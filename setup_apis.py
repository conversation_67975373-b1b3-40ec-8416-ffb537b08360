#!/usr/bin/env python3
# QuantTradeX API设置向导
# 帮助用户快速配置API集成

import os
import sys
import json
import requests
from datetime import datetime
from api_config_template import APIConfig, create_env_file, validate_configuration

class APISetupWizard:
    """API设置向导"""
    
    def __init__(self):
        self.config_updated = False
        
    def welcome(self):
        """欢迎信息"""
        print("🚀 欢迎使用QuantTradeX API设置向导")
        print("=" * 50)
        print("本向导将帮助您:")
        print("1. 了解需要申请的API")
        print("2. 配置API Key")
        print("3. 测试API连接")
        print("4. 启动QuantTradeX平台")
        print("=" * 50)
        
    def check_requirements(self):
        """检查系统要求"""
        print("\n🔍 检查系统要求...")
        
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("❌ 需要Python 3.7或更高版本")
            return False
        else:
            print(f"✅ Python版本: {sys.version}")
        
        # 检查必要的包
        required_packages = ['requests', 'flask', 'redis']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package} 未安装")
        
        if missing_packages:
            print(f"\n📦 请安装缺少的包:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        return True
    
    def show_api_guide(self):
        """显示API申请指南"""
        print("\n📋 API申请指南")
        print("-" * 30)
        
        apis = [
            {
                'name': 'Alpha Vantage (股票数据)',
                'url': 'https://www.alphavantage.co/support/#api-key',
                'free_limit': '500次/天',
                'difficulty': '⭐⭐ 简单',
                'required': True
            },
            {
                'name': 'CoinGecko (数字货币)',
                'url': 'https://www.coingecko.com/en/api',
                'free_limit': '10,000次/月',
                'difficulty': '⭐⭐ 简单',
                'required': True
            },
            {
                'name': 'ExchangeRate-API (外汇)',
                'url': 'https://exchangerate-api.com/',
                'free_limit': '1,500次/月',
                'difficulty': '⭐ 非常简单',
                'required': False
            },
            {
                'name': 'Quandl (期货数据)',
                'url': 'https://data.nasdaq.com/',
                'free_limit': '50次/天',
                'difficulty': '⭐⭐⭐ 中等',
                'required': False
            }
        ]
        
        for i, api in enumerate(apis, 1):
            required_text = "【必需】" if api['required'] else "【可选】"
            print(f"\n{i}. {api['name']} {required_text}")
            print(f"   🔗 申请地址: {api['url']}")
            print(f"   💰 免费额度: {api['free_limit']}")
            print(f"   📊 难度: {api['difficulty']}")
        
        print(f"\n📖 详细申请步骤请查看: API申请教程.md")
        
    def setup_env_file(self):
        """设置环境变量文件"""
        print("\n⚙️ 配置API Key")
        print("-" * 20)
        
        # 创建.env文件模板
        create_env_file()
        
        # 检查是否已有配置
        status = APIConfig.check_api_keys()
        
        if any(status.values()):
            print("✅ 检测到已有API配置")
            for api, configured in status.items():
                status_icon = "✅" if configured else "❌"
                print(f"  {status_icon} {api.replace('_', ' ').title()}")
        else:
            print("⚠️ 未检测到API配置")
        
        # 询问是否要手动输入API Key
        if input("\n是否要现在输入API Key? (y/n): ").lower() == 'y':
            self.input_api_keys()
        else:
            print("📝 请手动编辑 .env 文件添加您的API Key")
    
    def input_api_keys(self):
        """手动输入API Key"""
        print("\n📝 请输入您的API Key (按回车跳过):")
        
        api_keys = {}
        
        # Alpha Vantage
        key = input("Alpha Vantage API Key: ").strip()
        if key:
            api_keys['ALPHA_VANTAGE_API_KEY'] = key
        
        # CoinGecko
        key = input("CoinGecko API Key: ").strip()
        if key:
            api_keys['COINGECKO_API_KEY'] = key
        
        # ExchangeRate-API
        key = input("ExchangeRate-API Key: ").strip()
        if key:
            api_keys['EXCHANGERATE_API_KEY'] = key
        
        # Quandl
        key = input("Quandl API Key: ").strip()
        if key:
            api_keys['QUANDL_API_KEY'] = key
        
        if api_keys:
            self.update_env_file(api_keys)
            self.config_updated = True
        
    def update_env_file(self, api_keys):
        """更新.env文件"""
        try:
            # 读取现有.env文件
            env_content = ""
            if os.path.exists('.env'):
                with open('.env', 'r') as f:
                    env_content = f.read()
            
            # 更新API Key
            for key, value in api_keys.items():
                if f"{key}=" in env_content:
                    # 替换现有的key
                    lines = env_content.split('\n')
                    for i, line in enumerate(lines):
                        if line.startswith(f"{key}="):
                            lines[i] = f"{key}={value}"
                    env_content = '\n'.join(lines)
                else:
                    # 添加新的key
                    env_content += f"\n{key}={value}"
            
            # 写入文件
            with open('.env', 'w') as f:
                f.write(env_content)
            
            print("✅ API Key已保存到.env文件")
            
        except Exception as e:
            print(f"❌ 保存API Key失败: {e}")
    
    def test_apis(self):
        """测试API连接"""
        print("\n🧪 测试API连接...")
        print("-" * 20)
        
        # 重新加载配置
        if self.config_updated:
            print("🔄 重新加载配置...")
            # 这里需要重新导入配置
        
        from api_service import api_service
        
        # 健康检查
        health = api_service.health_check()
        
        print("📊 API配置状态:")
        for api, info in health['apis'].items():
            status_icon = "✅" if info['configured'] else "❌"
            print(f"  {status_icon} {api.replace('_', ' ').title()}: {info['status']}")
        
        # 测试实际API调用
        if health['apis']['alpha_vantage']['configured']:
            print("\n📈 测试股票数据API...")
            try:
                stock_data = api_service.get_stock_quote('AAPL')
                if stock_data:
                    print(f"✅ 成功获取AAPL数据: ${stock_data['price']}")
                else:
                    print("❌ 股票数据获取失败")
            except Exception as e:
                print(f"❌ 股票API测试失败: {e}")
        
        if health['apis']['coingecko']['configured']:
            print("\n🪙 测试数字货币API...")
            try:
                crypto_data = api_service.get_crypto_price('bitcoin')
                if crypto_data:
                    print(f"✅ 成功获取Bitcoin价格: ${crypto_data['price']:,.2f}")
                else:
                    print("❌ 数字货币数据获取失败")
            except Exception as e:
                print(f"❌ 数字货币API测试失败: {e}")
        
        if health['apis']['exchangerate']['configured']:
            print("\n💱 测试外汇API...")
            try:
                forex_data = api_service.get_exchange_rate('USD', 'EUR')
                if forex_data:
                    print(f"✅ 成功获取USD/EUR汇率: {forex_data['rate']}")
                else:
                    print("❌ 外汇数据获取失败")
            except Exception as e:
                print(f"❌ 外汇API测试失败: {e}")
    
    def show_next_steps(self):
        """显示下一步操作"""
        print("\n🎯 下一步操作")
        print("-" * 15)
        
        status = APIConfig.check_api_keys()
        missing = APIConfig.get_missing_apis()
        
        if missing:
            print("📋 待完成任务:")
            for api in missing:
                print(f"  ❌ 申请 {api.replace('_', ' ').title()} API Key")
            print(f"\n📖 详细申请步骤: 查看 API申请教程.md")
        
        if APIConfig.is_production_ready():
            print("✅ 基础API已配置完成，可以启动平台!")
            print("\n🚀 启动命令:")
            print("  python app.py")
        else:
            print("⚠️ 需要至少配置股票和数字货币API才能正常运行")
        
        print(f"\n📊 当前配置状态:")
        for api, configured in status.items():
            status_icon = "✅" if configured else "❌"
            print(f"  {status_icon} {api.replace('_', ' ').title()}")
    
    def run(self):
        """运行设置向导"""
        self.welcome()
        
        # 检查系统要求
        if not self.check_requirements():
            print("\n❌ 系统要求检查失败，请解决上述问题后重试")
            return
        
        # 显示API申请指南
        self.show_api_guide()
        
        # 设置环境变量
        self.setup_env_file()
        
        # 测试API
        self.test_apis()
        
        # 显示下一步操作
        self.show_next_steps()
        
        print(f"\n🎉 设置向导完成!")
        print(f"📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    wizard = APISetupWizard()
    wizard.run()

if __name__ == '__main__':
    main()
