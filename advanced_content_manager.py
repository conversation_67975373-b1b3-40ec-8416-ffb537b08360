#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantTradeX 高级内容管理系统
支持大规模内容生成、去重、数据库存储和增量更新
"""

import json
import hashlib
import sqlite3
import psycopg2
from datetime import datetime, timedelta
import random
import os
from typing import List, Dict, Set, Optional

class ContentManager:
    def __init__(self, storage_type='database', db_config=None):
        """
        初始化内容管理器
        
        Args:
            storage_type: 'json' 或 'database'
            db_config: 数据库配置 (PostgreSQL)
        """
        self.storage_type = storage_type
        self.db_config = db_config or {
            'host': 'localhost',
            'database': 'quanttradex',
            'user': 'postgres',
            'password': 'your_password'
        }
        
        # 内容去重缓存
        self.strategy_hashes = set()
        self.post_hashes = set()
        
        # 初始化存储
        self._init_storage()
        self._load_existing_hashes()
    
    def _init_storage(self):
        """初始化存储系统"""
        if self.storage_type == 'database':
            self._init_database()
        else:
            self._init_json_storage()
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 创建策略表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategies (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    code TEXT NOT NULL,
                    description TEXT,
                    type VARCHAR(100),
                    indicator VARCHAR(100),
                    timeframe VARCHAR(50),
                    asset_class VARCHAR(100),
                    risk_level VARCHAR(50),
                    complexity VARCHAR(50),
                    content_hash VARCHAR(64) UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建论坛帖子表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS forum_posts (
                    id SERIAL PRIMARY KEY,
                    title VARCHAR(500) NOT NULL,
                    content TEXT NOT NULL,
                    author VARCHAR(100),
                    category VARCHAR(100),
                    tags TEXT[], -- PostgreSQL数组类型
                    views INTEGER DEFAULT 0,
                    likes INTEGER DEFAULT 0,
                    reply_count INTEGER DEFAULT 0,
                    is_pinned BOOLEAN DEFAULT FALSE,
                    is_locked BOOLEAN DEFAULT FALSE,
                    content_hash VARCHAR(64) UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建内容哈希索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategies_hash ON strategies(content_hash)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_posts_hash ON forum_posts(content_hash)")
            
            conn.commit()
            cursor.close()
            conn.close()
            print("✅ 数据库表初始化完成")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
    
    def _init_json_storage(self):
        """初始化JSON存储"""
        if not os.path.exists('strategies_db.json'):
            with open('strategies_db.json', 'w', encoding='utf-8') as f:
                json.dump({'strategies': {}, 'hashes': []}, f, ensure_ascii=False, indent=2)
        
        if not os.path.exists('forum_posts_db.json'):
            with open('forum_posts_db.json', 'w', encoding='utf-8') as f:
                json.dump({'posts': [], 'hashes': []}, f, ensure_ascii=False, indent=2)
    
    def _load_existing_hashes(self):
        """加载已存在的内容哈希"""
        if self.storage_type == 'database':
            self._load_hashes_from_db()
        else:
            self._load_hashes_from_json()
    
    def _load_hashes_from_db(self):
        """从数据库加载哈希"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 加载策略哈希
            cursor.execute("SELECT content_hash FROM strategies WHERE content_hash IS NOT NULL")
            self.strategy_hashes = {row[0] for row in cursor.fetchall()}
            
            # 加载帖子哈希
            cursor.execute("SELECT content_hash FROM forum_posts WHERE content_hash IS NOT NULL")
            self.post_hashes = {row[0] for row in cursor.fetchall()}
            
            cursor.close()
            conn.close()
            
            print(f"📊 已加载 {len(self.strategy_hashes)} 个策略哈希, {len(self.post_hashes)} 个帖子哈希")
            
        except Exception as e:
            print(f"❌ 加载哈希失败: {e}")
    
    def _load_hashes_from_json(self):
        """从JSON文件加载哈希"""
        try:
            with open('strategies_db.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.strategy_hashes = set(data.get('hashes', []))
            
            with open('forum_posts_db.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.post_hashes = set(data.get('hashes', []))
                
            print(f"📊 已加载 {len(self.strategy_hashes)} 个策略哈希, {len(self.post_hashes)} 个帖子哈希")
            
        except Exception as e:
            print(f"❌ 加载哈希失败: {e}")
    
    def _generate_content_hash(self, content: str) -> str:
        """生成内容哈希"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def is_duplicate_strategy(self, strategy_data: Dict) -> bool:
        """检查策略是否重复"""
        # 基于名称和核心代码生成哈希
        content = f"{strategy_data['name']}_{strategy_data['code'][:200]}"
        content_hash = self._generate_content_hash(content)
        return content_hash in self.strategy_hashes
    
    def is_duplicate_post(self, post_data: Dict) -> bool:
        """检查帖子是否重复"""
        # 基于标题和内容前200字符生成哈希
        content = f"{post_data['title']}_{post_data['content'][:200]}"
        content_hash = self._generate_content_hash(content)
        return content_hash in self.post_hashes
    
    def add_strategy(self, strategy_data: Dict) -> bool:
        """添加策略（自动去重）"""
        if self.is_duplicate_strategy(strategy_data):
            return False
        
        # 生成哈希
        content = f"{strategy_data['name']}_{strategy_data['code'][:200]}"
        content_hash = self._generate_content_hash(content)
        strategy_data['content_hash'] = content_hash
        
        if self.storage_type == 'database':
            return self._add_strategy_to_db(strategy_data)
        else:
            return self._add_strategy_to_json(strategy_data)
    
    def add_post(self, post_data: Dict) -> bool:
        """添加帖子（自动去重）"""
        if self.is_duplicate_post(post_data):
            return False
        
        # 生成哈希
        content = f"{post_data['title']}_{post_data['content'][:200]}"
        content_hash = self._generate_content_hash(content)
        post_data['content_hash'] = content_hash
        
        if self.storage_type == 'database':
            return self._add_post_to_db(post_data)
        else:
            return self._add_post_to_json(post_data)
    
    def _add_strategy_to_db(self, strategy_data: Dict) -> bool:
        """添加策略到数据库"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO strategies (name, code, description, type, indicator, 
                                      timeframe, asset_class, risk_level, complexity, content_hash)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                strategy_data['name'],
                strategy_data['code'],
                strategy_data.get('description', ''),
                strategy_data.get('type', ''),
                strategy_data.get('indicator', ''),
                strategy_data.get('timeframe', ''),
                strategy_data.get('asset_class', ''),
                strategy_data.get('risk_level', ''),
                strategy_data.get('complexity', ''),
                strategy_data['content_hash']
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            # 更新哈希缓存
            self.strategy_hashes.add(strategy_data['content_hash'])
            return True
            
        except Exception as e:
            print(f"❌ 添加策略到数据库失败: {e}")
            return False
    
    def _add_post_to_db(self, post_data: Dict) -> bool:
        """添加帖子到数据库"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO forum_posts (title, content, author, category, tags, 
                                       views, likes, reply_count, is_pinned, is_locked, content_hash)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                post_data['title'],
                post_data['content'],
                post_data.get('author', ''),
                post_data.get('category', ''),
                post_data.get('tags', []),
                post_data.get('views', 0),
                post_data.get('likes', 0),
                post_data.get('reply_count', 0),
                post_data.get('is_pinned', False),
                post_data.get('is_locked', False),
                post_data['content_hash']
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            # 更新哈希缓存
            self.post_hashes.add(post_data['content_hash'])
            return True
            
        except Exception as e:
            print(f"❌ 添加帖子到数据库失败: {e}")
            return False
    
    def get_content_stats(self) -> Dict:
        """获取内容统计"""
        if self.storage_type == 'database':
            return self._get_stats_from_db()
        else:
            return self._get_stats_from_json()
    
    def _get_stats_from_db(self) -> Dict:
        """从数据库获取统计"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 策略统计
            cursor.execute("SELECT COUNT(*) FROM strategies")
            strategy_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT type, COUNT(*) FROM strategies GROUP BY type")
            strategy_types = dict(cursor.fetchall())
            
            # 帖子统计
            cursor.execute("SELECT COUNT(*) FROM forum_posts")
            post_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT category, COUNT(*) FROM forum_posts GROUP BY category")
            post_categories = dict(cursor.fetchall())
            
            cursor.close()
            conn.close()
            
            return {
                'strategies': {
                    'total': strategy_count,
                    'by_type': strategy_types
                },
                'posts': {
                    'total': post_count,
                    'by_category': post_categories
                }
            }
            
        except Exception as e:
            print(f"❌ 获取统计失败: {e}")
            return {}
    
    def export_to_json(self, filename_prefix='export'):
        """导出数据到JSON文件"""
        if self.storage_type == 'database':
            self._export_db_to_json(filename_prefix)
        else:
            print("当前已经是JSON存储模式")
    
    def _export_db_to_json(self, filename_prefix):
        """从数据库导出到JSON"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 导出策略
            cursor.execute("SELECT * FROM strategies ORDER BY id")
            strategies = {}
            for row in cursor.fetchall():
                strategies[str(row[0])] = {
                    'name': row[1],
                    'code': row[2],
                    'description': row[3],
                    'type': row[4],
                    'indicator': row[5],
                    'timeframe': row[6],
                    'asset_class': row[7],
                    'risk_level': row[8],
                    'complexity': row[9]
                }
            
            with open(f'{filename_prefix}_strategies.json', 'w', encoding='utf-8') as f:
                json.dump(strategies, f, ensure_ascii=False, indent=2)
            
            # 导出帖子
            cursor.execute("SELECT * FROM forum_posts ORDER BY id")
            posts = []
            for row in cursor.fetchall():
                posts.append({
                    'id': row[0],
                    'title': row[1],
                    'content': row[2],
                    'author': row[3],
                    'category': row[4],
                    'tags': row[5] or [],
                    'views': row[6],
                    'likes': row[7],
                    'reply_count': row[8],
                    'is_pinned': row[9],
                    'is_locked': row[10],
                    'created_at': row[12].isoformat() if row[12] else None
                })
            
            forum_data = {
                'posts': posts,
                'stats': self._get_stats_from_db()
            }
            
            with open(f'{filename_prefix}_forum_posts.json', 'w', encoding='utf-8') as f:
                json.dump(forum_data, f, ensure_ascii=False, indent=2)
            
            cursor.close()
            conn.close()
            
            print(f"✅ 数据已导出到 {filename_prefix}_strategies.json 和 {filename_prefix}_forum_posts.json")
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")

# 使用示例和配置
def get_recommended_config():
    """获取推荐配置"""
    return {
        'storage_recommendations': {
            'small_scale': {
                'description': '小规模使用（<1000条内容）',
                'storage_type': 'json',
                'pros': ['简单易用', '无需数据库', '便于备份'],
                'cons': ['性能较差', '并发支持差', '查询功能有限']
            },
            'medium_scale': {
                'description': '中等规模使用（1000-10000条内容）',
                'storage_type': 'database',
                'database': 'SQLite',
                'pros': ['性能好', '支持SQL查询', '事务安全'],
                'cons': ['需要数据库知识', '部署稍复杂']
            },
            'large_scale': {
                'description': '大规模使用（>10000条内容）',
                'storage_type': 'database',
                'database': 'PostgreSQL',
                'pros': ['高性能', '高并发', '丰富功能', '可扩展'],
                'cons': ['需要专业运维', '资源消耗大']
            }
        },
        'daily_generation_limits': {
            'json_storage': 100,
            'sqlite_storage': 500,
            'postgresql_storage': 2000
        }
    }

if __name__ == "__main__":
    # 示例用法
    print("QuantTradeX 高级内容管理系统")
    print("推荐配置:")
    config = get_recommended_config()
    for scale, info in config['storage_recommendations'].items():
        print(f"  {scale}: {info['description']} -> {info['storage_type']}")
