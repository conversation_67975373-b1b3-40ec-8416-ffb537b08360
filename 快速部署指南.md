# QuantTradeX 快速部署指南

## 🚀 5分钟快速部署

### 方式一：一键部署脚本（推荐）

```bash
# 1. 下载部署脚本
wget -O deploy.sh https://raw.githubusercontent.com/quanttradex/deploy/main/deploy_quanttradex.sh

# 2. 赋予执行权限
chmod +x deploy.sh

# 3. 执行部署
sudo ./deploy.sh

# 4. 访问网站
curl http://localhost
```

### 方式二：从备份文件恢复

```bash
# 1. 上传备份文件到服务器
scp quanttradex_complete_backup_20250527_065820.tar.gz user@server:/tmp/

# 2. 解压备份文件
cd /tmp
tar -xzf quanttradex_complete_backup_20250527_065820.tar.gz

# 3. 使用部署脚本恢复
sudo ./gdpp.com/backup_scripts/deploy_quanttradex.sh /tmp/quanttradex_complete_backup_20250527_065820.tar.gz

# 4. 验证部署
curl http://localhost/api/system/status
```

### 方式三：手动部署

```bash
# 1. 安装基础环境
sudo apt update && sudo apt install -y python3 python3-pip python3-venv nginx

# 2. 创建项目目录
sudo mkdir -p /www/wwwroot
sudo chown -R $USER:$USER /www/wwwroot

# 3. 解压项目文件
cd /www/wwwroot
tar -xzf /path/to/backup.tar.gz

# 4. 配置Python环境
cd gdpp.com
python3 -m venv venv
source venv/bin/activate
pip install flask gunicorn

# 5. 配置Nginx
sudo cp backup_scripts/nginx.conf /etc/nginx/sites-available/gdpp.com
sudo ln -s /etc/nginx/sites-available/gdpp.com /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl restart nginx

# 6. 创建系统服务
sudo cp backup_scripts/quanttradex.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable quanttradex
sudo systemctl start quanttradex
```

## 📋 部署前检查清单

### 系统要求
- [ ] Ubuntu 20.04+ 或 CentOS 7+
- [ ] 至少 4GB 内存
- [ ] 至少 50GB 可用磁盘空间
- [ ] Root 或 sudo 权限
- [ ] 网络连接正常

### 端口要求
- [ ] 端口 80 (HTTP) 可用
- [ ] 端口 443 (HTTPS) 可用
- [ ] 端口 5000 (Flask) 可用
- [ ] 防火墙配置正确

### 域名配置（可选）
- [ ] 域名 DNS 解析配置
- [ ] SSL 证书准备（可选）

## 🔧 配置选项

### 基础配置
```bash
# 修改域名
DOMAIN_NAME="your-domain.com"

# 修改端口
NGINX_PORT=80
FLASK_PORT=5000

# 修改用户
WEB_USER="www-data"
WEB_GROUP="www-data"
```

### 高级配置
```bash
# 启用SSL
SSL_ENABLED=true
SSL_CERT_PATH="/etc/ssl/certs/your-cert.pem"
SSL_KEY_PATH="/etc/ssl/private/your-key.pem"

# 数据库配置
DB_ENABLED=true
DB_HOST="localhost"
DB_USER="quanttradex"
DB_PASSWORD="your-password"

# 邮件通知
EMAIL_ENABLED=true
EMAIL_TO="<EMAIL>"
SMTP_SERVER="smtp.gmail.com"
```

## 🎯 部署后验证

### 1. 服务状态检查
```bash
# 检查服务运行状态
sudo systemctl status quanttradex nginx

# 检查端口监听
sudo netstat -tuln | grep -E ":(80|5000)"

# 检查进程
ps aux | grep -E "(python|nginx)"
```

### 2. 功能测试
```bash
# 测试主页
curl -I http://localhost/

# 测试API
curl http://localhost/api/system/status

# 测试静态文件
curl -I http://localhost/static/css/style.css
```

### 3. 日志检查
```bash
# 查看应用日志
sudo journalctl -u quanttradex --since "5 minutes ago"

# 查看Nginx日志
sudo tail -f /var/log/nginx/gdpp.com.access.log
sudo tail -f /var/log/nginx/gdpp.com.error.log
```

## 🛠️ 常见问题解决

### 问题1: 端口被占用
```bash
# 查看端口占用
sudo lsof -i :80
sudo lsof -i :5000

# 停止占用进程
sudo kill -9 <PID>

# 或修改配置使用其他端口
```

### 问题2: 权限错误
```bash
# 修复文件权限
sudo chown -R www-data:www-data /www/wwwroot/gdpp.com
sudo chmod -R 755 /www/wwwroot/gdpp.com

# 修复SELinux（CentOS）
sudo setsebool -P httpd_can_network_connect 1
```

### 问题3: Python环境问题
```bash
# 重新创建虚拟环境
cd /www/wwwroot/gdpp.com
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 问题4: Nginx配置错误
```bash
# 测试配置
sudo nginx -t

# 查看错误详情
sudo nginx -T

# 重新加载配置
sudo nginx -s reload
```

## 📊 性能优化

### 1. Nginx优化
```nginx
# 在 /etc/nginx/nginx.conf 中添加
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

### 2. 系统优化
```bash
# 调整文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf

# 调整网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sudo sysctl -p
```

### 3. 应用优化
```bash
# 增加Gunicorn工作进程
# 在 quanttradex.service 中修改
ExecStart=/path/to/venv/bin/gunicorn --bind 127.0.0.1:5000 --workers 4 --timeout 120 app:app
```

## 🔐 安全加固

### 1. 防火墙配置
```bash
# Ubuntu UFW
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### 3. 安全头配置
```nginx
# 在Nginx配置中添加
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

## 📈 监控和维护

### 1. 设置自动备份
```bash
# 添加到crontab
sudo crontab -e

# 每天凌晨2点备份
0 2 * * * /backup/scripts/backup_quanttradex.sh

# 每周日备份到远程
0 3 * * 0 rsync -av /backup/quanttradex/ user@backup-server:/backup/
```

### 2. 设置监控
```bash
# 每5分钟检查系统状态
*/5 * * * * /backup/scripts/monitor_quanttradex.sh

# 每小时生成性能报告
0 * * * * /backup/scripts/performance_report.sh
```

### 3. 日志轮转
```bash
# 创建logrotate配置
sudo tee /etc/logrotate.d/quanttradex << EOF
/var/log/quanttradex/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF
```

## 🎉 部署完成

### 访问地址
- **主页**: http://your-domain.com
- **策略市场**: http://your-domain.com/strategies
- **社区论坛**: http://your-domain.com/forum
- **API状态**: http://your-domain.com/api/system/status

### 测试账号
- **普通用户**: demo_user / password123
- **VIP用户**: premium_user / premium123

### 管理命令
```bash
# 重启服务
sudo systemctl restart quanttradex nginx

# 查看状态
sudo systemctl status quanttradex nginx

# 查看日志
sudo journalctl -u quanttradex -f

# 执行备份
sudo /backup/scripts/backup_quanttradex.sh

# 系统监控
sudo /backup/scripts/monitor_quanttradex.sh
```

---

**🎯 恭喜！QuantTradeX 已成功部署！**

如有问题，请参考详细的《系统备份和部署教程.md》或联系技术支持。
