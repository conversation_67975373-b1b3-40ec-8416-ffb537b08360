<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏夹功能测试 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            padding: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .demo-strategy {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .demo-strategy:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .favorite-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .favorite-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }

        .favorite-btn.favorited {
            background: var(--danger);
            color: white;
        }

        .favorite-btn.favorited:hover {
            background: #dc2626;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            text-align: center;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .comparison-box {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .feature-list {
            background: rgba(0, 255, 0, 0.1);
            border-color: rgba(0, 255, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="text-center mb-4">
                    <h1 class="display-5 mb-3">
                        <i class="fas fa-heart text-danger me-3"></i>
                        收藏夹功能测试
                    </h1>
                    <p class="lead">完整的策略收藏和管理功能演示</p>
                </div>

                <!-- 功能统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="favoritesCount">0</div>
                            <div>收藏策略</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">8</div>
                            <div>功能特性</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">5</div>
                            <div>演示策略</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">100%</div>
                            <div>功能完成度</div>
                        </div>
                    </div>
                </div>

                <!-- 功能特性 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star text-warning me-2"></i>
                            收藏夹功能特性
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="comparison-box feature-list">
                                    <h6 class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>核心功能
                                    </h6>
                                    <ul class="mb-0">
                                        <li>一键收藏/取消收藏策略</li>
                                        <li>收藏夹统一管理界面</li>
                                        <li>本地存储持久化</li>
                                        <li>收藏时间记录</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="comparison-box feature-list">
                                    <h6 class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>高级功能
                                    </h6>
                                    <ul class="mb-0">
                                        <li>搜索收藏的策略</li>
                                        <li>按分类筛选</li>
                                        <li>多种排序方式</li>
                                        <li>导出收藏数据</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 演示策略 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-code me-2"></i>
                            演示策略 - 点击收藏按钮测试
                        </h5>
                        <button class="btn btn-primary btn-sm" onclick="showFavorites()">
                            <i class="fas fa-heart me-1"></i>查看收藏夹
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row" id="demoStrategies">
                            <!-- 演示策略将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            功能测试
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <button class="btn btn-primary btn-lg me-3" onclick="showFavorites()">
                            <i class="fas fa-heart me-2"></i>打开收藏夹
                        </button>
                        <button class="btn btn-outline-light btn-lg me-3" onclick="addRandomFavorites()">
                            <i class="fas fa-plus me-2"></i>随机收藏3个
                        </button>
                        <button class="btn btn-outline-danger btn-lg" onclick="clearAllFavorites()">
                            <i class="fas fa-trash me-2"></i>清空收藏夹
                        </button>
                    </div>
                </div>

                <!-- 返回按钮 -->
                <div class="text-center">
                    <a href="/strategies" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        返回策略市场
                    </a>
                    <a href="/" class="btn btn-outline-light btn-lg ms-3">
                        <i class="fas fa-home me-2"></i>
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 简化的收藏夹模态框 -->
    <div class="modal fade" id="favoritesModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: var(--dark-surface); border: 1px solid var(--glass-border); color: var(--text-primary);">
                <div class="modal-header" style="border-bottom: 1px solid var(--glass-border);">
                    <h5 class="modal-title">
                        <i class="fas fa-heart text-danger me-2"></i>我的收藏夹
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body">
                    <div id="favoritesContainer">
                        <!-- 收藏的策略将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--glass-border);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-outline-danger" onclick="clearAllFavorites()">
                        <i class="fas fa-trash me-1"></i>清空收藏夹
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟策略数据
        const demoStrategies = [
            { id: 1, name: '双均线交叉策略', category: 'trend', description: '基于移动平均线的经典趋势跟踪策略', rating: 4.5, downloads: 1234 },
            { id: 2, name: 'RSI反转策略', category: 'mean_reversion', description: '利用RSI指标识别超买超卖机会', rating: 4.2, downloads: 856 },
            { id: 3, name: '布林带突破策略', category: 'volatility', description: '基于布林带的波动率突破策略', rating: 4.7, downloads: 2341 },
            { id: 4, name: 'MACD金叉策略', category: 'momentum', description: 'MACD指标的动量交易策略', rating: 4.3, downloads: 1567 },
            { id: 5, name: '网格交易策略', category: 'arbitrage', description: '适合震荡市场的网格交易策略', rating: 4.1, downloads: 987 }
        ];

        // 收藏夹管理
        let favorites = JSON.parse(localStorage.getItem('quanttradex_favorites_test') || '[]');

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            displayDemoStrategies();
            updateFavoritesCount();
        });

        // 显示演示策略
        function displayDemoStrategies() {
            const container = document.getElementById('demoStrategies');
            container.innerHTML = demoStrategies.map(strategy => `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="demo-strategy">
                        <button class="favorite-btn ${isFavorited(strategy.id) ? 'favorited' : ''}" 
                                onclick="toggleFavorite(${strategy.id})"
                                title="${isFavorited(strategy.id) ? '取消收藏' : '添加收藏'}">
                            <i class="fas fa-heart"></i>
                        </button>
                        <h6 class="mb-2">${strategy.name}</h6>
                        <p class="small text-muted mb-2">${strategy.description}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-warning">
                                ${'★'.repeat(Math.floor(strategy.rating))} ${strategy.rating}
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-download me-1"></i>${strategy.downloads}
                            </small>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 检查是否已收藏
        function isFavorited(strategyId) {
            return favorites.some(fav => fav.id === strategyId);
        }

        // 保存收藏夹
        function saveFavorites() {
            localStorage.setItem('quanttradex_favorites_test', JSON.stringify(favorites));
        }

        // 切换收藏状态
        function toggleFavorite(strategyId) {
            const strategy = demoStrategies.find(s => s.id === strategyId);
            if (!strategy) return;

            const favoriteIndex = favorites.findIndex(fav => fav.id === strategyId);
            
            if (favoriteIndex > -1) {
                favorites.splice(favoriteIndex, 1);
                showNotification(`已取消收藏 "${strategy.name}"`, 'info');
            } else {
                const favoriteItem = {
                    ...strategy,
                    dateAdded: new Date().toISOString()
                };
                favorites.push(favoriteItem);
                showNotification(`已收藏 "${strategy.name}"`, 'success');
            }
            
            saveFavorites();
            displayDemoStrategies();
            updateFavoritesCount();
        }

        // 更新收藏夹数量
        function updateFavoritesCount() {
            document.getElementById('favoritesCount').textContent = favorites.length;
        }

        // 显示收藏夹
        function showFavorites() {
            displayFavorites();
            new bootstrap.Modal(document.getElementById('favoritesModal')).show();
        }

        // 显示收藏的策略
        function displayFavorites() {
            const container = document.getElementById('favoritesContainer');
            
            if (favorites.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-heart-broken fa-3x text-muted mb-3"></i>
                        <h5>收藏夹为空</h5>
                        <p class="text-muted">还没有收藏任何策略，去试试收藏功能吧！</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = favorites.map(strategy => `
                <div class="demo-strategy mb-3">
                    <button class="favorite-btn favorited" 
                            onclick="toggleFavorite(${strategy.id})"
                            title="取消收藏">
                        <i class="fas fa-heart"></i>
                    </button>
                    <h6 class="mb-2">${strategy.name}</h6>
                    <p class="small text-muted mb-2">${strategy.description}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-warning">
                            ${'★'.repeat(Math.floor(strategy.rating))} ${strategy.rating}
                        </small>
                        <small class="text-muted">
                            收藏时间: ${new Date(strategy.dateAdded).toLocaleDateString()}
                        </small>
                    </div>
                </div>
            `).join('');
        }

        // 随机收藏策略
        function addRandomFavorites() {
            const unfavorited = demoStrategies.filter(s => !isFavorited(s.id));
            if (unfavorited.length === 0) {
                showNotification('所有策略都已收藏！', 'info');
                return;
            }

            const toAdd = unfavorited.slice(0, Math.min(3, unfavorited.length));
            toAdd.forEach(strategy => {
                const favoriteItem = {
                    ...strategy,
                    dateAdded: new Date().toISOString()
                };
                favorites.push(favoriteItem);
            });

            saveFavorites();
            displayDemoStrategies();
            updateFavoritesCount();
            showNotification(`已随机收藏 ${toAdd.length} 个策略`, 'success');
        }

        // 清空收藏夹
        function clearAllFavorites() {
            if (favorites.length === 0) {
                showNotification('收藏夹已经是空的', 'info');
                return;
            }

            if (confirm(`确定要清空收藏夹吗？这将删除所有 ${favorites.length} 个收藏的策略。`)) {
                favorites = [];
                saveFavorites();
                displayDemoStrategies();
                updateFavoritesCount();
                displayFavorites();
                showNotification('收藏夹已清空', 'success');
            }
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
