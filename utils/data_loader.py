#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据加载器
负责加载各种数据文件和生成的内容
"""

import json
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

def load_generated_forum_posts() -> List[Dict[str, Any]]:
    """加载生成的论坛帖子数据"""
    try:
        # 优先使用最新的论坛数据
        with open('forum_api_data.json', 'r', encoding='utf-8') as f:
            forum_data = json.load(f)
            return forum_data.get('posts', [])
    except FileNotFoundError:
        try:
            # 回退到完整内容
            with open('generated_data/forum_posts_complete.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            try:
                # 回退到原始生成数据
                with open('generated_data/forum_posts.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            except FileNotFoundError:
                logger.warning("无法加载论坛帖子数据，返回空列表")
                return []
    except Exception as e:
        logger.error(f"加载论坛帖子数据失败: {e}")
        return []

def load_strategy_code(strategy_id: int) -> str:
    """加载策略代码"""
    try:
        with open('strategy_codes_mapping.json', 'r', encoding='utf-8') as f:
            codes = json.load(f)
            strategy_data = codes.get(str(strategy_id))
            if strategy_data and isinstance(strategy_data, dict):
                return strategy_data.get('code', "# 策略代码未找到\npass")
            else:
                return strategy_data or "# 策略代码未找到\npass"
    except FileNotFoundError:
        logger.warning(f"策略代码映射文件不存在")
        return "# 策略代码加载失败\npass"
    except Exception as e:
        logger.error(f"加载策略代码失败: {e}")
        return "# 策略代码加载失败\npass"

def load_strategy_info(strategy_id: int) -> Dict[str, Any]:
    """加载策略信息"""
    try:
        with open('strategy_codes_mapping.json', 'r', encoding='utf-8') as f:
            codes = json.load(f)
            strategy_data = codes.get(str(strategy_id))
            if strategy_data and isinstance(strategy_data, dict):
                return strategy_data
            else:
                return {
                    "name": f"策略{strategy_id}", 
                    "code": strategy_data or "# 策略代码未找到\npass"
                }
    except FileNotFoundError:
        logger.warning(f"策略信息文件不存在")
        return {"name": f"策略{strategy_id}", "code": "# 策略代码加载失败\npass"}
    except Exception as e:
        logger.error(f"加载策略信息失败: {e}")
        return {"name": f"策略{strategy_id}", "code": "# 策略代码加载失败\npass"}

def load_generated_comments() -> List[Dict[str, Any]]:
    """加载生成的用户评论"""
    try:
        with open('generated_data/user_comments.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning("用户评论文件不存在")
        return []
    except Exception as e:
        logger.error(f"加载用户评论失败: {e}")
        return []

def load_generated_strategies() -> List[Dict[str, Any]]:
    """加载生成的策略数据"""
    try:
        with open('generated_data/strategy_codes.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning("策略数据文件不存在")
        return []
    except Exception as e:
        logger.error(f"加载策略数据失败: {e}")
        return []

def save_data_to_file(data: Any, filename: str, encoding: str = 'utf-8') -> bool:
    """保存数据到文件"""
    try:
        with open(filename, 'w', encoding=encoding) as f:
            if isinstance(data, (dict, list)):
                json.dump(data, f, ensure_ascii=False, indent=2)
            else:
                f.write(str(data))
        return True
    except Exception as e:
        logger.error(f"保存数据到文件失败 {filename}: {e}")
        return False

def load_json_file(filename: str, default: Any = None) -> Any:
    """加载JSON文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"文件不存在: {filename}")
        return default
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误 {filename}: {e}")
        return default
    except Exception as e:
        logger.error(f"加载文件失败 {filename}: {e}")
        return default

def save_json_file(data: Any, filename: str) -> bool:
    """保存JSON文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存JSON文件失败 {filename}: {e}")
        return False

def load_config_file(filename: str) -> Optional[Dict[str, Any]]:
    """加载配置文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            if filename.endswith('.json'):
                return json.load(f)
            elif filename.endswith('.py'):
                # 简单的Python配置文件加载
                config = {}
                exec(f.read(), config)
                return {k: v for k, v in config.items() if not k.startswith('__')}
            else:
                logger.warning(f"不支持的配置文件格式: {filename}")
                return None
    except Exception as e:
        logger.error(f"加载配置文件失败 {filename}: {e}")
        return None

def get_file_size(filename: str) -> int:
    """获取文件大小（字节）"""
    try:
        import os
        return os.path.getsize(filename)
    except Exception as e:
        logger.error(f"获取文件大小失败 {filename}: {e}")
        return 0

def file_exists(filename: str) -> bool:
    """检查文件是否存在"""
    try:
        import os
        return os.path.exists(filename)
    except Exception:
        return False

def create_backup(filename: str, backup_suffix: str = '.backup') -> bool:
    """创建文件备份"""
    try:
        import shutil
        backup_filename = filename + backup_suffix
        shutil.copy2(filename, backup_filename)
        logger.info(f"创建备份文件: {backup_filename}")
        return True
    except Exception as e:
        logger.error(f"创建备份失败 {filename}: {e}")
        return False

def validate_json_structure(data: Any, required_fields: List[str]) -> bool:
    """验证JSON数据结构"""
    try:
        if not isinstance(data, dict):
            return False
        
        for field in required_fields:
            if field not in data:
                return False
        
        return True
    except Exception:
        return False

def merge_json_data(base_data: Dict[str, Any], update_data: Dict[str, Any]) -> Dict[str, Any]:
    """合并JSON数据"""
    try:
        result = base_data.copy()
        result.update(update_data)
        return result
    except Exception as e:
        logger.error(f"合并JSON数据失败: {e}")
        return base_data
