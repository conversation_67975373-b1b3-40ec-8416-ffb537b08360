#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户模型
定义用户数据结构和相关操作
"""

from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Optional, List

@dataclass
class User:
    """用户数据模型"""
    id: int
    username: str
    email: str
    full_name: str
    role: str = 'user'
    avatar_url: str = '/static/img/avatar-default.png'
    bio: str = ''
    phone: str = ''
    region: str = 'cn'
    risk_preference: str = 'moderate'
    experience: str = 'beginner'
    created_at: str = ''
    updated_at: str = ''
    last_login: str = ''
    is_premium: bool = False
    premium_expires: Optional[str] = None
    total_strategies: int = 0
    total_backtests: int = 0
    total_profit: float = 0.0
    win_rate: float = 0.0
    followers: int = 0
    following: int = 0
    two_factor_enabled: bool = False
    two_factor_secret: Optional[str] = None
    backup_codes: List[str] = None
    
    def __post_init__(self):
        if self.backup_codes is None:
            self.backup_codes = []
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()
    
    def to_dict(self, include_sensitive=False):
        """转换为字典，可选择是否包含敏感信息"""
        data = asdict(self)
        if not include_sensitive:
            # 移除敏感信息
            sensitive_fields = ['two_factor_secret', 'backup_codes']
            for field in sensitive_fields:
                data.pop(field, None)
        return data
    
    def to_public_dict(self):
        """转换为公开信息字典"""
        return {
            'id': self.id,
            'username': self.username,
            'full_name': self.full_name,
            'avatar_url': self.avatar_url,
            'bio': self.bio,
            'region': self.region,
            'experience': self.experience,
            'total_strategies': self.total_strategies,
            'total_backtests': self.total_backtests,
            'win_rate': self.win_rate,
            'followers': self.followers,
            'following': self.following,
            'is_premium': self.is_premium
        }
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
    
    def update_profile(self, **kwargs):
        """更新用户资料"""
        allowed_fields = [
            'full_name', 'bio', 'phone', 'region', 
            'risk_preference', 'experience', 'avatar_url'
        ]
        
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(self, field):
                setattr(self, field, value)
        
        self.updated_at = datetime.now().isoformat()
    
    def enable_premium(self, expires_at: str):
        """启用高级会员"""
        self.is_premium = True
        self.premium_expires = expires_at
        self.updated_at = datetime.now().isoformat()
    
    def disable_premium(self):
        """禁用高级会员"""
        self.is_premium = False
        self.premium_expires = None
        self.updated_at = datetime.now().isoformat()
    
    def is_premium_active(self):
        """检查高级会员是否有效"""
        if not self.is_premium:
            return False
        if not self.premium_expires:
            return True
        return datetime.now() < datetime.fromisoformat(self.premium_expires)
    
    def add_strategy(self):
        """增加策略数量"""
        self.total_strategies += 1
        self.updated_at = datetime.now().isoformat()
    
    def add_backtest(self):
        """增加回测数量"""
        self.total_backtests += 1
        self.updated_at = datetime.now().isoformat()
    
    def update_profit(self, profit: float):
        """更新总收益"""
        self.total_profit += profit
        self.updated_at = datetime.now().isoformat()
    
    def update_win_rate(self, win_rate: float):
        """更新胜率"""
        self.win_rate = win_rate
        self.updated_at = datetime.now().isoformat()
    
    def add_follower(self):
        """增加粉丝"""
        self.followers += 1
        self.updated_at = datetime.now().isoformat()
    
    def remove_follower(self):
        """减少粉丝"""
        if self.followers > 0:
            self.followers -= 1
        self.updated_at = datetime.now().isoformat()
    
    def add_following(self):
        """增加关注"""
        self.following += 1
        self.updated_at = datetime.now().isoformat()
    
    def remove_following(self):
        """减少关注"""
        if self.following > 0:
            self.following -= 1
        self.updated_at = datetime.now().isoformat()

@dataclass
class UserSession:
    """用户会话模型"""
    user_id: int
    username: str
    role: str
    login_time: str
    last_activity: str
    ip_address: str = ''
    user_agent: str = ''
    
    def __post_init__(self):
        if not self.login_time:
            self.login_time = datetime.now().isoformat()
        if not self.last_activity:
            self.last_activity = datetime.now().isoformat()
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity = datetime.now().isoformat()
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)

@dataclass
class UserPreferences:
    """用户偏好设置模型"""
    user_id: int
    theme: str = 'light'
    language: str = 'zh-CN'
    timezone: str = 'Asia/Shanghai'
    notifications_email: bool = True
    notifications_push: bool = True
    notifications_sms: bool = False
    auto_save_strategies: bool = True
    default_chart_type: str = 'candlestick'
    default_time_frame: str = '1d'
    risk_warning_enabled: bool = True
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)
    
    def update_preferences(self, **kwargs):
        """更新偏好设置"""
        for field, value in kwargs.items():
            if hasattr(self, field):
                setattr(self, field, value)
