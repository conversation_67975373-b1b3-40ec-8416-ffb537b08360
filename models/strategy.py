#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
策略模型
定义交易策略数据结构和相关操作
"""

from datetime import datetime
from dataclasses import dataclass, asdict, field
from typing import Optional, List, Dict, Any
from enum import Enum

class StrategyType(Enum):
    """策略类型枚举"""
    TREND_FOLLOWING = "trend_following"
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    ARBITRAGE = "arbitrage"
    MARKET_MAKING = "market_making"
    CUSTOM = "custom"

class AssetClass(Enum):
    """资产类别枚举"""
    STOCKS = "stocks"
    CRYPTO = "crypto"
    FOREX = "forex"
    FUTURES = "futures"
    OPTIONS = "options"
    BONDS = "bonds"

class StrategyStatus(Enum):
    """策略状态枚举"""
    DRAFT = "draft"
    TESTING = "testing"
    ACTIVE = "active"
    PAUSED = "paused"
    ARCHIVED = "archived"

@dataclass
class Strategy:
    """交易策略模型"""
    id: int
    name: str
    description: str
    author_id: int
    author_username: str
    strategy_type: str = StrategyType.CUSTOM.value
    asset_class: str = AssetClass.STOCKS.value
    status: str = StrategyStatus.DRAFT.value
    code: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    is_public: bool = False
    is_premium: bool = False
    price: float = 0.0
    rating: float = 0.0
    downloads: int = 0
    likes: int = 0
    views: int = 0
    created_at: str = ""
    updated_at: str = ""
    last_backtest: Optional[str] = None
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    risk_metrics: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)
    
    def to_public_dict(self):
        """转换为公开信息字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'author_username': self.author_username,
            'strategy_type': self.strategy_type,
            'asset_class': self.asset_class,
            'tags': self.tags,
            'is_premium': self.is_premium,
            'price': self.price,
            'rating': self.rating,
            'downloads': self.downloads,
            'likes': self.likes,
            'views': self.views,
            'created_at': self.created_at,
            'performance_metrics': self.performance_metrics
        }
    
    def update_strategy(self, **kwargs):
        """更新策略信息"""
        allowed_fields = [
            'name', 'description', 'strategy_type', 'asset_class',
            'code', 'parameters', 'tags', 'is_public', 'is_premium', 'price'
        ]
        
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(self, field):
                setattr(self, field, value)
        
        self.updated_at = datetime.now().isoformat()
    
    def add_view(self):
        """增加浏览量"""
        self.views += 1
    
    def add_download(self):
        """增加下载量"""
        self.downloads += 1
    
    def add_like(self):
        """增加点赞"""
        self.likes += 1
    
    def remove_like(self):
        """取消点赞"""
        if self.likes > 0:
            self.likes -= 1
    
    def update_rating(self, new_rating: float):
        """更新评分"""
        self.rating = max(0.0, min(5.0, new_rating))
    
    def update_performance(self, metrics: Dict[str, Any]):
        """更新性能指标"""
        self.performance_metrics.update(metrics)
        self.updated_at = datetime.now().isoformat()
    
    def update_risk_metrics(self, metrics: Dict[str, Any]):
        """更新风险指标"""
        self.risk_metrics.update(metrics)
        self.updated_at = datetime.now().isoformat()
    
    def set_last_backtest(self):
        """设置最后回测时间"""
        self.last_backtest = datetime.now().isoformat()

@dataclass
class StrategyTemplate:
    """策略模板模型"""
    id: int
    name: str
    description: str
    category: str
    template_code: str
    parameters_schema: Dict[str, Any] = field(default_factory=dict)
    example_parameters: Dict[str, Any] = field(default_factory=dict)
    documentation: str = ""
    difficulty_level: str = "beginner"  # beginner, intermediate, advanced
    estimated_time: int = 30  # 预估完成时间（分钟）
    tags: List[str] = field(default_factory=list)
    is_active: bool = True
    created_at: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)

@dataclass
class StrategyComment:
    """策略评论模型"""
    id: int
    strategy_id: int
    user_id: int
    username: str
    content: str
    rating: Optional[float] = None
    parent_id: Optional[int] = None  # 用于回复评论
    likes: int = 0
    created_at: str = ""
    updated_at: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)
    
    def update_content(self, content: str):
        """更新评论内容"""
        self.content = content
        self.updated_at = datetime.now().isoformat()
    
    def add_like(self):
        """增加点赞"""
        self.likes += 1
    
    def remove_like(self):
        """取消点赞"""
        if self.likes > 0:
            self.likes -= 1

@dataclass
class StrategySubscription:
    """策略订阅模型"""
    id: int
    user_id: int
    strategy_id: int
    subscription_type: str = "free"  # free, premium, trial
    subscribed_at: str = ""
    expires_at: Optional[str] = None
    is_active: bool = True
    
    def __post_init__(self):
        if not self.subscribed_at:
            self.subscribed_at = datetime.now().isoformat()
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)
    
    def is_subscription_active(self):
        """检查订阅是否有效"""
        if not self.is_active:
            return False
        if not self.expires_at:
            return True
        return datetime.now() < datetime.fromisoformat(self.expires_at)
    
    def extend_subscription(self, days: int):
        """延长订阅时间"""
        if self.expires_at:
            current_expiry = datetime.fromisoformat(self.expires_at)
        else:
            current_expiry = datetime.now()
        
        from datetime import timedelta
        new_expiry = current_expiry + timedelta(days=days)
        self.expires_at = new_expiry.isoformat()

@dataclass
class StrategyPerformance:
    """策略性能记录模型"""
    id: int
    strategy_id: int
    backtest_id: Optional[int] = None
    period_start: str = ""
    period_end: str = ""
    total_return: float = 0.0
    annual_return: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    total_trades: int = 0
    avg_trade_duration: float = 0.0
    volatility: float = 0.0
    beta: float = 0.0
    alpha: float = 0.0
    recorded_at: str = ""
    
    def __post_init__(self):
        if not self.recorded_at:
            self.recorded_at = datetime.now().isoformat()
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)
