[{"id": 1, "title": "量化交易中的风险管理", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_4339", "created_at": "2025-05-09T13:57:58.023835", "views": 1626, "replies": 29, "likes": 9, "category": "经验分享"}, {"id": 2, "title": "机器学习在股票预测中的效果", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_8322", "created_at": "2025-05-23T13:57:59.269751", "views": 1129, "replies": 33, "likes": 21, "category": "经验分享"}, {"id": 3, "title": "移动平均策略的优化技巧", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_6495", "created_at": "2025-05-18T13:58:00.516190", "views": 440, "replies": 32, "likes": 40, "category": "工具推荐"}, {"id": 4, "title": "量化交易中的风险管理", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_7720", "created_at": "2025-04-29T13:58:01.817264", "views": 1543, "replies": 36, "likes": 33, "category": "市场观点"}, {"id": 5, "title": "回测过拟合的避免方法", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_2077", "created_at": "2025-05-12T13:58:03.052974", "views": 704, "replies": 27, "likes": 57, "category": "市场观点"}, {"id": 6, "title": "期货套利的实操心得", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_4651", "created_at": "2025-05-10T13:58:04.280791", "views": 359, "replies": 19, "likes": 69, "category": "工具推荐"}, {"id": 7, "title": "回测过拟合的避免方法", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_7043", "created_at": "2025-05-25T13:58:05.509740", "views": 887, "replies": 31, "likes": 65, "category": "经验分享"}, {"id": 8, "title": "机器学习在股票预测中的效果", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_2420", "created_at": "2025-05-04T13:58:06.931607", "views": 812, "replies": 26, "likes": 78, "category": "工具推荐"}, {"id": 9, "title": "期货套利的实操心得", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_2999", "created_at": "2025-05-06T13:58:08.173796", "views": 385, "replies": 21, "likes": 61, "category": "经验分享"}, {"id": 10, "title": "如何在震荡市场中使用RSI指标", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_3502", "created_at": "2025-05-10T13:58:09.411706", "views": 1134, "replies": 35, "likes": 72, "category": "策略讨论"}, {"id": 11, "title": "量化交易中的风险管理", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_3964", "created_at": "2025-05-12T13:58:11.034603", "views": 1728, "replies": 11, "likes": 82, "category": "技术分析"}, {"id": 12, "title": "Python在量化交易中的应用", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_8239", "created_at": "2025-05-02T13:58:12.370603", "views": 873, "replies": 40, "likes": 63, "category": "市场观点"}, {"id": 13, "title": "Python在量化交易中的应用", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_6784", "created_at": "2025-05-16T13:58:13.654751", "views": 1624, "replies": 38, "likes": 15, "category": "市场观点"}, {"id": 14, "title": "回测过拟合的避免方法", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_1526", "created_at": "2025-05-20T13:58:15.201076", "views": 1387, "replies": 15, "likes": 16, "category": "策略讨论"}, {"id": 15, "title": "Python在量化交易中的应用", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_2045", "created_at": "2025-05-20T13:58:16.438974", "views": 132, "replies": 32, "likes": 5, "category": "技术分析"}, {"id": 16, "title": "机器学习在股票预测中的效果", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_3603", "created_at": "2025-05-20T13:58:17.677043", "views": 902, "replies": 37, "likes": 16, "category": "经验分享"}, {"id": 17, "title": "加密货币量化交易策略", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_8483", "created_at": "2025-04-27T13:58:18.975340", "views": 1497, "replies": 5, "likes": 38, "category": "工具推荐"}, {"id": 18, "title": "期货套利的实操心得", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_2144", "created_at": "2025-05-08T13:58:20.251807", "views": 1755, "replies": 9, "likes": 10, "category": "技术分析"}, {"id": 19, "title": "如何在震荡市场中使用RSI指标", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_1760", "created_at": "2025-05-17T13:58:21.504695", "views": 724, "replies": 16, "likes": 0, "category": "经验分享"}, {"id": 20, "title": "如何在震荡市场中使用RSI指标", "content": "这是一个关于量化交易的讨论帖子，内容正在生成中...", "author": "trader_4565", "created_at": "2025-05-13T13:58:22.974438", "views": 781, "replies": 30, "likes": 52, "category": "经验分享"}]