#!/bin/bash

# QuantTradeX 服务启动脚本
# 用于启动和管理Flask应用

SCRIPT_DIR="/www/wwwroot/gdpp.com"
APP_NAME="QuantTradeX"
PID_FILE="$SCRIPT_DIR/app.pid"
LOG_FILE="$SCRIPT_DIR/app.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

# 检查服务状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null 2>&1; then
            print_message $GREEN "$APP_NAME 正在运行 (PID: $pid)"
            return 0
        else
            print_message $YELLOW "$APP_NAME PID文件存在但进程不存在，清理PID文件"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        print_message $RED "$APP_NAME 未运行"
        return 1
    fi
}

# 启动服务
start_service() {
    print_message $BLUE "启动 $APP_NAME 服务..."
    
    if check_status > /dev/null 2>&1; then
        print_message $YELLOW "$APP_NAME 已经在运行"
        return 0
    fi
    
    cd "$SCRIPT_DIR"
    
    # 启动Flask应用
    nohup python3 app.py > "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    
    # 等待服务启动
    sleep 3
    
    # 检查是否成功启动
    if ps -p $pid > /dev/null 2>&1; then
        print_message $GREEN "$APP_NAME 启动成功 (PID: $pid)"
        
        # 测试服务是否响应
        if curl -s http://127.0.0.1:5000 > /dev/null; then
            print_message $GREEN "$APP_NAME 服务响应正常"
        else
            print_message $YELLOW "$APP_NAME 启动但可能未完全就绪，请稍等片刻"
        fi
    else
        print_message $RED "$APP_NAME 启动失败"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务
stop_service() {
    print_message $BLUE "停止 $APP_NAME 服务..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null 2>&1; then
            kill $pid
            sleep 2
            
            # 如果进程仍然存在，强制杀死
            if ps -p $pid > /dev/null 2>&1; then
                print_message $YELLOW "正常停止失败，强制停止..."
                kill -9 $pid
                sleep 1
            fi
            
            if ! ps -p $pid > /dev/null 2>&1; then
                print_message $GREEN "$APP_NAME 停止成功"
                rm -f "$PID_FILE"
            else
                print_message $RED "$APP_NAME 停止失败"
                return 1
            fi
        else
            print_message $YELLOW "$APP_NAME 进程不存在，清理PID文件"
            rm -f "$PID_FILE"
        fi
    else
        print_message $YELLOW "$APP_NAME 未运行"
    fi
}

# 重启服务
restart_service() {
    print_message $BLUE "重启 $APP_NAME 服务..."
    stop_service
    sleep 2
    start_service
}

# 查看日志
view_logs() {
    if [ -f "$LOG_FILE" ]; then
        print_message $BLUE "显示 $APP_NAME 日志 (最后50行):"
        tail -50 "$LOG_FILE"
    else
        print_message $YELLOW "日志文件不存在: $LOG_FILE"
    fi
}

# 实时查看日志
follow_logs() {
    if [ -f "$LOG_FILE" ]; then
        print_message $BLUE "实时查看 $APP_NAME 日志 (按Ctrl+C退出):"
        tail -f "$LOG_FILE"
    else
        print_message $YELLOW "日志文件不存在: $LOG_FILE"
    fi
}

# 显示帮助信息
show_help() {
    echo "QuantTradeX 服务管理脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|follow|help}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  status  - 查看服务状态"
    echo "  logs    - 查看日志 (最后50行)"
    echo "  follow  - 实时查看日志"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start     # 启动QuantTradeX服务"
    echo "  $0 status    # 查看服务状态"
    echo "  $0 logs      # 查看最近日志"
}

# 主程序
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        check_status
        ;;
    logs)
        view_logs
        ;;
    follow)
        follow_logs
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $RED "无效的命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac

exit $?
