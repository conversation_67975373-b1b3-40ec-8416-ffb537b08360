# 🚀 宝塔面板后台运行Flask应用配置指南

## 📋 方法一：宝塔面板 - 进程守护管理器（推荐）

### 1. 登录宝塔面板
```
访问: http://你的服务器IP:8888
用户名: 你的宝塔用户名
密码: 你的宝塔密码
```

### 2. 安装进程守护管理器
1. 点击左侧菜单 **"软件商店"**
2. 搜索 **"进程守护管理器"** 或 **"Supervisor管理器"**
3. 点击 **"安装"** 按钮
4. 等待安装完成

### 3. 配置Flask应用守护进程
1. 安装完成后，点击左侧菜单 **"进程守护管理器"**
2. 点击 **"添加守护进程"** 按钮
3. 填写配置信息：

```ini
# 进程名称
quanttradex

# 启动用户
root

# 运行目录
/www/wwwroot/gdpp.com

# 启动命令
/usr/bin/python3 /www/wwwroot/gdpp.com/app.py

# 进程数量
1

# 自动启动
是

# 自动重启
是
```

### 4. 高级配置（可选）
```ini
# 日志文件
/www/wwwroot/gdpp.com/logs/app.log

# 错误日志
/www/wwwroot/gdpp.com/logs/error.log

# 环境变量
PYTHONPATH=/www/wwwroot/gdpp.com
```

### 5. 启动和管理
1. 点击 **"保存"** 按钮
2. 在进程列表中找到 `quanttradex`
3. 点击 **"启动"** 按钮
4. 状态显示为 **"运行中"** 即成功

---

## 📋 方法二：宝塔面板 - Python项目管理器

### 1. 安装Python项目管理器
1. 进入宝塔面板 **"软件商店"**
2. 搜索 **"Python项目管理器"**
3. 点击安装

### 2. 创建Python项目
1. 点击左侧菜单 **"Python项目管理器"**
2. 点击 **"添加项目"**
3. 填写项目信息：

```
项目名称: QuantTradeX
项目路径: /www/wwwroot/gdpp.com
Python版本: Python 3.x
启动方式: python3 app.py
端口: 5000
```

### 3. 配置项目
1. 点击项目名称进入详情
2. 设置 **"开机自启"** 为 **"是"**
3. 设置 **"自动重启"** 为 **"是"**
4. 点击 **"启动"** 按钮

---

## 📋 方法三：Ubuntu系统级配置（systemd）

### 1. 创建systemd服务文件
```bash
sudo nano /etc/systemd/system/quanttradex.service
```

### 2. 添加服务配置
```ini
[Unit]
Description=QuantTradeX Flask Application
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/www/wwwroot/gdpp.com
Environment=PATH=/usr/bin:/usr/local/bin
Environment=PYTHONPATH=/www/wwwroot/gdpp.com
ExecStart=/usr/bin/python3 /www/wwwroot/gdpp.com/app.py
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 3. 启用和启动服务
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用开机自启
sudo systemctl enable quanttradex

# 启动服务
sudo systemctl start quanttradex

# 查看状态
sudo systemctl status quanttradex
```

### 4. 服务管理命令
```bash
# 启动服务
sudo systemctl start quanttradex

# 停止服务
sudo systemctl stop quanttradex

# 重启服务
sudo systemctl restart quanttradex

# 查看状态
sudo systemctl status quanttradex

# 查看日志
sudo journalctl -u quanttradex -f
```

---

## 📋 方法四：使用screen或tmux（临时方案）

### 使用screen
```bash
# 安装screen
sudo apt update
sudo apt install screen

# 创建新的screen会话
screen -S quanttradex

# 在screen中启动应用
cd /www/wwwroot/gdpp.com
python3 app.py

# 按 Ctrl+A 然后按 D 来分离会话
# 现在可以安全关闭SSH连接

# 重新连接到会话
screen -r quanttradex

# 查看所有会话
screen -ls
```

### 使用tmux
```bash
# 安装tmux
sudo apt update
sudo apt install tmux

# 创建新的tmux会话
tmux new-session -d -s quanttradex

# 在tmux中启动应用
tmux send-keys -t quanttradex "cd /www/wwwroot/gdpp.com" Enter
tmux send-keys -t quanttradex "python3 app.py" Enter

# 查看会话
tmux list-sessions

# 连接到会话
tmux attach-session -t quanttradex
```

---

## 📋 方法五：使用nohup（简单方案）

### 1. 使用nohup后台运行
```bash
cd /www/wwwroot/gdpp.com

# 后台运行并输出到日志文件
nohup python3 app.py > app.log 2>&1 &

# 查看进程
ps aux | grep python3 | grep app.py

# 查看日志
tail -f app.log
```

### 2. 停止nohup进程
```bash
# 查找进程ID
ps aux | grep python3 | grep app.py

# 杀死进程（替换PID为实际进程ID）
kill PID

# 或者强制杀死
pkill -f "python3 app.py"
```

---

## 🎯 推荐配置方案

### 生产环境推荐：宝塔进程守护管理器
**优势**：
- ✅ 图形化界面，操作简单
- ✅ 自动重启和监控
- ✅ 日志管理
- ✅ 开机自启
- ✅ 资源监控

### 开发环境推荐：systemd服务
**优势**：
- ✅ 系统级服务管理
- ✅ 完整的日志系统
- ✅ 依赖管理
- ✅ 安全配置

### 临时使用：screen/tmux
**优势**：
- ✅ 快速部署
- ✅ 会话管理
- ✅ 适合调试

---

## 🔧 验证配置是否成功

### 1. 检查服务状态
```bash
# 检查进程是否运行
ps aux | grep python3 | grep app.py

# 检查端口是否监听
netstat -tlnp | grep :5000

# 测试网站访问
curl -I http://gdpp.com
```

### 2. 测试自动重启
```bash
# 杀死Flask进程
pkill -f "python3 app.py"

# 等待几秒钟，检查是否自动重启
sleep 5
ps aux | grep python3 | grep app.py
```

### 3. 测试开机自启
```bash
# 重启服务器
sudo reboot

# 重启后检查服务是否自动启动
ps aux | grep python3 | grep app.py
```

---

## 🚨 故障排除

### 常见问题1：服务启动失败
```bash
# 检查Python路径
which python3

# 检查应用文件权限
ls -la /www/wwwroot/gdpp.com/app.py

# 手动测试启动
cd /www/wwwroot/gdpp.com
python3 app.py
```

### 常见问题2：端口被占用
```bash
# 查看端口占用
lsof -i :5000

# 杀死占用进程
sudo kill -9 PID
```

### 常见问题3：权限问题
```bash
# 修复文件权限
sudo chown -R root:root /www/wwwroot/gdpp.com
sudo chmod +x /www/wwwroot/gdpp.com/app.py
```

---

## 📊 监控和维护

### 1. 日志监控
```bash
# 实时查看应用日志
tail -f /www/wwwroot/gdpp.com/app.log

# 查看系统日志
sudo journalctl -u quanttradex -f

# 查看nginx错误日志
tail -f /www/wwwlogs/gdpp.com.error.log
```

### 2. 性能监控
```bash
# 查看进程资源使用
top -p $(pgrep -f "python3 app.py")

# 查看内存使用
free -h

# 查看磁盘使用
df -h
```

### 3. 定期维护
```bash
# 清理日志文件（每周执行）
find /www/wwwroot/gdpp.com -name "*.log" -size +100M -delete

# 重启服务（每月执行）
sudo systemctl restart quanttradex
```

---

**🎉 配置完成后，您的QuantTradeX应用将在后台持续运行，即使关闭SSH连接也不会影响网站访问！**
