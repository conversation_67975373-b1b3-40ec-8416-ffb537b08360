#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantTradeX 数据集成脚本
将生成的数据集成到主应用中
"""

import json
import os
import re
from typing import Dict, List, Any

class DataIntegrator:
    def __init__(self):
        self.data_dir = 'generated_data'
        self.app_file = 'app.py'
        
    def load_generated_data(self) -> Dict[str, Any]:
        """加载所有生成的数据"""
        data = {}
        
        # 加载论坛帖子
        forum_file = os.path.join(self.data_dir, 'forum_posts.json')
        if os.path.exists(forum_file):
            with open(forum_file, 'r', encoding='utf-8') as f:
                data['forum_posts'] = json.load(f)
        
        # 加载策略代码
        strategy_file = os.path.join(self.data_dir, 'strategy_codes.json')
        if os.path.exists(strategy_file):
            with open(strategy_file, 'r', encoding='utf-8') as f:
                data['strategy_codes'] = json.load(f)
        
        # 加载用户评论
        comments_file = os.path.join(self.data_dir, 'user_comments.json')
        if os.path.exists(comments_file):
            with open(comments_file, 'r', encoding='utf-8') as f:
                data['user_comments'] = json.load(f)
        
        return data
    
    def update_forum_data(self, forum_posts: List[Dict]):
        """更新论坛数据到app.py"""
        print("正在更新论坛数据...")
        
        # 读取app.py文件
        with open(self.app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找mock_forum_posts的位置
        pattern = r'mock_forum_posts\s*=\s*\[.*?\]'
        
        # 构建新的论坛数据
        new_forum_data = "mock_forum_posts = " + json.dumps(forum_posts, ensure_ascii=False, indent=4)
        
        # 替换数据
        if re.search(pattern, content, re.DOTALL):
            content = re.sub(pattern, new_forum_data, content, flags=re.DOTALL)
            
            # 写回文件
            with open(self.app_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"已更新 {len(forum_posts)} 个论坛帖子")
        else:
            print("未找到mock_forum_posts，请手动添加")
    
    def update_strategy_codes(self, strategy_codes: Dict[str, str]):
        """更新策略代码"""
        print("正在更新策略代码...")
        
        # 创建策略代码映射文件
        strategy_mapping = {}
        
        # 现有策略ID映射
        strategy_id_mapping = {
            1: 'moving_average_crossover',
            2: 'rsi_mean_reversion', 
            3: 'bollinger_bands',
            4: 'momentum',
            5: 'mean_reversion',
            6: 'trend_following',
            7: 'arbitrage',
            8: 'volatility'
        }
        
        # 构建ID到代码的映射
        for strategy_id, strategy_type in strategy_id_mapping.items():
            if strategy_type in strategy_codes:
                strategy_mapping[strategy_id] = strategy_codes[strategy_type]
            else:
                strategy_mapping[strategy_id] = f"# {strategy_type} 策略代码\n# 代码正在生成中...\npass"
        
        # 保存策略代码映射
        with open('strategy_codes_mapping.json', 'w', encoding='utf-8') as f:
            json.dump(strategy_mapping, f, ensure_ascii=False, indent=2)
        
        print(f"已更新 {len(strategy_mapping)} 个策略代码")
    
    def create_data_loader_functions(self):
        """创建数据加载函数"""
        loader_code = '''
# ==================== 生成数据加载函数 ====================

def load_generated_forum_posts():
    """加载生成的论坛帖子数据"""
    try:
        with open('generated_data/forum_posts.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return mock_forum_posts  # 回退到原始数据

def load_strategy_code(strategy_id):
    """加载策略代码"""
    try:
        with open('strategy_codes_mapping.json', 'r', encoding='utf-8') as f:
            codes = json.load(f)
            return codes.get(str(strategy_id), "# 策略代码未找到\\npass")
    except:
        return "# 策略代码加载失败\\npass"

def load_generated_comments():
    """加载生成的用户评论"""
    try:
        with open('generated_data/user_comments.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return []
'''
        
        # 读取app.py
        with open(self.app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已存在这些函数
        if 'load_generated_forum_posts' not in content:
            # 在适当位置插入函数
            insert_position = content.find('# ==================== 核心服务类 ====================')
            if insert_position != -1:
                content = content[:insert_position] + loader_code + '\n' + content[insert_position:]
                
                with open(self.app_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("已添加数据加载函数")
            else:
                print("未找到插入位置，请手动添加数据加载函数")
    
    def update_api_endpoints(self):
        """更新API端点以使用生成的数据"""
        print("正在更新API端点...")
        
        # 读取app.py
        with open(self.app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新论坛API
        forum_api_pattern = r'@app\.route\(\'/api/forum/posts\'\).*?return jsonify.*?\n'
        new_forum_api = '''@app.route('/api/forum/posts')
def get_forum_posts():
    """获取论坛帖子列表"""
    posts = load_generated_forum_posts()
    return jsonify({'success': True, 'posts': posts})
'''
        
        if re.search(forum_api_pattern, content, re.DOTALL):
            content = re.sub(forum_api_pattern, new_forum_api, content, flags=re.DOTALL)
        
        # 更新策略详情API
        strategy_api_pattern = r'@app\.route\(\'/api/strategy/<int:strategy_id>\'\).*?return jsonify.*?\n'
        new_strategy_api = '''@app.route('/api/strategy/<int:strategy_id>')
def get_strategy_detail(strategy_id):
    """获取策略详情"""
    code = load_strategy_code(strategy_id)
    
    # 查找对应的策略信息
    strategy = None
    for s in mock_strategies:
        if s['id'] == strategy_id:
            strategy = s
            break
    
    if strategy:
        strategy['code'] = code
        return jsonify({'success': True, 'strategy': strategy})
    else:
        return jsonify({'success': False, 'error': '策略未找到'})
'''
        
        if re.search(strategy_api_pattern, content, re.DOTALL):
            content = re.sub(strategy_api_pattern, new_strategy_api, content, flags=re.DOTALL)
        
        # 写回文件
        with open(self.app_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("API端点更新完成")
    
    def run_integration(self):
        """运行完整的数据集成流程"""
        print("=" * 50)
        print("QuantTradeX 数据集成器启动")
        print("=" * 50)
        
        # 检查生成的数据是否存在
        if not os.path.exists(self.data_dir):
            print(f"错误：未找到生成数据目录 {self.data_dir}")
            print("请先运行 data_generator.py 生成数据")
            return
        
        # 加载生成的数据
        data = self.load_generated_data()
        
        if not data:
            print("未找到生成的数据文件")
            return
        
        # 备份原始app.py
        backup_file = f"{self.app_file}.backup"
        if not os.path.exists(backup_file):
            with open(self.app_file, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已备份原始文件到 {backup_file}")
        
        # 集成数据
        if 'forum_posts' in data:
            self.update_forum_data(data['forum_posts'])
        
        if 'strategy_codes' in data:
            self.update_strategy_codes(data['strategy_codes'])
        
        # 创建数据加载函数
        self.create_data_loader_functions()
        
        # 更新API端点
        self.update_api_endpoints()
        
        print("=" * 50)
        print("数据集成完成！")
        print("建议重启Flask应用以加载新数据")
        print("=" * 50)

if __name__ == "__main__":
    integrator = DataIntegrator()
    integrator.run_integration()
