"""
API数据源配置和管理
支持多个数据提供商的API集成
"""

import os
import requests
import time
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class APIConfig:
    """API配置类"""

    # Alpha Vantage API (股票数据)
    ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY', 'YOUR_ALPHA_VANTAGE_KEY_HERE')
    ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query'
    ALPHA_VANTAGE_RATE_LIMIT = 5  # 每分钟5次请求

    # CoinGecko API (加密货币数据)
    COINGECKO_API_KEY = os.getenv('COINGECKO_API_KEY', 'YOUR_COINGECKO_KEY_HERE')
    COINGECKO_BASE_URL = 'https://api.coingecko.com/api/v3'
    COINGECKO_RATE_LIMIT = 10  # 每分钟10次请求

    # Forex API (外汇数据)
    FOREX_API_KEY = os.getenv('FOREX_API_KEY', 'YOUR_FOREX_KEY_HERE')
    FOREX_BASE_URL = 'https://api.exchangerate-api.com/v4'
    FOREX_RATE_LIMIT = 1000  # 每月1000次请求

    # Yahoo Finance (免费数据源)
    YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart'
    YAHOO_FINANCE_RATE_LIMIT = 2000  # 每小时2000次请求

    # Twelve Data API (综合金融数据)
    TWELVE_DATA_API_KEY = os.getenv('TWELVE_DATA_API_KEY', 'YOUR_TWELVE_DATA_KEY_HERE')
    TWELVE_DATA_BASE_URL = 'https://api.twelvedata.com'
    TWELVE_DATA_RATE_LIMIT = 800  # 每分钟800次请求

class RateLimiter:
    """API请求频率限制器"""

    def __init__(self, calls_per_minute=60):
        self.calls_per_minute = calls_per_minute
        self.calls = []

    def wait_if_needed(self):
        """如果需要，等待以避免超过频率限制"""
        now = time.time()

        # 移除一分钟前的调用记录
        self.calls = [call_time for call_time in self.calls if now - call_time < 60]

        # 如果达到频率限制，等待
        if len(self.calls) >= self.calls_per_minute:
            sleep_time = 60 - (now - self.calls[0])
            if sleep_time > 0:
                logger.info(f"API频率限制，等待 {sleep_time:.2f} 秒")
                time.sleep(sleep_time)

        # 记录这次调用
        self.calls.append(now)

class DataProviderManager:
    """数据提供商管理器"""

    def __init__(self):
        self.providers = {
            'alpha_vantage': {
                'name': 'Alpha Vantage',
                'description': '专业股票和外汇数据',
                'supported_types': ['stock', 'forex'],
                'rate_limiter': RateLimiter(APIConfig.ALPHA_VANTAGE_RATE_LIMIT),
                'status': 'active' if APIConfig.ALPHA_VANTAGE_API_KEY != 'YOUR_ALPHA_VANTAGE_KEY_HERE' else 'inactive'
            },
            'coingecko': {
                'name': 'CoinGecko',
                'description': '加密货币市场数据',
                'supported_types': ['crypto'],
                'rate_limiter': RateLimiter(APIConfig.COINGECKO_RATE_LIMIT),
                'status': 'active' if APIConfig.COINGECKO_API_KEY != 'YOUR_COINGECKO_KEY_HERE' else 'inactive'
            },
            'yahoo_finance': {
                'name': 'Yahoo Finance',
                'description': '免费股票和指数数据',
                'supported_types': ['stock', 'index'],
                'rate_limiter': RateLimiter(APIConfig.YAHOO_FINANCE_RATE_LIMIT // 60),
                'status': 'active'  # 免费API，默认可用
            },
            'twelve_data': {
                'name': 'Twelve Data',
                'description': '综合金融市场数据',
                'supported_types': ['stock', 'forex', 'crypto', 'futures'],
                'rate_limiter': RateLimiter(APIConfig.TWELVE_DATA_RATE_LIMIT),
                'status': 'active' if APIConfig.TWELVE_DATA_API_KEY != 'YOUR_TWELVE_DATA_KEY_HERE' else 'inactive'
            }
        }

    def get_provider_for_type(self, data_type):
        """根据数据类型获取最佳提供商"""
        active_providers = [
            provider_id for provider_id, provider in self.providers.items()
            if provider['status'] == 'active' and data_type in provider['supported_types']
        ]

        if not active_providers:
            return None

        # 优先级排序：付费API > 免费API
        priority_order = ['twelve_data', 'alpha_vantage', 'coingecko', 'yahoo_finance']

        for provider_id in priority_order:
            if provider_id in active_providers:
                return provider_id

        return active_providers[0]

    def get_provider_status(self):
        """获取所有提供商状态"""
        return {
            provider_id: {
                'name': provider['name'],
                'description': provider['description'],
                'supported_types': provider['supported_types'],
                'status': provider['status']
            }
            for provider_id, provider in self.providers.items()
        }

class UnifiedDataAPI:
    """统一数据API接口"""

    def __init__(self):
        self.provider_manager = DataProviderManager()
        self.cache = {}  # 简单内存缓存
        self.cache_ttl = 300  # 缓存5分钟

    def get_stock_data(self, symbol, interval='1d', provider=None):
        """获取股票数据"""
        if not provider:
            provider = self.provider_manager.get_provider_for_type('stock')

        if not provider:
            logger.error("没有可用的股票数据提供商")
            return None

        cache_key = f"{provider}:stock:{symbol}:{interval}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        try:
            if provider == 'alpha_vantage':
                data = self._get_alpha_vantage_stock_data(symbol, interval)
            elif provider == 'yahoo_finance':
                data = self._get_yahoo_finance_data(symbol, interval)
            elif provider == 'twelve_data':
                data = self._get_twelve_data_stock_data(symbol, interval)
            else:
                logger.error(f"不支持的股票数据提供商: {provider}")
                return None

            if data:
                self._cache_data(cache_key, data)

            return data

        except Exception as e:
            logger.error(f"获取股票数据失败 ({provider}): {e}")
            return None

    def get_crypto_data(self, symbol, interval='1d', provider=None):
        """获取加密货币数据"""
        if not provider:
            provider = self.provider_manager.get_provider_for_type('crypto')

        if not provider:
            logger.error("没有可用的加密货币数据提供商")
            return None

        cache_key = f"{provider}:crypto:{symbol}:{interval}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        try:
            if provider == 'coingecko':
                data = self._get_coingecko_data(symbol, interval)
            elif provider == 'twelve_data':
                data = self._get_twelve_data_crypto_data(symbol, interval)
            else:
                logger.error(f"不支持的加密货币数据提供商: {provider}")
                return None

            if data:
                self._cache_data(cache_key, data)

            return data

        except Exception as e:
            logger.error(f"获取加密货币数据失败 ({provider}): {e}")
            return None

    def _get_coingecko_data(self, symbol, interval='1d'):
        """从CoinGecko获取加密货币数据"""
        provider = self.provider_manager.providers['coingecko']
        provider['rate_limiter'].wait_if_needed()

        # CoinGecko API调用
        url = f"{APIConfig.COINGECKO_BASE_URL}/simple/price"
        params = {
            'ids': symbol,
            'vs_currencies': 'usd',
            'include_24hr_change': 'true',
            'include_24hr_vol': 'true',
            'include_market_cap': 'true'
        }

        if APIConfig.COINGECKO_API_KEY:
            params['x_cg_demo_api_key'] = APIConfig.COINGECKO_API_KEY

        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if symbol not in data:
            logger.error(f"CoinGecko未找到数据: {symbol}")
            return None

        crypto_data = data[symbol]

        # 转换为标准格式
        return {
            'success': True,
            'data': {
                'symbol': symbol.upper(),
                'price': crypto_data.get('usd', 0),
                'change_24h': crypto_data.get('usd_24h_change', 0),
                'change_24h_percent': crypto_data.get('usd_24h_change', 0),
                'volume_24h': crypto_data.get('usd_24h_vol', 0),
                'market_cap': crypto_data.get('usd_market_cap', 0),
                'provider': 'coingecko',
                'timestamp': datetime.now().isoformat()
            },
            'source': 'CoinGecko'
        }

    def _get_alpha_vantage_stock_data(self, symbol, interval):
        """从Alpha Vantage获取股票数据"""
        provider = self.provider_manager.providers['alpha_vantage']
        provider['rate_limiter'].wait_if_needed()

        params = {
            'function': 'TIME_SERIES_DAILY',
            'symbol': symbol,
            'apikey': APIConfig.ALPHA_VANTAGE_API_KEY,
            'outputsize': 'compact'
        }

        response = requests.get(APIConfig.ALPHA_VANTAGE_BASE_URL, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if 'Error Message' in data:
            logger.error(f"Alpha Vantage错误: {data['Error Message']}")
            return None

        if 'Time Series (Daily)' not in data:
            logger.error("Alpha Vantage返回数据格式错误")
            return None

        # 转换为标准格式
        time_series = data['Time Series (Daily)']
        dates = sorted(time_series.keys())[-30:]  # 最近30天

        return {
            'symbol': symbol,
            'provider': 'alpha_vantage',
            'dates': dates,
            'open': [float(time_series[date]['1. open']) for date in dates],
            'high': [float(time_series[date]['2. high']) for date in dates],
            'low': [float(time_series[date]['3. low']) for date in dates],
            'close': [float(time_series[date]['4. close']) for date in dates],
            'volume': [int(time_series[date]['5. volume']) for date in dates],
            'timestamp': datetime.now().isoformat()
        }

    def _get_yahoo_finance_data(self, symbol, interval):
        """从Yahoo Finance获取数据"""
        provider = self.provider_manager.providers['yahoo_finance']
        provider['rate_limiter'].wait_if_needed()

        # Yahoo Finance需要特定的时间范围
        end_time = int(time.time())
        start_time = end_time - (30 * 24 * 60 * 60)  # 30天前

        url = f"{APIConfig.YAHOO_FINANCE_BASE_URL}/{symbol}"
        params = {
            'period1': start_time,
            'period2': end_time,
            'interval': '1d',
            'includePrePost': 'false'
        }

        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if 'chart' not in data or not data['chart']['result']:
            logger.error("Yahoo Finance返回数据格式错误")
            return None

        result = data['chart']['result'][0]
        timestamps = result['timestamp']
        quotes = result['indicators']['quote'][0]

        # 转换为标准格式
        dates = [datetime.fromtimestamp(ts).strftime('%Y-%m-%d') for ts in timestamps]

        return {
            'symbol': symbol,
            'provider': 'yahoo_finance',
            'dates': dates,
            'open': quotes['open'],
            'high': quotes['high'],
            'low': quotes['low'],
            'close': quotes['close'],
            'volume': quotes['volume'],
            'timestamp': datetime.now().isoformat()
        }

    def _get_cached_data(self, cache_key):
        """获取缓存数据"""
        if cache_key in self.cache:
            cached_item = self.cache[cache_key]
            if time.time() - cached_item['timestamp'] < self.cache_ttl:
                return cached_item['data']
            else:
                del self.cache[cache_key]
        return None

    def _cache_data(self, cache_key, data):
        """缓存数据"""
        self.cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }

# 全局实例
unified_api = UnifiedDataAPI()
