#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
认证相关路由
负责用户登录、注册、登出等功能
"""

from flask import Blueprint, request, jsonify, session, redirect, url_for
from services.auth_service import auth_service
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'success': False, 'error': '用户名和密码不能为空'})

        # 验证用户
        result = auth_service.authenticate_user(username, password)

        if result['success']:
            user = result['user']
            # 设置永久会话
            session.permanent = True
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            session['is_premium'] = user['is_premium']
            session['full_name'] = user['full_name']
            session['avatar_url'] = user['avatar_url']

            return jsonify({
                'success': True,
                'message': '登录成功',
                'user': user,
                'redirect_url': '/dashboard'
            })
        else:
            return jsonify(result)

    except Exception as e:
        logger.error(f"登录失败: {e}")
        return jsonify({'success': False, 'error': '登录服务异常'})

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['username', 'email', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'error': f'{field} 不能为空'})

        # 验证密码长度
        if len(data['password']) < 6:
            return jsonify({'success': False, 'error': '密码长度至少6位'})

        # 注册用户
        result = auth_service.register_user(data)

        if result['success']:
            user = result['user']
            # 自动登录并设置永久会话
            session.permanent = True
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            session['is_premium'] = False
            session['full_name'] = user['full_name']

            return jsonify({
                'success': True,
                'message': '注册成功',
                'user': user,
                'redirect_url': '/dashboard'
            })
        else:
            return jsonify(result)

    except Exception as e:
        logger.error(f"注册失败: {e}")
        return jsonify({'success': False, 'error': '注册服务异常'})

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户登出"""
    try:
        # 清除会话
        session.clear()
        return jsonify({
            'success': True,
            'message': '已安全退出',
            'redirect_url': '/'
        })
    except Exception as e:
        logger.error(f"登出失败: {e}")
        return jsonify({'success': False, 'error': '登出失败'})

@auth_bp.route('/profile', methods=['GET'])
def get_profile():
    """获取用户资料"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        result = auth_service.get_user_profile(session['username'])
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取用户资料失败: {e}")
        return jsonify({'success': False, 'error': '获取资料失败'})

@auth_bp.route('/profile', methods=['PUT'])
def update_profile():
    """更新用户资料"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        result = auth_service.update_user_profile(session['username'], data)

        # 更新会话中的信息
        if result['success'] and 'full_name' in data:
            session['full_name'] = data['full_name']

        return jsonify(result)
    except Exception as e:
        logger.error(f"更新用户资料失败: {e}")
        return jsonify({'success': False, 'error': '更新失败'})

@auth_bp.route('/2fa/setup', methods=['POST'])
def setup_two_factor():
    """设置双因子认证"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        result = auth_service.setup_two_factor(session['username'])
        return jsonify(result)
    except Exception as e:
        logger.error(f"设置双因子认证失败: {e}")
        return jsonify({'success': False, 'error': '设置失败'})

@auth_bp.route('/2fa/verify', methods=['POST'])
def verify_two_factor():
    """验证双因子认证"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        token = data.get('token')

        if not token:
            return jsonify({'success': False, 'error': '验证码不能为空'})

        result = auth_service.verify_two_factor(session['username'], token)
        return jsonify(result)
    except Exception as e:
        logger.error(f"验证双因子认证失败: {e}")
        return jsonify({'success': False, 'error': '验证失败'})

@auth_bp.route('/check', methods=['GET'])
def check_auth():
    """检查登录状态"""
    if 'username' in session:
        return jsonify({
            'success': True,
            'authenticated': True,
            'user': {
                'id': session.get('user_id'),
                'username': session.get('username'),
                'role': session.get('role'),
                'full_name': session.get('full_name'),
                'is_premium': session.get('is_premium', False),
                'avatar_url': session.get('avatar_url')
            }
        })
    else:
        return jsonify({
            'success': True,
            'authenticated': False
        })

@auth_bp.route('/change-password', methods=['POST'])
def change_password():
    """修改密码"""
    if 'username' not in session:
        return jsonify({'success': False, 'error': '请先登录'})

    try:
        data = request.get_json()
        current_password = data.get('current_password')
        new_password = data.get('new_password')

        if not current_password or not new_password:
            return jsonify({'success': False, 'error': '当前密码和新密码不能为空'})

        if len(new_password) < 6:
            return jsonify({'success': False, 'error': '新密码长度至少6位'})

        # 验证当前密码
        auth_result = auth_service.authenticate_user(session['username'], current_password)
        if not auth_result['success']:
            return jsonify({'success': False, 'error': '当前密码错误'})

        # 更新密码（这里简化处理，实际应该加密存储）
        user = auth_service.users.get(session['username'])
        if user:
            user['password'] = new_password
            return jsonify({'success': True, 'message': '密码修改成功'})
        else:
            return jsonify({'success': False, 'error': '用户不存在'})

    except Exception as e:
        logger.error(f"修改密码失败: {e}")
        return jsonify({'success': False, 'error': '修改密码失败'})
