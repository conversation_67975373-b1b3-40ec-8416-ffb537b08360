#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
主要页面路由
负责渲染各个页面模板
"""

from flask import Blueprint, render_template, session, redirect, url_for
from services.auth_service import login_required

# 创建蓝图
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """主页"""
    return render_template('index.html')

@main_bp.route('/dashboard')
def dashboard():
    """交易仪表板"""
    return render_template('dashboard.html')

@main_bp.route('/strategies')
def strategies():
    """策略市场"""
    return render_template('strategies.html')

@main_bp.route('/backtest')
def backtest():
    """回测页面"""
    return render_template('backtest.html')

@main_bp.route('/forum')
def forum():
    """论坛页面"""
    return render_template('forum.html')

@main_bp.route('/strategy-editor')
def strategy_editor():
    """策略开发页面"""
    return render_template('strategy_editor.html')

@main_bp.route('/security')
def security():
    """安全设置页面"""
    return render_template('security.html')

@main_bp.route('/realtime')
def realtime():
    """实时数据流页面"""
    return render_template('realtime.html')

@main_bp.route('/realtime_test')
def realtime_test():
    """实时数据流测试页面"""
    return render_template('realtime_test.html')

@main_bp.route('/advanced_backtest')
def advanced_backtest():
    """高级回测页面"""
    return render_template('advanced_backtest.html')

@main_bp.route('/profile')
@login_required
def profile_page():
    """个人资料页面"""
    return render_template('profile.html')

@main_bp.route('/datacenter')
def datacenter():
    """数据中心页面"""
    return render_template('datacenter.html')

@main_bp.route('/demo')
def feature_demo():
    """新功能演示页面"""
    return render_template('feature_demo.html')

# 错误处理页面
@main_bp.errorhandler(404)
def not_found_error(error):
    """404错误页面"""
    return render_template('errors/404.html'), 404

@main_bp.errorhandler(500)
def internal_error(error):
    """500错误页面"""
    return render_template('errors/500.html'), 500

@main_bp.errorhandler(403)
def forbidden_error(error):
    """403错误页面"""
    return render_template('errors/403.html'), 403
