#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实数据获取功能
验证BTC和其他加密货币的真实价格数据
"""

import requests
import json
from datetime import datetime

def test_coingecko_direct():
    """直接测试CoinGecko API"""
    print("=== 测试CoinGecko直接API ===")
    
    try:
        url = "https://api.coingecko.com/api/v3/simple/price"
        params = {
            'ids': 'bitcoin,ethereum,binancecoin',
            'vs_currencies': 'usd',
            'include_24hr_change': 'true',
            'include_24hr_vol': 'true',
            'include_market_cap': 'true'
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        print("✅ CoinGecko API调用成功")
        print(f"📊 获取到的数据:")
        
        for crypto_id, info in data.items():
            print(f"\n{crypto_id.upper()}:")
            print(f"  价格: ${info.get('usd', 0):,.2f}")
            print(f"  24h变化: {info.get('usd_24h_change', 0):.2f}%")
            print(f"  24h交易量: ${info.get('usd_24h_vol', 0):,.0f}")
            print(f"  市值: ${info.get('usd_market_cap', 0):,.0f}")
        
        return True
        
    except Exception as e:
        print(f"❌ CoinGecko API调用失败: {e}")
        return False

def test_app_crypto_api():
    """测试应用的加密货币API"""
    print("\n=== 测试应用加密货币API ===")
    
    try:
        # 测试BTC数据
        response = requests.get('http://127.0.0.1:5000/api/realtime/crypto/BTC', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 应用API调用成功")
            print(f"📊 BTC数据:")
            print(f"  价格: ${data.get('price', 0):,.2f}")
            print(f"  24h变化: {data.get('change_percent', 0):.2f}%")
            print(f"  数据源: {data.get('source', 'Unknown')}")
            print(f"  更新时间: {data.get('last_update', 'Unknown')}")
            return True
        else:
            print(f"❌ 应用API调用失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 应用API调用失败: {e}")
        return False

def test_unified_api():
    """测试统一API"""
    print("\n=== 测试统一API ===")
    
    try:
        from api_config import unified_api
        
        if unified_api:
            print("✅ 统一API模块加载成功")
            
            # 测试获取BTC数据
            result = unified_api.get_crypto_data('bitcoin')
            
            if result and result.get('success'):
                data = result['data']
                print("✅ 统一API获取数据成功")
                print(f"📊 BTC数据:")
                print(f"  价格: ${data.get('price', 0):,.2f}")
                print(f"  24h变化: {data.get('change_24h_percent', 0):.2f}%")
                print(f"  数据源: {result.get('source', 'Unknown')}")
                return True
            else:
                print(f"❌ 统一API获取数据失败: {result.get('error', 'Unknown error')}")
                return False
        else:
            print("❌ 统一API模块未加载")
            return False
            
    except Exception as e:
        print(f"❌ 统一API测试失败: {e}")
        return False

def test_data_providers_status():
    """测试数据提供商状态"""
    print("\n=== 测试数据提供商状态 ===")
    
    try:
        response = requests.get('http://127.0.0.1:5000/api/data-providers', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 数据提供商状态API调用成功")
            
            if data.get('success'):
                providers = data.get('providers', {})
                print(f"📊 数据提供商状态:")
                
                for provider_id, info in providers.items():
                    status = "✅ 已配置" if info.get('status') == 'active' else "❌ 未配置"
                    print(f"  {info.get('name', provider_id)}: {status}")
                    if info.get('supported_types'):
                        print(f"    支持类型: {', '.join(info['supported_types'])}")
                
                return True
            else:
                print(f"❌ 获取提供商状态失败: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ 数据提供商状态API调用失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 数据提供商状态测试失败: {e}")
        return False

def compare_prices():
    """比较不同数据源的价格"""
    print("\n=== 价格数据对比 ===")
    
    # 获取CoinGecko直接数据
    coingecko_price = None
    try:
        response = requests.get(
            "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd",
            timeout=5
        )
        if response.status_code == 200:
            data = response.json()
            coingecko_price = data.get('bitcoin', {}).get('usd')
    except:
        pass
    
    # 获取应用API数据
    app_price = None
    try:
        response = requests.get('http://127.0.0.1:5000/api/realtime/crypto/BTC', timeout=5)
        if response.status_code == 200:
            data = response.json()
            app_price = data.get('price')
    except:
        pass
    
    print(f"CoinGecko直接API: ${coingecko_price:,.2f}" if coingecko_price else "CoinGecko直接API: 获取失败")
    print(f"应用API: ${app_price:,.2f}" if app_price else "应用API: 获取失败")
    
    if coingecko_price and app_price:
        diff = abs(coingecko_price - app_price)
        diff_percent = (diff / coingecko_price) * 100
        print(f"价格差异: ${diff:,.2f} ({diff_percent:.2f}%)")
        
        if diff_percent < 1:
            print("✅ 价格数据一致性良好")
        else:
            print("⚠️ 价格数据存在较大差异")

def main():
    """主测试函数"""
    print("🚀 开始测试真实数据获取功能")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    results = []
    
    # 测试CoinGecko直接API
    results.append(("CoinGecko直接API", test_coingecko_direct()))
    
    # 测试统一API
    results.append(("统一API", test_unified_api()))
    
    # 测试数据提供商状态
    results.append(("数据提供商状态", test_data_providers_status()))
    
    # 测试应用API
    results.append(("应用加密货币API", test_app_crypto_api()))
    
    # 价格对比
    compare_prices()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！真实数据获取功能正常工作")
    elif passed > 0:
        print("⚠️ 部分测试通过，建议检查失败的项目")
    else:
        print("❌ 所有测试失败，需要检查配置和网络连接")

if __name__ == "__main__":
    main()
