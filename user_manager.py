#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理模块
提供真正的数据库用户管理功能，替换内存中的MOCK_USERS
"""

import psycopg2
import hashlib
import json
from datetime import datetime
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'quanttradex',
    'user': 'quanttradex_user',
    'password': 'quanttradex123'
}

class UserManager:
    """用户管理类"""
    
    def __init__(self):
        self.connection = None
        self.connect()
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = psycopg2.connect(**DB_CONFIG)
            logger.info("用户管理器数据库连接成功")
        except Exception as e:
            logger.error(f"用户管理器数据库连接失败: {e}")
            self.connection = None
    
    def hash_password(self, password):
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password, password_hash):
        """验证密码"""
        return self.hash_password(password) == password_hash
    
    def get_user_by_username(self, username):
        """根据用户名获取用户"""
        if not self.connection:
            return None
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT id, username, email, password_hash, full_name, role, avatar_url, bio,
                       phone, region, risk_preference, experience, created_at, updated_at,
                       last_login, is_premium, premium_expires, total_strategies, total_backtests,
                       total_profit, win_rate, followers, following, two_factor_enabled,
                       two_factor_secret, backup_codes
                FROM users WHERE username = %s
            """, (username,))
            
            row = cursor.fetchone()
            cursor.close()
            
            if row:
                return {
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'password': row[3],  # 这是哈希后的密码
                    'full_name': row[4],
                    'role': row[5],
                    'avatar_url': row[6],
                    'bio': row[7],
                    'phone': row[8],
                    'region': row[9],
                    'risk_preference': row[10],
                    'experience': row[11],
                    'created_at': row[12].isoformat() if row[12] else None,
                    'updated_at': row[13].isoformat() if row[13] else None,
                    'last_login': row[14].isoformat() if row[14] else None,
                    'is_premium': row[15],
                    'premium_expires': row[16].isoformat() if row[16] else None,
                    'total_strategies': row[17],
                    'total_backtests': row[18],
                    'total_profit': float(row[19]) if row[19] else 0.0,
                    'win_rate': float(row[20]) if row[20] else 0.0,
                    'followers': row[21],
                    'following': row[22],
                    'two_factor_enabled': row[23],
                    'two_factor_secret': row[24],
                    'backup_codes': json.loads(row[25]) if row[25] else []
                }
            return None
            
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    def get_user_by_email(self, email):
        """根据邮箱获取用户"""
        if not self.connection:
            return None
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT username FROM users WHERE email = %s", (email,))
            row = cursor.fetchone()
            cursor.close()
            
            if row:
                return self.get_user_by_username(row[0])
            return None
            
        except Exception as e:
            logger.error(f"根据邮箱获取用户失败: {e}")
            return None
    
    def create_user(self, username, email, password, full_name='', **kwargs):
        """创建新用户"""
        if not self.connection:
            return False, "数据库连接失败"
        
        try:
            cursor = self.connection.cursor()
            
            # 检查用户名是否已存在
            cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
            if cursor.fetchone():
                cursor.close()
                return False, "用户名已存在"
            
            # 检查邮箱是否已存在
            cursor.execute("SELECT id FROM users WHERE email = %s", (email,))
            if cursor.fetchone():
                cursor.close()
                return False, "邮箱已被注册"
            
            # 插入新用户
            cursor.execute("""
                INSERT INTO users (
                    username, email, password_hash, full_name, role, avatar_url, bio,
                    phone, region, risk_preference, experience
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (
                username,
                email,
                self.hash_password(password),
                full_name,
                kwargs.get('role', 'user'),
                kwargs.get('avatar_url', '/static/img/avatar-default.png'),
                kwargs.get('bio', ''),
                kwargs.get('phone', ''),
                kwargs.get('region', ''),
                kwargs.get('risk_preference', ''),
                kwargs.get('experience', '')
            ))
            
            user_id = cursor.fetchone()[0]
            self.connection.commit()
            cursor.close()
            
            logger.info(f"创建用户成功: {username} (ID: {user_id})")
            return True, "注册成功"
            
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            self.connection.rollback()
            return False, "注册失败，请稍后重试"
    
    def update_user(self, username, **kwargs):
        """更新用户信息"""
        if not self.connection:
            return False, "数据库连接失败"
        
        try:
            cursor = self.connection.cursor()
            
            # 构建更新语句
            update_fields = []
            values = []
            
            for field in ['full_name', 'email', 'bio', 'phone', 'region', 'risk_preference', 'experience']:
                if field in kwargs:
                    update_fields.append(f"{field} = %s")
                    values.append(kwargs[field])
            
            if not update_fields:
                cursor.close()
                return True, "没有需要更新的字段"
            
            # 添加更新时间
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            values.append(username)
            
            sql = f"UPDATE users SET {', '.join(update_fields)} WHERE username = %s"
            cursor.execute(sql, values)
            
            self.connection.commit()
            cursor.close()
            
            logger.info(f"更新用户信息成功: {username}")
            return True, "更新成功"
            
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            self.connection.rollback()
            return False, "更新失败，请稍后重试"
    
    def update_last_login(self, username):
        """更新最后登录时间"""
        if not self.connection:
            return
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(
                "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE username = %s",
                (username,)
            )
            self.connection.commit()
            cursor.close()
            
        except Exception as e:
            logger.error(f"更新登录时间失败: {e}")
    
    def upgrade_to_premium(self, username, plan):
        """升级用户为VIP"""
        if not self.connection:
            return False, "数据库连接失败"
        
        try:
            cursor = self.connection.cursor()
            
            # 计算到期时间
            if plan == 'monthly':
                expires_sql = "CURRENT_TIMESTAMP + INTERVAL '30 days'"
            elif plan == 'yearly':
                expires_sql = "CURRENT_TIMESTAMP + INTERVAL '365 days'"
            elif plan == 'lifetime':
                expires_sql = "'2099-12-31 23:59:59'"
            else:
                cursor.close()
                return False, "无效的会员计划"
            
            cursor.execute(f"""
                UPDATE users 
                SET is_premium = TRUE, premium_expires = {expires_sql}
                WHERE username = %s
            """, (username,))
            
            self.connection.commit()
            cursor.close()
            
            logger.info(f"用户 {username} 升级为VIP成功，计划: {plan}")
            return True, "VIP升级成功"
            
        except Exception as e:
            logger.error(f"VIP升级失败: {e}")
            self.connection.rollback()
            return False, "升级失败，请稍后重试"
    
    def get_all_users(self):
        """获取所有用户（管理员功能）"""
        if not self.connection:
            return []
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT id, username, email, full_name, role, created_at, is_premium,
                       total_strategies, total_backtests, followers
                FROM users ORDER BY created_at DESC
            """)
            
            users = []
            for row in cursor.fetchall():
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'full_name': row[3],
                    'role': row[4],
                    'created_at': row[5].isoformat() if row[5] else None,
                    'is_premium': row[6],
                    'total_strategies': row[7],
                    'total_backtests': row[8],
                    'followers': row[9]
                })
            
            cursor.close()
            return users
            
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return []
    
    def get_user_watchlist(self, username):
        """获取用户关注列表"""
        if not self.connection:
            return []
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT w.symbol, w.asset_type, w.name, w.added_at
                FROM user_watchlists w
                JOIN users u ON w.user_id = u.id
                WHERE u.username = %s
                ORDER BY w.added_at DESC
            """, (username,))
            
            watchlist = []
            for row in cursor.fetchall():
                watchlist.append({
                    'symbol': row[0],
                    'type': row[1],
                    'name': row[2],
                    'added_at': row[3].isoformat() if row[3] else None
                })
            
            cursor.close()
            return watchlist
            
        except Exception as e:
            logger.error(f"获取关注列表失败: {e}")
            return []
    
    def add_to_watchlist(self, username, symbol, asset_type, name=''):
        """添加到关注列表"""
        if not self.connection:
            return False, "数据库连接失败"
        
        try:
            cursor = self.connection.cursor()
            
            # 获取用户ID
            cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
            user_row = cursor.fetchone()
            if not user_row:
                cursor.close()
                return False, "用户不存在"
            
            user_id = user_row[0]
            
            # 检查是否已存在
            cursor.execute("""
                SELECT id FROM user_watchlists 
                WHERE user_id = %s AND symbol = %s AND asset_type = %s
            """, (user_id, symbol, asset_type))
            
            if cursor.fetchone():
                cursor.close()
                return False, "已在关注列表中"
            
            # 添加到关注列表
            cursor.execute("""
                INSERT INTO user_watchlists (user_id, symbol, asset_type, name)
                VALUES (%s, %s, %s, %s)
            """, (user_id, symbol, asset_type, name))
            
            self.connection.commit()
            cursor.close()
            
            logger.info(f"用户 {username} 添加关注: {symbol} ({asset_type})")
            return True, "添加成功"
            
        except Exception as e:
            logger.error(f"添加关注失败: {e}")
            self.connection.rollback()
            return False, "添加失败，请稍后重试"
    
    def remove_from_watchlist(self, username, symbol, asset_type):
        """从关注列表移除"""
        if not self.connection:
            return False, "数据库连接失败"
        
        try:
            cursor = self.connection.cursor()
            
            cursor.execute("""
                DELETE FROM user_watchlists 
                WHERE user_id = (SELECT id FROM users WHERE username = %s)
                AND symbol = %s AND asset_type = %s
            """, (username, symbol, asset_type))
            
            deleted_count = cursor.rowcount
            self.connection.commit()
            cursor.close()
            
            if deleted_count > 0:
                logger.info(f"用户 {username} 移除关注: {symbol} ({asset_type})")
                return True, "移除成功"
            else:
                return False, "关注项不存在"
            
        except Exception as e:
            logger.error(f"移除关注失败: {e}")
            self.connection.rollback()
            return False, "移除失败，请稍后重试"
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("用户管理器数据库连接已关闭")

# 创建全局用户管理器实例
user_manager = UserManager()
