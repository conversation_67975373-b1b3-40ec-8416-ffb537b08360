<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantTradeX 登录演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            color: white;
        }

        .demo-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            color: white;
            padding: 2rem;
            margin: 2rem 0;
        }

        .btn-demo {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            border: none;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
            margin: 0.5rem;
        }

        .btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
            color: white;
        }

        .user-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-available {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-login-required {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="text-center mb-5">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-chart-line me-3"></i>
                        QuantTradeX 功能演示
                    </h1>
                    <p class="lead">体验完整的量化交易平台功能</p>
                </div>

                <!-- 功能状态说明 -->
                <div class="demo-card">
                    <h3 class="mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        功能访问说明
                    </h3>
                    <ul class="feature-list">
                        <li class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-home me-2"></i>首页</span>
                            <span class="status-badge status-available">公开访问</span>
                        </li>
                        <li class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-store me-2"></i>策略市场</span>
                            <span class="status-badge status-available">公开访问</span>
                        </li>
                        <li class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-comments me-2"></i>社区论坛</span>
                            <span class="status-badge status-available">公开访问</span>
                        </li>
                        <li class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-code me-2"></i>策略开发</span>
                            <span class="status-badge status-login-required">需要登录</span>
                        </li>
                        <li class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-history me-2"></i>回测系统</span>
                            <span class="status-badge status-login-required">需要登录</span>
                        </li>
                        <li class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-tachometer-alt me-2"></i>交易仪表板</span>
                            <span class="status-badge status-login-required">需要登录</span>
                        </li>
                    </ul>
                </div>

                <!-- 测试用户账号 -->
                <div class="demo-card">
                    <h3 class="mb-4">
                        <i class="fas fa-users me-2"></i>
                        测试用户账号
                    </h3>
                    <p class="text-white-50 mb-4">点击下方用户卡片可以快速登录体验不同权限的功能</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="user-card" onclick="quickLogin('demo_user', 'password123')">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0">
                                        <i class="fas fa-user me-2"></i>普通用户
                                    </h5>
                                    <span class="badge bg-primary">免费用户</span>
                                </div>
                                <p class="text-white-50 mb-2">
                                    <strong>用户名:</strong> demo_user<br>
                                    <strong>密码:</strong> password123
                                </p>
                                <small class="text-white-50">
                                    可以访问基础功能：策略开发、回测系统、论坛参与
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="user-card" onclick="quickLogin('premium_user', 'premium123')">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0">
                                        <i class="fas fa-crown me-2"></i>VIP用户
                                    </h5>
                                    <span class="badge bg-warning">VIP会员</span>
                                </div>
                                <p class="text-white-50 mb-2">
                                    <strong>用户名:</strong> premium_user<br>
                                    <strong>密码:</strong> premium123
                                </p>
                                <small class="text-white-50">
                                    可以访问所有功能：高级策略、实时数据、专属客服
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能入口 -->
                <div class="demo-card">
                    <h3 class="mb-4">
                        <i class="fas fa-rocket me-2"></i>
                        功能入口
                    </h3>
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <a href="/" class="btn-demo w-100">
                                <i class="fas fa-home"></i>
                                返回首页
                            </a>
                        </div>
                        <div class="col-md-4 text-center">
                            <a href="/strategies" class="btn-demo w-100">
                                <i class="fas fa-store"></i>
                                策略市场
                            </a>
                        </div>
                        <div class="col-md-4 text-center">
                            <a href="/forum" class="btn-demo w-100">
                                <i class="fas fa-comments"></i>
                                社区论坛
                            </a>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-4 text-center">
                            <a href="/strategy-editor" class="btn-demo w-100" id="strategyEditorLink">
                                <i class="fas fa-code"></i>
                                策略开发
                            </a>
                        </div>
                        <div class="col-md-4 text-center">
                            <a href="/backtest" class="btn-demo w-100" id="backtestLink">
                                <i class="fas fa-history"></i>
                                回测系统
                            </a>
                        </div>
                        <div class="col-md-4 text-center">
                            <a href="/dashboard" class="btn-demo w-100" id="dashboardLink">
                                <i class="fas fa-tachometer-alt"></i>
                                交易仪表板
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 当前登录状态 -->
                <div class="demo-card" id="loginStatus">
                    <h3 class="mb-3">
                        <i class="fas fa-user-check me-2"></i>
                        当前登录状态
                    </h3>
                    <div id="statusContent">
                        <p class="text-white-50">
                            <i class="fas fa-info-circle me-2"></i>
                            未登录 - 请使用上方测试账号登录体验完整功能
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 快速登录
        async function quickLogin(username, password) {
            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('登录成功！', 'success');
                    setTimeout(() => {
                        checkLoginStatus();
                    }, 1000);
                } else {
                    showNotification(result.error || '登录失败', 'error');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/auth/profile');
                const result = await response.json();

                if (result.success && result.user) {
                    updateLoginStatus(result.user);
                } else {
                    updateLoginStatus(null);
                }
            } catch (error) {
                updateLoginStatus(null);
            }
        }

        // 更新登录状态显示
        function updateLoginStatus(user) {
            const statusContent = document.getElementById('statusContent');
            
            if (user) {
                statusContent.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1">
                                <i class="fas fa-user-circle me-2"></i>
                                ${user.username}
                                ${user.is_premium ? '<span class="badge bg-warning ms-2">VIP</span>' : '<span class="badge bg-primary ms-2">免费</span>'}
                            </h5>
                            <p class="text-white-50 mb-0">
                                ${user.full_name || '未设置姓名'} | ${user.email}
                            </p>
                        </div>
                        <button class="btn btn-outline-light btn-sm" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-1"></i>退出登录
                        </button>
                    </div>
                    <div class="mt-3">
                        <small class="text-success">
                            <i class="fas fa-check-circle me-1"></i>
                            已登录 - 现在可以访问策略开发和回测系统了！
                        </small>
                    </div>
                `;
            } else {
                statusContent.innerHTML = `
                    <p class="text-white-50">
                        <i class="fas fa-info-circle me-2"></i>
                        未登录 - 请使用上方测试账号登录体验完整功能
                    </p>
                `;
            }
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('已退出登录', 'success');
                    setTimeout(() => {
                        checkLoginStatus();
                    }, 1000);
                }
            } catch (error) {
                console.error('退出登录错误:', error);
                showNotification('退出登录失败', 'error');
            }
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
