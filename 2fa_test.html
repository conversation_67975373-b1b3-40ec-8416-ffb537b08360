<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2FA功能测试 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            padding: 2rem;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            text-align: center;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .comparison-box {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .feature-list {
            background: rgba(0, 255, 0, 0.1);
            border-color: rgba(0, 255, 0, 0.3);
        }

        .demo-login {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .alert-info {
            background: rgba(6, 182, 212, 0.1);
            color: var(--accent);
            border: 1px solid rgba(6, 182, 212, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="text-center mb-4">
                    <h1 class="display-5 mb-3">
                        <i class="fas fa-shield-alt text-primary me-3"></i>
                        2FA双因素认证功能测试
                    </h1>
                    <p class="lead">完整的双因素认证系统演示和测试</p>
                </div>

                <!-- 功能统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">8</div>
                            <div>核心功能</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">3</div>
                            <div>设置步骤</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">10</div>
                            <div>备用验证码</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">100%</div>
                            <div>安全提升</div>
                        </div>
                    </div>
                </div>

                <!-- 功能特性 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star text-warning me-2"></i>
                            2FA功能特性
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="comparison-box feature-list">
                                    <h6 class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>核心功能
                                    </h6>
                                    <ul class="mb-0">
                                        <li>TOTP时间验证码生成</li>
                                        <li>QR码自动生成</li>
                                        <li>备用验证码系统</li>
                                        <li>三步设置向导</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="comparison-box feature-list">
                                    <h6 class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>安全特性
                                    </h6>
                                    <ul class="mb-0">
                                        <li>密钥安全存储</li>
                                        <li>登录双重验证</li>
                                        <li>备用码一次性使用</li>
                                        <li>安全禁用流程</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 演示登录 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            2FA登录演示
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="demo-login">
                                    <h6 class="mb-3">测试账户登录</h6>
                                    <div class="mb-3">
                                        <label class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="testUsername" value="admin" readonly>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">密码</label>
                                        <input type="password" class="form-control" id="testPassword" value="admin123" readonly>
                                    </div>
                                    <div class="mb-3" id="twoFactorCodeDiv" style="display: none;">
                                        <label class="form-label">2FA验证码</label>
                                        <input type="text" class="form-control" id="testTwoFactorCode" placeholder="请输入6位验证码" maxlength="6">
                                        <small class="text-muted">如果已启用2FA，请输入认证应用中的验证码</small>
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="testLogin()">
                                        <i class="fas fa-sign-in-alt me-1"></i>测试登录
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>测试说明</h6>
                                    <p class="mb-2"><strong>测试流程：</strong></p>
                                    <ol class="mb-0">
                                        <li>首先进入安全设置页面启用2FA</li>
                                        <li>使用认证应用扫描QR码</li>
                                        <li>保存备用验证码</li>
                                        <li>返回此页面测试2FA登录</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            功能测试
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <a href="/security" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-shield-alt me-2"></i>安全设置页面
                        </a>
                        <button class="btn btn-outline-light btn-lg me-3" onclick="check2FAStatus()">
                            <i class="fas fa-search me-2"></i>检查2FA状态
                        </button>
                        <button class="btn btn-outline-success btn-lg" onclick="showTestInstructions()">
                            <i class="fas fa-question-circle me-2"></i>测试说明
                        </button>
                    </div>
                </div>

                <!-- 技术实现 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-code me-2"></i>
                            技术实现
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <h6><i class="fab fa-python text-warning me-2"></i>后端技术</h6>
                                    <ul class="small mb-0">
                                        <li>PyOTP - TOTP算法</li>
                                        <li>QRCode - 二维码生成</li>
                                        <li>Cryptography - 加密存储</li>
                                        <li>Flask Session - 状态管理</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <h6><i class="fas fa-mobile-alt text-success me-2"></i>认证应用</h6>
                                    <ul class="small mb-0">
                                        <li>Google Authenticator</li>
                                        <li>Microsoft Authenticator</li>
                                        <li>Authy</li>
                                        <li>其他TOTP应用</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <h6><i class="fas fa-shield-alt text-primary me-2"></i>安全特性</h6>
                                    <ul class="small mb-0">
                                        <li>时间同步验证</li>
                                        <li>30秒有效期</li>
                                        <li>备用码应急访问</li>
                                        <li>安全密钥存储</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 返回按钮 -->
                <div class="text-center">
                    <a href="/security" class="btn btn-primary btn-lg">
                        <i class="fas fa-shield-alt me-2"></i>
                        进入安全设置
                    </a>
                    <a href="/" class="btn btn-outline-light btn-lg ms-3">
                        <i class="fas fa-home me-2"></i>
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试登录
        async function testLogin() {
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            const code = document.getElementById('testTwoFactorCode').value;

            try {
                const response = await fetch('/auth/login/2fa', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        code: code
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('登录成功！', 'success');
                    // 可以跳转到仪表板或其他页面
                } else if (result.require_2fa) {
                    document.getElementById('twoFactorCodeDiv').style.display = 'block';
                    showNotification('请输入2FA验证码', 'warning');
                } else {
                    showNotification(result.error, 'error');
                }
            } catch (error) {
                showNotification('登录失败，请稍后重试', 'error');
            }
        }

        // 检查2FA状态
        async function check2FAStatus() {
            try {
                const response = await fetch('/auth/2fa/status');
                const result = await response.json();

                if (result.success) {
                    const status = result.enabled ? '已启用' : '未启用';
                    const backupCodes = result.backup_codes_count || 0;
                    showNotification(`2FA状态：${status}，剩余备用验证码：${backupCodes} 个`, 'info');
                } else {
                    showNotification('请先登录', 'warning');
                }
            } catch (error) {
                showNotification('检查状态失败', 'error');
            }
        }

        // 显示测试说明
        function showTestInstructions() {
            const instructions = `
2FA功能测试步骤：

1. 📱 准备认证应用
   - 下载 Google Authenticator、Microsoft Authenticator 或 Authy
   - 确保手机时间同步准确

2. 🔐 启用2FA
   - 点击"安全设置页面"按钮
   - 使用测试账户登录（admin/admin123）
   - 按照三步向导设置2FA

3. 📋 保存备用验证码
   - 在设置过程中会生成10个备用验证码
   - 请妥善保存这些代码

4. 🧪 测试登录
   - 返回此页面
   - 使用测试账户登录
   - 输入认证应用中的6位验证码

5. ✅ 验证功能
   - 测试正常登录流程
   - 测试备用验证码登录
   - 测试禁用2FA功能

注意：这是演示环境，数据不会持久化保存。
            `;
            alert(instructions);
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            check2FAStatus();
        });
    </script>
</body>
</html>
