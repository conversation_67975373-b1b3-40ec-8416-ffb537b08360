# QuantTradeX API配置模板
# 请根据API申请教程.md获取相应的API Key后填入此文件

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class APIConfig:
    """API配置类 - 管理所有第三方API的配置信息"""
    
    # =============================================================================
    # 股票数据API - Alpha Vantage
    # =============================================================================
    ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY', 'YOUR_ALPHA_VANTAGE_KEY_HERE')
    ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query'
    ALPHA_VANTAGE_RATE_LIMIT = 5  # 每分钟5次调用（免费版）
    
    # Alpha Vantage API函数
    ALPHA_VANTAGE_FUNCTIONS = {
        'intraday': 'TIME_SERIES_INTRADAY',
        'daily': 'TIME_SERIES_DAILY',
        'weekly': 'TIME_SERIES_WEEKLY',
        'monthly': 'TIME_SERIES_MONTHLY',
        'quote': 'GLOBAL_QUOTE',
        'search': 'SYMBOL_SEARCH'
    }
    
    # =============================================================================
    # 数字货币API - CoinGecko
    # =============================================================================
    COINGECKO_API_KEY = os.getenv('COINGECKO_API_KEY', 'YOUR_COINGECKO_KEY_HERE')
    COINGECKO_BASE_URL = 'https://api.coingecko.com/api/v3'
    COINGECKO_RATE_LIMIT = 50  # 每分钟50次调用（免费版）
    
    # 支持的数字货币
    SUPPORTED_CRYPTOCURRENCIES = [
        'bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana',
        'polkadot', 'dogecoin', 'avalanche-2', 'shiba-inu', 'polygon'
    ]
    
    # =============================================================================
    # 外汇数据API - ExchangeRate-API
    # =============================================================================
    EXCHANGERATE_API_KEY = os.getenv('EXCHANGERATE_API_KEY', 'YOUR_EXCHANGERATE_KEY_HERE')
    EXCHANGERATE_BASE_URL = 'https://v6.exchangerate-api.com/v6'
    EXCHANGERATE_RATE_LIMIT = 1500  # 每月1500次调用（免费版）
    
    # 支持的货币对
    SUPPORTED_CURRENCIES = [
        'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'AUD', 'CAD', 'CHF', 'NZD', 'SEK'
    ]
    
    # =============================================================================
    # 期货数据API - Quandl/Nasdaq Data Link
    # =============================================================================
    QUANDL_API_KEY = os.getenv('QUANDL_API_KEY', 'YOUR_QUANDL_KEY_HERE')
    QUANDL_BASE_URL = 'https://data.nasdaq.com/api/v3'
    QUANDL_RATE_LIMIT = 50  # 每天50次调用（免费版）
    
    # 支持的期货合约
    SUPPORTED_FUTURES = {
        'CL': 'CHRIS/CME_CL1',  # 原油期货
        'GC': 'CHRIS/CME_GC1',  # 黄金期货
        'SI': 'CHRIS/CME_SI1',  # 白银期货
        'ES': 'CHRIS/CME_ES1',  # E-mini S&P 500
        'NQ': 'CHRIS/CME_NQ1'   # E-mini Nasdaq
    }
    
    # =============================================================================
    # 实盘交易API - Interactive Brokers (高级功能)
    # =============================================================================
    IBKR_HOST = os.getenv('IBKR_HOST', '127.0.0.1')
    IBKR_PORT = int(os.getenv('IBKR_PORT', '7497'))  # TWS端口
    IBKR_CLIENT_ID = int(os.getenv('IBKR_CLIENT_ID', '1'))
    IBKR_ACCOUNT = os.getenv('IBKR_ACCOUNT', 'YOUR_IBKR_ACCOUNT')
    
    # =============================================================================
    # 缓存配置
    # =============================================================================
    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.getenv('REDIS_PORT', '6379'))
    REDIS_DB = int(os.getenv('REDIS_DB', '0'))
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', None)
    
    # 缓存TTL设置（分钟）
    CACHE_TTL = {
        'stock_quote': 1,      # 股票报价缓存1分钟
        'crypto_price': 1,     # 数字货币价格缓存1分钟
        'forex_rate': 5,       # 外汇汇率缓存5分钟
        'futures_price': 2,    # 期货价格缓存2分钟
        'indicators': 10,      # 技术指标缓存10分钟
        'historical_data': 60  # 历史数据缓存1小时
    }
    
    # =============================================================================
    # API状态检查
    # =============================================================================
    @classmethod
    def check_api_keys(cls):
        """检查API Key配置状态"""
        status = {
            'alpha_vantage': cls.ALPHA_VANTAGE_API_KEY != 'YOUR_ALPHA_VANTAGE_KEY_HERE',
            'coingecko': cls.COINGECKO_API_KEY != 'YOUR_COINGECKO_KEY_HERE',
            'exchangerate': cls.EXCHANGERATE_API_KEY != 'YOUR_EXCHANGERATE_KEY_HERE',
            'quandl': cls.QUANDL_API_KEY != 'YOUR_QUANDL_KEY_HERE'
        }
        return status
    
    @classmethod
    def get_missing_apis(cls):
        """获取未配置的API列表"""
        status = cls.check_api_keys()
        missing = [api for api, configured in status.items() if not configured]
        return missing
    
    @classmethod
    def is_production_ready(cls):
        """检查是否准备好生产环境"""
        missing = cls.get_missing_apis()
        # 至少需要股票和数字货币API
        required_apis = ['alpha_vantage', 'coingecko']
        return all(api not in missing for api in required_apis)

# =============================================================================
# 环境变量模板
# =============================================================================
ENV_TEMPLATE = """
# QuantTradeX API配置
# 请根据API申请教程获取相应的API Key

# 股票数据API - Alpha Vantage
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# 数字货币API - CoinGecko
COINGECKO_API_KEY=your_coingecko_key_here

# 外汇数据API - ExchangeRate-API
EXCHANGERATE_API_KEY=your_exchangerate_key_here

# 期货数据API - Quandl
QUANDL_API_KEY=your_quandl_key_here

# 实盘交易API - Interactive Brokers (可选)
IBKR_HOST=127.0.0.1
IBKR_PORT=7497
IBKR_CLIENT_ID=1
IBKR_ACCOUNT=your_ibkr_account

# Redis缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Flask应用配置
FLASK_SECRET_KEY=your_secret_key_here
FLASK_DEBUG=True
"""

def create_env_file():
    """创建.env文件模板"""
    if not os.path.exists('.env'):
        with open('.env', 'w') as f:
            f.write(ENV_TEMPLATE)
        print("✅ 已创建.env文件模板，请填入您的API Key")
    else:
        print("⚠️ .env文件已存在，请手动更新")

def validate_configuration():
    """验证配置完整性"""
    print("🔍 检查API配置状态...")
    
    status = APIConfig.check_api_keys()
    missing = APIConfig.get_missing_apis()
    
    print("\n📊 API配置状态:")
    for api, configured in status.items():
        status_icon = "✅" if configured else "❌"
        print(f"  {status_icon} {api.replace('_', ' ').title()}")
    
    if missing:
        print(f"\n⚠️ 缺少以下API配置: {', '.join(missing)}")
        print("📖 请参考 API申请教程.md 获取API Key")
    else:
        print("\n🎉 所有API配置完成！")
    
    if APIConfig.is_production_ready():
        print("✅ 平台已准备好基础功能运行")
    else:
        print("⚠️ 需要至少配置股票和数字货币API才能正常运行")

if __name__ == '__main__':
    # 创建环境变量文件模板
    create_env_file()
    
    # 验证配置
    validate_configuration()
    
    # 显示帮助信息
    print("\n📋 下一步操作:")
    print("1. 根据 API申请教程.md 申请API Key")
    print("2. 将API Key填入 .env 文件")
    print("3. 重新运行此脚本验证配置")
    print("4. 启动QuantTradeX应用")
