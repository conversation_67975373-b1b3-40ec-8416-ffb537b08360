# QuantTradeX API集成服务
# 统一管理所有第三方API调用

import requests
import time
import json
import redis
from datetime import datetime, timedelta
from functools import wraps
from api_config_template import APIConfig
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RateLimiter:
    """API频率限制器"""
    
    def __init__(self, calls_per_minute=60):
        self.calls_per_minute = calls_per_minute
        self.last_called = 0.0
    
    def __call__(self, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - self.last_called
            left_to_wait = 60.0 / self.calls_per_minute - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            result = func(*args, **kwargs)
            self.last_called = time.time()
            return result
        return wrapper

class DataCache:
    """数据缓存管理器"""
    
    def __init__(self):
        try:
            self.redis_client = redis.Redis(
                host=APIConfig.REDIS_HOST,
                port=APIConfig.REDIS_PORT,
                db=APIConfig.REDIS_DB,
                password=APIConfig.REDIS_PASSWORD,
                decode_responses=True
            )
            # 测试连接
            self.redis_client.ping()
            self.cache_enabled = True
            logger.info("✅ Redis缓存已启用")
        except Exception as e:
            logger.warning(f"⚠️ Redis连接失败，使用内存缓存: {e}")
            self.cache_enabled = False
            self.memory_cache = {}
    
    def get(self, key):
        """获取缓存数据"""
        try:
            if self.cache_enabled:
                data = self.redis_client.get(key)
                return json.loads(data) if data else None
            else:
                return self.memory_cache.get(key)
        except Exception as e:
            logger.error(f"缓存读取失败: {e}")
            return None
    
    def set(self, key, data, ttl_minutes=5):
        """设置缓存数据"""
        try:
            if self.cache_enabled:
                self.redis_client.setex(
                    key, 
                    timedelta(minutes=ttl_minutes), 
                    json.dumps(data)
                )
            else:
                # 简单的内存缓存，不考虑TTL
                self.memory_cache[key] = data
        except Exception as e:
            logger.error(f"缓存写入失败: {e}")

class APIService:
    """统一API服务类"""
    
    def __init__(self):
        self.cache = DataCache()
        self.session = self._create_session()
    
    def _create_session(self):
        """创建带重试机制的HTTP会话"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'QuantTradeX/1.0'
        })
        return session
    
    # =========================================================================
    # 股票数据API - Alpha Vantage
    # =========================================================================
    
    @RateLimiter(calls_per_minute=APIConfig.ALPHA_VANTAGE_RATE_LIMIT)
    def get_stock_quote(self, symbol):
        """获取股票实时报价"""
        cache_key = f"stock_quote:{symbol}"
        cached_data = self.cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            url = APIConfig.ALPHA_VANTAGE_BASE_URL
            params = {
                'function': 'GLOBAL_QUOTE',
                'symbol': symbol,
                'apikey': APIConfig.ALPHA_VANTAGE_API_KEY
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if 'Global Quote' in data:
                quote_data = data['Global Quote']
                result = {
                    'symbol': quote_data.get('01. symbol', symbol),
                    'price': float(quote_data.get('05. price', 0)),
                    'change': float(quote_data.get('09. change', 0)),
                    'change_percent': quote_data.get('10. change percent', '0%').replace('%', ''),
                    'volume': int(quote_data.get('06. volume', 0)),
                    'timestamp': datetime.now().isoformat()
                }
                
                # 缓存数据
                self.cache.set(cache_key, result, APIConfig.CACHE_TTL['stock_quote'])
                return result
            else:
                logger.error(f"Alpha Vantage API错误: {data}")
                return None
                
        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {e}")
            return None
    
    def search_stocks(self, keywords):
        """搜索股票"""
        try:
            url = APIConfig.ALPHA_VANTAGE_BASE_URL
            params = {
                'function': 'SYMBOL_SEARCH',
                'keywords': keywords,
                'apikey': APIConfig.ALPHA_VANTAGE_API_KEY
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if 'bestMatches' in data:
                results = []
                for match in data['bestMatches'][:10]:  # 限制返回10个结果
                    results.append({
                        'symbol': match.get('1. symbol'),
                        'name': match.get('2. name'),
                        'type': match.get('3. type'),
                        'region': match.get('4. region'),
                        'currency': match.get('8. currency')
                    })
                return results
            else:
                return []
                
        except Exception as e:
            logger.error(f"搜索股票失败 {keywords}: {e}")
            return []
    
    # =========================================================================
    # 数字货币API - CoinGecko
    # =========================================================================
    
    @RateLimiter(calls_per_minute=APIConfig.COINGECKO_RATE_LIMIT)
    def get_crypto_price(self, crypto_id):
        """获取数字货币价格"""
        cache_key = f"crypto_price:{crypto_id}"
        cached_data = self.cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            url = f"{APIConfig.COINGECKO_BASE_URL}/simple/price"
            params = {
                'ids': crypto_id,
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true',
                'include_market_cap': 'true'
            }
            
            if APIConfig.COINGECKO_API_KEY != 'YOUR_COINGECKO_KEY_HERE':
                params['x_cg_demo_api_key'] = APIConfig.COINGECKO_API_KEY
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if crypto_id in data:
                crypto_data = data[crypto_id]
                result = {
                    'id': crypto_id,
                    'price': crypto_data.get('usd', 0),
                    'change_24h': crypto_data.get('usd_24h_change', 0),
                    'volume_24h': crypto_data.get('usd_24h_vol', 0),
                    'market_cap': crypto_data.get('usd_market_cap', 0),
                    'timestamp': datetime.now().isoformat()
                }
                
                # 缓存数据
                self.cache.set(cache_key, result, APIConfig.CACHE_TTL['crypto_price'])
                return result
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取数字货币数据失败 {crypto_id}: {e}")
            return None
    
    def get_crypto_list(self):
        """获取支持的数字货币列表"""
        try:
            url = f"{APIConfig.COINGECKO_BASE_URL}/coins/markets"
            params = {
                'vs_currency': 'usd',
                'order': 'market_cap_desc',
                'per_page': 50,
                'page': 1
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            results = []
            for coin in data:
                results.append({
                    'id': coin.get('id'),
                    'symbol': coin.get('symbol', '').upper(),
                    'name': coin.get('name'),
                    'price': coin.get('current_price', 0),
                    'change_24h': coin.get('price_change_percentage_24h', 0),
                    'market_cap': coin.get('market_cap', 0),
                    'volume_24h': coin.get('total_volume', 0)
                })
            
            return results
            
        except Exception as e:
            logger.error(f"获取数字货币列表失败: {e}")
            return []
    
    # =========================================================================
    # 外汇API - ExchangeRate-API
    # =========================================================================
    
    def get_exchange_rate(self, base_currency='USD', target_currency='EUR'):
        """获取汇率"""
        cache_key = f"forex_rate:{base_currency}_{target_currency}"
        cached_data = self.cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            url = f"{APIConfig.EXCHANGERATE_BASE_URL}/{APIConfig.EXCHANGERATE_API_KEY}/pair/{base_currency}/{target_currency}"
            
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('result') == 'success':
                result = {
                    'base_currency': base_currency,
                    'target_currency': target_currency,
                    'rate': data.get('conversion_rate', 0),
                    'timestamp': datetime.now().isoformat()
                }
                
                # 缓存数据
                self.cache.set(cache_key, result, APIConfig.CACHE_TTL['forex_rate'])
                return result
            else:
                logger.error(f"汇率API错误: {data}")
                return None
                
        except Exception as e:
            logger.error(f"获取汇率失败 {base_currency}/{target_currency}: {e}")
            return None
    
    # =========================================================================
    # 技术指标计算
    # =========================================================================
    
    def calculate_technical_indicators(self, symbol, data_type='stock'):
        """计算技术指标"""
        cache_key = f"indicators:{data_type}:{symbol}"
        cached_data = self.cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        # 这里应该集成真实的技术指标计算
        # 目前返回模拟数据
        import random
        
        result = {
            'symbol': symbol,
            'sma_20': round(random.uniform(100, 200), 2),
            'sma_50': round(random.uniform(90, 190), 2),
            'rsi_14': round(random.uniform(30, 70), 2),
            'macd': round(random.uniform(-2, 2), 4),
            'bollinger_upper': round(random.uniform(180, 220), 2),
            'bollinger_lower': round(random.uniform(80, 120), 2),
            'timestamp': datetime.now().isoformat()
        }
        
        # 缓存数据
        self.cache.set(cache_key, result, APIConfig.CACHE_TTL['indicators'])
        return result
    
    # =========================================================================
    # 健康检查
    # =========================================================================
    
    def health_check(self):
        """API服务健康检查"""
        status = {
            'timestamp': datetime.now().isoformat(),
            'cache_enabled': self.cache.cache_enabled,
            'apis': {}
        }
        
        # 检查各API配置状态
        api_status = APIConfig.check_api_keys()
        for api, configured in api_status.items():
            status['apis'][api] = {
                'configured': configured,
                'status': 'ready' if configured else 'not_configured'
            }
        
        return status

# 创建全局API服务实例
api_service = APIService()

if __name__ == '__main__':
    # 测试API服务
    print("🔍 测试API服务...")
    
    # 健康检查
    health = api_service.health_check()
    print(f"📊 服务状态: {json.dumps(health, indent=2)}")
    
    # 测试股票数据（如果配置了API Key）
    if health['apis']['alpha_vantage']['configured']:
        print("\n📈 测试股票数据...")
        stock_data = api_service.get_stock_quote('AAPL')
        if stock_data:
            print(f"✅ AAPL数据: ${stock_data['price']}")
        else:
            print("❌ 股票数据获取失败")
    
    # 测试数字货币数据
    if health['apis']['coingecko']['configured']:
        print("\n🪙 测试数字货币数据...")
        crypto_data = api_service.get_crypto_price('bitcoin')
        if crypto_data:
            print(f"✅ Bitcoin价格: ${crypto_data['price']}")
        else:
            print("❌ 数字货币数据获取失败")
